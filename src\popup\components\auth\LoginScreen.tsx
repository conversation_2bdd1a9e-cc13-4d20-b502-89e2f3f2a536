import React, { useState } from 'react';
import {
    <PERSON>,
    Card,
    CardContent,
    TextField,
    Button,
    Typography,
    FormControlLabel,
    Checkbox,
    Alert,
    CircularProgress,
    InputAdornment,
    IconButton
} from '@mui/material';
import {
    LockOutlined as LockIcon,
    Visibility,
    VisibilityOff,
    Email as EmailIcon,
    Lock as PasswordIcon
} from '@mui/icons-material';
import { useNavigate } from '@tanstack/react-router';
import { useAuth } from '@shared/hooks/useAuth';

const LoginScreen: React.FC = () => {
    const navigate = useNavigate();
    const { login, loading, error } = useAuth();
    
    const [formData, setFormData] = useState({
        email: '',
        password: '',
        rememberMe: false
    });
    
    const [showPassword, setShowPassword] = useState(false);
    const [formErrors, setFormErrors] = useState<{
        email?: string;
        password?: string;
    }>({});

    const validateForm = () => {
        const errors: { email?: string; password?: string } = {};
        
        if (!formData.email) {
            errors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            errors.email = 'Email is invalid';
        }
        
        if (!formData.password) {
            errors.password = 'Password is required';
        } else if (formData.password.length < 6) {
            errors.password = 'Password must be at least 6 characters';
        }
        
        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        const result = await login({
            email: formData.email,
            password: formData.password,
            rememberMe: formData.rememberMe
        });

        if (result.success) {
            navigate({ to: '/home-page' });
        }
    };

    const handleInputChange = (field: keyof typeof formData) => (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        const value = field === 'rememberMe' ? e.target.checked : e.target.value;
        setFormData(prev => ({ ...prev, [field]: value }));
        
        // Clear field error when user starts typing
        if (formErrors[field as keyof typeof formErrors]) {
            setFormErrors(prev => ({ ...prev, [field]: undefined }));
        }
    };

    return (
        <Box
            sx={{
                width: 350,
                minHeight: 500,
                display: 'flex',
                flexDirection: 'column',
                p: 2
            }}
        >
            <Card
                sx={{
                    flex: 1,
                    boxShadow: 1,
                    borderRadius: 2
                }}
            >
                <CardContent sx={{ p: 3 }}>
                    <Box
                        sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            mb: 2
                        }}
                    >
                        <Box
                            sx={{
                                bgcolor: 'primary.main',
                                borderRadius: '50%',
                                width: 40,
                                height: 40,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                mb: 1
                            }}
                        >
                            <LockIcon sx={{ color: 'white', fontSize: 20 }} />
                        </Box>
                        <Typography component="h1" variant="h6" sx={{ fontWeight: 600 }}>
                            Sign In
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                            Access your extension features
                        </Typography>
                    </Box>

                    {error && (
                        <Alert severity="error" sx={{ mb: 2, fontSize: '0.8rem' }}>
                            {error}
                        </Alert>
                    )}

                    <Box component="form" onSubmit={handleSubmit}>
                        <TextField
                            margin="normal"
                            required
                            fullWidth
                            id="email"
                            label="Email"
                            name="email"
                            autoComplete="email"
                            autoFocus
                            size="small"
                            value={formData.email}
                            onChange={handleInputChange('email')}
                            error={!!formErrors.email}
                            helperText={formErrors.email}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <EmailIcon color="action" fontSize="small" />
                                    </InputAdornment>
                                )
                            }}
                        />
                        <TextField
                            margin="normal"
                            required
                            fullWidth
                            name="password"
                            label="Password"
                            type={showPassword ? 'text' : 'password'}
                            id="password"
                            autoComplete="current-password"
                            size="small"
                            value={formData.password}
                            onChange={handleInputChange('password')}
                            error={!!formErrors.password}
                            helperText={formErrors.password}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <PasswordIcon color="action" fontSize="small" />
                                    </InputAdornment>
                                ),
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton
                                            aria-label="toggle password visibility"
                                            onClick={() => setShowPassword(!showPassword)}
                                            edge="end"
                                            size="small"
                                        >
                                            {showPassword ? <VisibilityOff fontSize="small" /> : <Visibility fontSize="small" />}
                                        </IconButton>
                                    </InputAdornment>
                                )
                            }}
                        />
                        <FormControlLabel
                            control={
                                <Checkbox
                                    value="remember"
                                    color="primary"
                                    size="small"
                                    checked={formData.rememberMe}
                                    onChange={handleInputChange('rememberMe')}
                                />
                            }
                            label={<Typography variant="body2">Remember me</Typography>}
                            sx={{ mt: 1 }}
                        />
                        <Button
                            type="submit"
                            fullWidth
                            variant="contained"
                            disabled={loading}
                            sx={{
                                mt: 2,
                                mb: 2,
                                py: 1,
                                fontSize: '0.9rem',
                                fontWeight: 600
                            }}
                        >
                            {loading ? (
                                <CircularProgress size={20} color="inherit" />
                            ) : (
                                'Sign In'
                            )}
                        </Button>
                    </Box>

                    <Box sx={{ mt: 2, textAlign: 'center' }}>
                        <Typography variant="caption" color="text.secondary" display="block">
                            Demo Credentials:
                        </Typography>
                        <Typography variant="caption" color="text.secondary" display="block">
                            <EMAIL> / admin123
                        </Typography>
                        <Typography variant="caption" color="text.secondary" display="block">
                            <EMAIL> / user123
                        </Typography>
                    </Box>
                </CardContent>
            </Card>
        </Box>
    );
};

export default LoginScreen;

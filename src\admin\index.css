/* Admin Dashboard Global Styles */
* {
    box-sizing: border-box;
}

html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen', 'Ubuntu',
        'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f5f5f5;
}

#admin-root {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading Animation */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

/* Custom Material-UI Overrides */
.MuiDrawer-paper {
    border-right: 1px solid rgba(0, 0, 0, 0.12);
}

.MuiAppBar-root {
    box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12);
}

/* Dashboard Cards */
.dashboard-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0px 4px 8px -2px rgba(0,0,0,0.2), 0px 8px 10px 1px rgba(0,0,0,0.14), 0px 3px 14px 2px rgba(0,0,0,0.12);
}

/* Responsive Design */
@media (max-width: 768px) {
    .MuiDrawer-paper {
        width: 240px;
    }
}

@media (max-width: 480px) {
    .MuiDrawer-paper {
        width: 200px;
    }
}

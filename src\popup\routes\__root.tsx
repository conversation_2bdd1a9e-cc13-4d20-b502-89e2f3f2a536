import { useEffect } from 'react';

import { Outlet, createRootRoute, useNavigate, useLocation } from '@tanstack/react-router';
import ProtectedRoute from '../components/auth/ProtectedRoute';
import { useAuth } from '@shared/hooks/useAuth';

function NotFound() {
    const navigate = useNavigate();
    const { isAuthenticated } = useAuth();

    useEffect(() => {
        if (isAuthenticated) {
            navigate({
                to: '/home-page'
            });
        } else {
            navigate({
                to: '/login'
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAuthenticated]);

    return null;
}

function Layout() {
    const location = useLocation();
    const isLoginPage = location.pathname === '/login';

    // Don't wrap login page with ProtectedRoute
    if (isLoginPage) {
        return <Outlet />;
    }

    return (
        <ProtectedRoute>
            <Outlet />
        </ProtectedRoute>
    );
}

export const Route = createRootRoute({
    component: Layout,
    notFoundComponent: NotFound
});

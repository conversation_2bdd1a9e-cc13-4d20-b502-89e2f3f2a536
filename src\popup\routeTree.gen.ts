/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

import { Route as rootRouteImport } from './routes/__root'

const ProductsIndexLazyRouteImport = createFileRoute('/products/')()
const LoginIndexLazyRouteImport = createFileRoute('/login/')()
const HomePageIndexLazyRouteImport = createFileRoute('/home-page/')()

const ProductsIndexLazyRoute = ProductsIndexLazyRouteImport.update({
  id: '/products/',
  path: '/products/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() =>
  import('./routes/products/index.lazy').then((d) => d.Route),
)
const LoginIndexLazyRoute = LoginIndexLazyRouteImport.update({
  id: '/login/',
  path: '/login/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() => import('./routes/login/index.lazy').then((d) => d.Route))
const HomePageIndexLazyRoute = HomePageIndexLazyRouteImport.update({
  id: '/home-page/',
  path: '/home-page/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() =>
  import('./routes/home-page/index.lazy').then((d) => d.Route),
)

export interface FileRoutesByFullPath {
  '/home-page': typeof HomePageIndexLazyRoute
  '/login': typeof LoginIndexLazyRoute
  '/products': typeof ProductsIndexLazyRoute
}
export interface FileRoutesByTo {
  '/home-page': typeof HomePageIndexLazyRoute
  '/login': typeof LoginIndexLazyRoute
  '/products': typeof ProductsIndexLazyRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/home-page/': typeof HomePageIndexLazyRoute
  '/login/': typeof LoginIndexLazyRoute
  '/products/': typeof ProductsIndexLazyRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/home-page' | '/login' | '/products'
  fileRoutesByTo: FileRoutesByTo
  to: '/home-page' | '/login' | '/products'
  id: '__root__' | '/home-page/' | '/login/' | '/products/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  HomePageIndexLazyRoute: typeof HomePageIndexLazyRoute
  LoginIndexLazyRoute: typeof LoginIndexLazyRoute
  ProductsIndexLazyRoute: typeof ProductsIndexLazyRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/products/': {
      id: '/products/'
      path: '/products'
      fullPath: '/products'
      preLoaderRoute: typeof ProductsIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login/': {
      id: '/login/'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/home-page/': {
      id: '/home-page/'
      path: '/home-page'
      fullPath: '/home-page'
      preLoaderRoute: typeof HomePageIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  HomePageIndexLazyRoute: HomePageIndexLazyRoute,
  LoginIndexLazyRoute: LoginIndexLazyRoute,
  ProductsIndexLazyRoute: ProductsIndexLazyRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

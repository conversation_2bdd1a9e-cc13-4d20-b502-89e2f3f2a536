import { AuthResponse, AuthState, LoginCredentials, RegisterCredentials, User } from '../types';

class AuthService {
    private readonly STORAGE_KEYS = {
        TOKEN: 'auth_token',
        USER: 'auth_user',
        REMEMBER_ME: 'auth_remember_me'
    };

    private authState: AuthState = {
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: null
    };

    private listeners: Array<(state: AuthState) => void> = [];

    constructor() {
        this.initializeAuth();
    }

    /**
     * Initialize authentication state from storage
     */
    private async initializeAuth(): Promise<void> {
        try {
            const token = await this.getStoredToken();
            const user = await this.getStoredUser();

            if (token && user) {
                this.authState = {
                    ...this.authState,
                    isAuthenticated: true,
                    user,
                    token
                };
                this.notifyListeners();
            }
        } catch (error) {
            console.error('Failed to initialize auth:', error);
        }
    }

    /**
     * Login with email and password
     */
    async login(credentials: LoginCredentials): Promise<AuthResponse> {
        this.setLoading(true);
        this.setError(null);

        try {
            // Simulate API call - replace with actual authentication
            const response = await this.mockLogin(credentials);

            if (response.success && response.user && response.token) {
                await this.setAuthData(response.user, response.token, credentials.rememberMe);
                
                this.authState = {
                    ...this.authState,
                    isAuthenticated: true,
                    user: response.user,
                    token: response.token,
                    loading: false,
                    error: null
                };
                
                this.notifyListeners();
                return response;
            } else {
                this.setError(response.error || 'Login failed');
                return response;
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Login failed';
            this.setError(errorMessage);
            return {
                success: false,
                error: errorMessage
            };
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Register new user
     */
    async register(credentials: RegisterCredentials): Promise<AuthResponse> {
        this.setLoading(true);
        this.setError(null);

        try {
            // Simulate API call - replace with actual registration
            const response = await this.mockRegister(credentials);

            if (response.success && response.user && response.token) {
                await this.setAuthData(response.user, response.token, false);

                this.authState = {
                    ...this.authState,
                    isAuthenticated: true,
                    user: response.user,
                    token: response.token,
                    loading: false,
                    error: null
                };

                this.notifyListeners();
                return response;
            } else {
                this.setError(response.error || 'Registration failed');
                return response;
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Registration failed';
            this.setError(errorMessage);
            return {
                success: false,
                error: errorMessage
            };
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Logout user
     */
    async logout(): Promise<void> {
        try {
            await this.clearAuthData();

            this.authState = {
                isAuthenticated: false,
                user: null,
                token: null,
                loading: false,
                error: null
            };

            this.notifyListeners();
        } catch (error) {
            console.error('Logout error:', error);
        }
    }

    /**
     * Get current authentication state
     */
    getAuthState(): AuthState {
        return { ...this.authState };
    }

    /**
     * Subscribe to auth state changes
     */
    subscribe(listener: (state: AuthState) => void): () => void {
        this.listeners.push(listener);
        
        // Return unsubscribe function
        return () => {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated(): boolean {
        return this.authState.isAuthenticated;
    }

    /**
     * Get current user
     */
    getCurrentUser(): User | null {
        return this.authState.user;
    }

    /**
     * Mock login function - replace with actual API call
     */
    private async mockLogin(credentials: LoginCredentials): Promise<AuthResponse> {
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock users for demo
        const mockUsers: Record<string, { password: string; user: User }> = {
            'admin': {
                password: 'admin',
                user: {
                    id: '1',
                    email: '<EMAIL>',
                    name: 'Admin User',
                    role: 'admin',
                    createdAt: new Date().toISOString(),
                    lastLogin: new Date().toISOString()
                }
            },
            'user': {
                password: 'user',
                user: {
                    id: '2',
                    email: '<EMAIL>',
                    name: 'Regular User',
                    role: 'user',
                    createdAt: new Date().toISOString(),
                    lastLogin: new Date().toISOString()
                }
            }
        };

        const mockUser = mockUsers[credentials.email];

        if (!mockUser) {
            return {
                success: false,
                error: 'User not found'
            };
        }

        if (mockUser.password !== credentials.password) {
            return {
                success: false,
                error: 'Invalid password'
            };
        }

        return {
            success: true,
            user: mockUser.user,
            token: `mock_token_${Date.now()}`
        };
    }

    /**
     * Mock register function - replace with actual API call
     */
    private async mockRegister(credentials: RegisterCredentials): Promise<AuthResponse> {
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Basic validation
        if (!credentials.email || !credentials.firstName || !credentials.lastName || !credentials.username || !credentials.password) {
            return {
                success: false,
                error: 'Missing required fields'
            };
        }

        if (credentials.password !== credentials.retypePassword) {
            return {
                success: false,
                error: 'Passwords do not match'
            };
        }

        if (credentials.password.length < 6) {
            return {
                success: false,
                error: 'Password must be at least 6 characters long'
            };
        }

        if (!credentials.agreeToTerms) {
            return {
                success: false,
                error: 'You must agree to the terms and conditions'
            };
        }

        // Check if user already exists (simulate)
        if (credentials.email === '<EMAIL>' || credentials.email === '<EMAIL>') {
            return {
                success: false,
                error: 'User with this email already exists'
            };
        }

        // Create new user
        const newUser: User = {
            id: `user_${Date.now()}`,
            email: credentials.email,
            name: `${credentials.firstName} ${credentials.lastName}`,
            role: 'user',
            createdAt: new Date().toISOString(),
            lastLogin: new Date().toISOString()
        };

        return {
            success: true,
            user: newUser,
            token: `mock_token_${Date.now()}`
        };
    }

    /**
     * Store authentication data
     */
    private async setAuthData(user: User, token: string, rememberMe?: boolean): Promise<void> {
        const storage = rememberMe ? chrome.storage.local : chrome.storage.session;
        
        await storage.set({
            [this.STORAGE_KEYS.TOKEN]: token,
            [this.STORAGE_KEYS.USER]: user,
            [this.STORAGE_KEYS.REMEMBER_ME]: rememberMe || false
        });
    }

    /**
     * Clear authentication data
     */
    private async clearAuthData(): Promise<void> {
        await chrome.storage.local.remove([
            this.STORAGE_KEYS.TOKEN,
            this.STORAGE_KEYS.USER,
            this.STORAGE_KEYS.REMEMBER_ME
        ]);
        
        await chrome.storage.session.remove([
            this.STORAGE_KEYS.TOKEN,
            this.STORAGE_KEYS.USER,
            this.STORAGE_KEYS.REMEMBER_ME
        ]);
    }

    /**
     * Get stored token
     */
    private async getStoredToken(): Promise<string | null> {
        const [local, session] = await Promise.all([
            chrome.storage.local.get(this.STORAGE_KEYS.TOKEN),
            chrome.storage.session.get(this.STORAGE_KEYS.TOKEN)
        ]);
        
        return local[this.STORAGE_KEYS.TOKEN] || session[this.STORAGE_KEYS.TOKEN] || null;
    }

    /**
     * Get stored user
     */
    private async getStoredUser(): Promise<User | null> {
        const [local, session] = await Promise.all([
            chrome.storage.local.get(this.STORAGE_KEYS.USER),
            chrome.storage.session.get(this.STORAGE_KEYS.USER)
        ]);
        
        return local[this.STORAGE_KEYS.USER] || session[this.STORAGE_KEYS.USER] || null;
    }

    /**
     * Set loading state
     */
    private setLoading(loading: boolean): void {
        this.authState = { ...this.authState, loading };
        this.notifyListeners();
    }

    /**
     * Set error state
     */
    private setError(error: string | null): void {
        this.authState = { ...this.authState, error };
        this.notifyListeners();
    }

    /**
     * Notify all listeners of state changes
     */
    private notifyListeners(): void {
        this.listeners.forEach(listener => listener(this.authState));
    }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;

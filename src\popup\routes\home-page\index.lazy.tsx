import { ReactElement, useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';

import { But<PERSON>, Divider, Stack } from '@mui/material';
import Box from '@mui/material/Box';
import { Link, createLazyFileRoute } from '@tanstack/react-router';

import { ChromeApiWrapper, ChromeMessage, ChromeMessageType } from '@/common/chrome-api-wrapper';
import { ScraperCommand, ScraperMessage } from '@/common/types/scraper';
import PopupContent from '@/popup/modules/core/components/PopupContent/PopupContent';
import PopupHeader from '@/popup/modules/core/components/PopupHeader/PopupHeader';

const CACHE_KEY = 'scrapedPageTitle';

function HomePage(): ReactElement {
    const { enqueueSnackbar } = useSnackbar();

    const [scrapedPageTitle, setScrapedPageTitle] = useState<string>('');
    const [disableScrapeButton, setDisableScrapeButton] = useState<boolean>(false);

    async function openAdminDashboard() {
        try {
            await chrome.runtime.openOptionsPage();
        } catch (error) {
            console.error('Failed to open admin dashboard:', error);
            enqueueSnackbar('Failed to open admin dashboard', {
                variant: 'error'
            });
        }
    }

    async function scrape() {
        setDisableScrapeButton(true);

        const message: ChromeMessage<ScraperMessage> = {
            type: ChromeMessageType.SCRAPER_COMMAND,
            payload: { command: ScraperCommand.SCRAPE }
        };

        try {
            await ChromeApiWrapper.sendTabMessage(message);
        } catch (e) {
            console.error(e);
            enqueueSnackbar('Failed to scrape page title. Please check console logs.', {
                variant: 'error'
            });

            setDisableScrapeButton(false);
        }
    }

    useEffect(() => {
        chrome.storage.session.get(CACHE_KEY).then(items => {
            const cachedTitle = items[CACHE_KEY];
            setScrapedPageTitle(cachedTitle ?? '');
        });

        chrome.runtime.onMessage.addListener((message: ChromeMessage<string>) => {
            if (message.type !== ChromeMessageType.SCRAPING_RESULTS) {
                return false;
            }

            chrome.storage.session.set({ [CACHE_KEY]: message.payload });
            setScrapedPageTitle(message.payload);
            setDisableScrapeButton(false);
            return false;
        });
    }, []);

    return (
        <>
            <PopupHeader />
            <PopupContent>
                <Stack alignItems="center" spacing={1}>
                    <Box alignItems="center">
                        <h1>My Chromium extension</h1>
                    </Box>

                    <p>
                        <strong>Scraped title:</strong> {scrapedPageTitle}
                    </p>

                    <Button
                        className="scrape-button"
                        variant="contained"
                        disabled={disableScrapeButton}
                        onClick={scrape}
                    >
                        Scrape page title
                    </Button>

                    <Divider sx={{ width: '100%', my: 2 }} />

                    <Button
                        component={Link}
                        to="/products"
                        variant="contained"
                        color="secondary"
                        sx={{ width: '100%' }}
                    >
                        View Products
                    </Button>

                    <Button
                        variant="outlined"
                        color="primary"
                        onClick={openAdminDashboard}
                        sx={{ width: '100%' }}
                    >
                        Open Admin Dashboard
                    </Button>
                </Stack>
            </PopupContent>
        </>
    );
}

export const Route = createLazyFileRoute('/home-page/')({
    component: HomePage
});

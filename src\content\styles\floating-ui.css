/* Floating Icon Styles */
#chrome-extension-floating-icon {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    width: 60px !important;
    height: 60px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    z-index: 10000 !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    user-select: none !important;
    border: 3px solid rgba(255, 255, 255, 0.2) !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

#chrome-extension-floating-icon:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4) !important;
}

/* Floating Menu Styles */
#chrome-extension-floating-menu {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 10001 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    backdrop-filter: blur(5px) !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

#chrome-extension-floating-menu > div {
    background: white !important;
    border-radius: 16px !important;
    padding: 24px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    min-width: 320px !important;
    max-width: 400px !important;
    position: relative !important;
}

/* Animation keyframes */
@keyframes pulse {
    0% {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

/* Reset styles for extension elements */
#chrome-extension-floating-icon *,
#chrome-extension-floating-menu * {
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 0 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Ensure buttons and inputs have proper styling */
#chrome-extension-floating-menu button {
    background: #4CAF50 !important;
    color: white !important;
    border: none !important;
    padding: 12px 16px !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

#chrome-extension-floating-menu button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

#chrome-extension-floating-menu input {
    width: 100% !important;
    padding: 12px !important;
    border: 2px solid #e0e0e0 !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    transition: border-color 0.2s ease !important;
    box-sizing: border-box !important;
}

#chrome-extension-floating-menu input:focus {
    border-color: #667eea !important;
    outline: none !important;
}

#chrome-extension-floating-menu h2 {
    margin: 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #333 !important;
}

#chrome-extension-floating-menu label {
    display: block !important;
    margin-bottom: 8px !important;
    font-weight: 500 !important;
    color: #555 !important;
    font-size: 14px !important;
}

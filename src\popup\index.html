<!doctype html>

<!-- Chrome Extension language is not determined by language set in HTML tag -->
<!--suppress HtmlRequiredLangAttribute -->
<html>
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta http-equiv="X-UA-Compatible" content="ie=edge" />
        <title>Chrome Extension React</title>

        <link rel="stylesheet" href="index.css" />
    </head>
    <body>
        <div id="root"></div>
        <script type="module" src="index.tsx"></script>
    </body>
</html>

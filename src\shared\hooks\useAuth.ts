import { useEffect, useState } from 'react';
import { AuthState, LoginCredentials, RegisterCredentials } from '../types';
import { authService } from '../services/authService';

export interface UseAuthReturn extends AuthState {
    login: (credentials: LoginCredentials) => Promise<{ success: boolean; error?: string }>;
    register: (credentials: RegisterCredentials) => Promise<{ success: boolean; error?: string }>;
    logout: () => Promise<void>;
    clearError: () => void;
}

/**
 * Custom hook for authentication
 */
export const useAuth = (): UseAuthReturn => {
    const [authState, setAuthState] = useState<AuthState>(authService.getAuthState());

    useEffect(() => {
        // Subscribe to auth state changes
        const unsubscribe = authService.subscribe(setAuthState);
        
        // Cleanup subscription on unmount
        return unsubscribe;
    }, []);

    const login = async (credentials: LoginCredentials) => {
        const response = await authService.login(credentials);
        return {
            success: response.success,
            error: response.error
        };
    };

    const register = async (credentials: RegisterCredentials) => {
        const response = await authService.register(credentials);
        return {
            success: response.success,
            error: response.error
        };
    };

    const logout = async () => {
        await authService.logout();
    };

    const clearError = () => {
        // This would need to be implemented in authService if needed
        // For now, we'll just return the current state
    };

    return {
        ...authState,
        login,
        register,
        logout,
        clearError
    };
};

export default useAuth;

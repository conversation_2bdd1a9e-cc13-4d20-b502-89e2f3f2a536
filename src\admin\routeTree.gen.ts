/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

import { Route as rootRouteImport } from './routes/__root'

const WordMapperIndexLazyRouteImport = createFileRoute('/word-mapper/')()
const SignupIndexLazyRouteImport = createFileRoute('/signup/')()
const SettingsIndexLazyRouteImport = createFileRoute('/settings/')()
const ServicesIndexLazyRouteImport = createFileRoute('/services/')()
const ProductsIndexLazyRouteImport = createFileRoute('/products/')()
const OcrIndexLazyRouteImport = createFileRoute('/ocr/')()
const LoginIndexLazyRouteImport = createFileRoute('/login/')()
const InfoIndexLazyRouteImport = createFileRoute('/info/')()
const FormsIndexLazyRouteImport = createFileRoute('/forms/')()
const ExtensionIndexLazyRouteImport = createFileRoute('/extension/')()
const DashboardIndexLazyRouteImport = createFileRoute('/dashboard/')()
const AnalyticsIndexLazyRouteImport = createFileRoute('/analytics/')()
const AdminProceduresIndexLazyRouteImport =
  createFileRoute('/admin-procedures/')()

const WordMapperIndexLazyRoute = WordMapperIndexLazyRouteImport.update({
  id: '/word-mapper/',
  path: '/word-mapper/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() =>
  import('./routes/word-mapper/index.lazy').then((d) => d.Route),
)
const SignupIndexLazyRoute = SignupIndexLazyRouteImport.update({
  id: '/signup/',
  path: '/signup/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() => import('./routes/signup/index.lazy').then((d) => d.Route))
const SettingsIndexLazyRoute = SettingsIndexLazyRouteImport.update({
  id: '/settings/',
  path: '/settings/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() =>
  import('./routes/settings/index.lazy').then((d) => d.Route),
)
const ServicesIndexLazyRoute = ServicesIndexLazyRouteImport.update({
  id: '/services/',
  path: '/services/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() =>
  import('./routes/services/index.lazy').then((d) => d.Route),
)
const ProductsIndexLazyRoute = ProductsIndexLazyRouteImport.update({
  id: '/products/',
  path: '/products/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() =>
  import('./routes/products/index.lazy').then((d) => d.Route),
)
const OcrIndexLazyRoute = OcrIndexLazyRouteImport.update({
  id: '/ocr/',
  path: '/ocr/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() => import('./routes/ocr/index.lazy').then((d) => d.Route))
const LoginIndexLazyRoute = LoginIndexLazyRouteImport.update({
  id: '/login/',
  path: '/login/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() => import('./routes/login/index.lazy').then((d) => d.Route))
const InfoIndexLazyRoute = InfoIndexLazyRouteImport.update({
  id: '/info/',
  path: '/info/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() => import('./routes/info/index.lazy').then((d) => d.Route))
const FormsIndexLazyRoute = FormsIndexLazyRouteImport.update({
  id: '/forms/',
  path: '/forms/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() => import('./routes/forms/index.lazy').then((d) => d.Route))
const ExtensionIndexLazyRoute = ExtensionIndexLazyRouteImport.update({
  id: '/extension/',
  path: '/extension/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() =>
  import('./routes/extension/index.lazy').then((d) => d.Route),
)
const DashboardIndexLazyRoute = DashboardIndexLazyRouteImport.update({
  id: '/dashboard/',
  path: '/dashboard/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() =>
  import('./routes/dashboard/index.lazy').then((d) => d.Route),
)
const AnalyticsIndexLazyRoute = AnalyticsIndexLazyRouteImport.update({
  id: '/analytics/',
  path: '/analytics/',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() =>
  import('./routes/analytics/index.lazy').then((d) => d.Route),
)
const AdminProceduresIndexLazyRoute =
  AdminProceduresIndexLazyRouteImport.update({
    id: '/admin-procedures/',
    path: '/admin-procedures/',
    getParentRoute: () => rootRouteImport,
  } as any).lazy(() =>
    import('./routes/admin-procedures/index.lazy').then((d) => d.Route),
  )

export interface FileRoutesByFullPath {
  '/admin-procedures': typeof AdminProceduresIndexLazyRoute
  '/analytics': typeof AnalyticsIndexLazyRoute
  '/dashboard': typeof DashboardIndexLazyRoute
  '/extension': typeof ExtensionIndexLazyRoute
  '/forms': typeof FormsIndexLazyRoute
  '/info': typeof InfoIndexLazyRoute
  '/login': typeof LoginIndexLazyRoute
  '/ocr': typeof OcrIndexLazyRoute
  '/products': typeof ProductsIndexLazyRoute
  '/services': typeof ServicesIndexLazyRoute
  '/settings': typeof SettingsIndexLazyRoute
  '/signup': typeof SignupIndexLazyRoute
  '/word-mapper': typeof WordMapperIndexLazyRoute
}
export interface FileRoutesByTo {
  '/admin-procedures': typeof AdminProceduresIndexLazyRoute
  '/analytics': typeof AnalyticsIndexLazyRoute
  '/dashboard': typeof DashboardIndexLazyRoute
  '/extension': typeof ExtensionIndexLazyRoute
  '/forms': typeof FormsIndexLazyRoute
  '/info': typeof InfoIndexLazyRoute
  '/login': typeof LoginIndexLazyRoute
  '/ocr': typeof OcrIndexLazyRoute
  '/products': typeof ProductsIndexLazyRoute
  '/services': typeof ServicesIndexLazyRoute
  '/settings': typeof SettingsIndexLazyRoute
  '/signup': typeof SignupIndexLazyRoute
  '/word-mapper': typeof WordMapperIndexLazyRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/admin-procedures/': typeof AdminProceduresIndexLazyRoute
  '/analytics/': typeof AnalyticsIndexLazyRoute
  '/dashboard/': typeof DashboardIndexLazyRoute
  '/extension/': typeof ExtensionIndexLazyRoute
  '/forms/': typeof FormsIndexLazyRoute
  '/info/': typeof InfoIndexLazyRoute
  '/login/': typeof LoginIndexLazyRoute
  '/ocr/': typeof OcrIndexLazyRoute
  '/products/': typeof ProductsIndexLazyRoute
  '/services/': typeof ServicesIndexLazyRoute
  '/settings/': typeof SettingsIndexLazyRoute
  '/signup/': typeof SignupIndexLazyRoute
  '/word-mapper/': typeof WordMapperIndexLazyRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/admin-procedures'
    | '/analytics'
    | '/dashboard'
    | '/extension'
    | '/forms'
    | '/info'
    | '/login'
    | '/ocr'
    | '/products'
    | '/services'
    | '/settings'
    | '/signup'
    | '/word-mapper'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/admin-procedures'
    | '/analytics'
    | '/dashboard'
    | '/extension'
    | '/forms'
    | '/info'
    | '/login'
    | '/ocr'
    | '/products'
    | '/services'
    | '/settings'
    | '/signup'
    | '/word-mapper'
  id:
    | '__root__'
    | '/admin-procedures/'
    | '/analytics/'
    | '/dashboard/'
    | '/extension/'
    | '/forms/'
    | '/info/'
    | '/login/'
    | '/ocr/'
    | '/products/'
    | '/services/'
    | '/settings/'
    | '/signup/'
    | '/word-mapper/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AdminProceduresIndexLazyRoute: typeof AdminProceduresIndexLazyRoute
  AnalyticsIndexLazyRoute: typeof AnalyticsIndexLazyRoute
  DashboardIndexLazyRoute: typeof DashboardIndexLazyRoute
  ExtensionIndexLazyRoute: typeof ExtensionIndexLazyRoute
  FormsIndexLazyRoute: typeof FormsIndexLazyRoute
  InfoIndexLazyRoute: typeof InfoIndexLazyRoute
  LoginIndexLazyRoute: typeof LoginIndexLazyRoute
  OcrIndexLazyRoute: typeof OcrIndexLazyRoute
  ProductsIndexLazyRoute: typeof ProductsIndexLazyRoute
  ServicesIndexLazyRoute: typeof ServicesIndexLazyRoute
  SettingsIndexLazyRoute: typeof SettingsIndexLazyRoute
  SignupIndexLazyRoute: typeof SignupIndexLazyRoute
  WordMapperIndexLazyRoute: typeof WordMapperIndexLazyRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/word-mapper/': {
      id: '/word-mapper/'
      path: '/word-mapper'
      fullPath: '/word-mapper'
      preLoaderRoute: typeof WordMapperIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/signup/': {
      id: '/signup/'
      path: '/signup'
      fullPath: '/signup'
      preLoaderRoute: typeof SignupIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/settings/': {
      id: '/settings/'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/services/': {
      id: '/services/'
      path: '/services'
      fullPath: '/services'
      preLoaderRoute: typeof ServicesIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/products/': {
      id: '/products/'
      path: '/products'
      fullPath: '/products'
      preLoaderRoute: typeof ProductsIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/ocr/': {
      id: '/ocr/'
      path: '/ocr'
      fullPath: '/ocr'
      preLoaderRoute: typeof OcrIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login/': {
      id: '/login/'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/info/': {
      id: '/info/'
      path: '/info'
      fullPath: '/info'
      preLoaderRoute: typeof InfoIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/forms/': {
      id: '/forms/'
      path: '/forms'
      fullPath: '/forms'
      preLoaderRoute: typeof FormsIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/extension/': {
      id: '/extension/'
      path: '/extension'
      fullPath: '/extension'
      preLoaderRoute: typeof ExtensionIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard/': {
      id: '/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/analytics/': {
      id: '/analytics/'
      path: '/analytics'
      fullPath: '/analytics'
      preLoaderRoute: typeof AnalyticsIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/admin-procedures/': {
      id: '/admin-procedures/'
      path: '/admin-procedures'
      fullPath: '/admin-procedures'
      preLoaderRoute: typeof AdminProceduresIndexLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  AdminProceduresIndexLazyRoute: AdminProceduresIndexLazyRoute,
  AnalyticsIndexLazyRoute: AnalyticsIndexLazyRoute,
  DashboardIndexLazyRoute: DashboardIndexLazyRoute,
  ExtensionIndexLazyRoute: ExtensionIndexLazyRoute,
  FormsIndexLazyRoute: FormsIndexLazyRoute,
  InfoIndexLazyRoute: InfoIndexLazyRoute,
  LoginIndexLazyRoute: LoginIndexLazyRoute,
  OcrIndexLazyRoute: OcrIndexLazyRoute,
  ProductsIndexLazyRoute: ProductsIndexLazyRoute,
  ServicesIndexLazyRoute: ServicesIndexLazyRoute,
  SettingsIndexLazyRoute: SettingsIndexLazyRoute,
  SignupIndexLazyRoute: SignupIndexLazyRoute,
  WordMapperIndexLazyRoute: WordMapperIndexLazyRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

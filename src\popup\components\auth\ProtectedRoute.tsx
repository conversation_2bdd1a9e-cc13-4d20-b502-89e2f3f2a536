import React, { useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useAuth } from '@shared/hooks/useAuth';

interface ProtectedRouteProps {
    children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
    const { isAuthenticated, loading } = useAuth();
    const navigate = useNavigate();

    useEffect(() => {
        if (!loading && !isAuthenticated) {
            navigate({ to: '/login' });
        }
    }, [isAuthenticated, loading, navigate]);

    if (loading) {
        return (
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: 200,
                    gap: 1,
                    p: 2
                }}
            >
                <CircularProgress size={30} />
                <Typography variant="body2" color="text.secondary">
                    Loading...
                </Typography>
            </Box>
        );
    }

    if (!isAuthenticated) {
        return null; // Will redirect in useEffect
    }

    return <>{children}</>;
};

export default ProtectedRoute;

{"name": "chrome-extension-boilerplate-react-vite", "version": "1.0.0", "description": "A chrome extension boilerplate built with React 18 and Vite 5", "license": "MIT", "type": "module", "scripts": {"lint": "eslint . --cache --cache-strategy content --cache-location .cache/.eslintcache", "check-types": "tsc --noEmit", "format": "prettier --config prettier.config.js --write .", "watch": "vite build --watch", "build": "vite build"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fontsource/roboto": "^5.2.6", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/system": "^7.2.0", "@mui/x-date-pickers": "^8.9.2", "@react-pdf/renderer": "^4.3.0", "@tanstack/react-router": "^1.125.6", "@types/bcryptjs": "^3.0.0", "@types/fabric": "^5.3.10", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "dayjs": "^1.11.13", "docx-preview": "^0.3.5", "docx-templates": "^4.14.1", "docxtemplater": "^3.65.2", "fabric": "^6.7.0", "file-saver": "^2.0.5", "html-react-parser": "^5.2.6", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "mammoth": "^1.9.1", "notistack": "^3.0.2", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.3.93", "pizzip": "^3.2.0", "react": "^19.1.0", "react-doc-viewer": "^0.1.14", "react-dom": "^19.1.0", "react-pdf": "^10.0.1", "react-pdf-viewer": "^0.1.0", "socket.io-client": "^4.8.1", "watch": "^1.0.2"}, "devDependencies": {"@crxjs/vite-plugin": "^2.0.2", "@eslint/js": "^9.30.1", "@tanstack/router-plugin": "^1.125.6", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/file-saver": "^2.0.7", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/watch": "^1", "@vitejs/plugin-react": "^4.6.0", "chrome-types": "^0.1.359", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.6.2", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "vite": "^7.0.2"}, "packageManager": "yarn@4.9.2"}
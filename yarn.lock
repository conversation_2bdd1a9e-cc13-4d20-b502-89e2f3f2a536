# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec0732aac2be342d3d7d3ffd271c6f489ed
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10c0/5dd9a18baa5fce4741ba729acc3a3272c49c25cb8736c4b18e113099520e7ef7b545a4096a26d600e4416157e63e87d66db46aa3fbf0a5f2286da2705c12da00
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.27.2":
  version: 7.28.0
  resolution: "@babel/compat-data@npm:7.28.0"
  checksum: 10c0/c4e527302bcd61052423f757355a71c3bc62362bac13f7f130de16e439716f66091ff5bdecda418e8fa0271d4c725f860f0ee23ab7bf6e769f7a8bb16dfcb531
  languageName: node
  linkType: hard

"@babel/core@npm:^7.23.7, @babel/core@npm:^7.27.4, @babel/core@npm:^7.27.7, @babel/core@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/core@npm:7.28.0"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.28.0"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-module-transforms": "npm:^7.27.3"
    "@babel/helpers": "npm:^7.27.6"
    "@babel/parser": "npm:^7.28.0"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.28.0"
    "@babel/types": "npm:^7.28.0"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/423302e7c721e73b1c096217880272e02020dfb697a55ccca60ad01bba90037015f84d0c20c6ce297cf33a19bb704bc5c2b3d3095f5284dfa592bd1de0b9e8c3
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.26.5, @babel/generator@npm:^7.27.5, @babel/generator@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/generator@npm:7.28.0"
  dependencies:
    "@babel/parser": "npm:^7.28.0"
    "@babel/types": "npm:^7.28.0"
    "@jridgewell/gen-mapping": "npm:^0.3.12"
    "@jridgewell/trace-mapping": "npm:^0.3.28"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/1b3d122268ea3df50fde707ad864d9a55c72621357d5cebb972db3dd76859c45810c56e16ad23123f18f80cc2692f5a015d2858361300f0f224a05dc43d36a92
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.22.5, @babel/helper-annotate-as-pure@npm:^7.27.1, @babel/helper-annotate-as-pure@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-annotate-as-pure@npm:7.27.3"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  checksum: 10c0/94996ce0a05b7229f956033e6dcd69393db2b0886d0db6aff41e704390402b8cdcca11f61449cb4f86cfd9e61b5ad3a73e4fa661eeed7846b125bd1c33dbc633
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10c0/f338fa00dcfea931804a7c55d1a1c81b6f0a09787e528ec580d5c21b3ecb3913f6cb0f361368973ce953b824d910d3ac3e8a8ee15192710d3563826447193ad1
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-create-class-features-plugin@npm:7.27.1"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.1"
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/helper-replace-supers": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    semver: "npm:^6.3.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4ee199671d6b9bdd4988aa2eea4bdced9a73abfc831d81b00c7634f49a8fc271b3ceda01c067af58018eb720c6151322015d463abea7072a368ee13f35adbb4c
  languageName: node
  linkType: hard

"@babel/helper-globals@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/helper-globals@npm:7.28.0"
  checksum: 10c0/5a0cd0c0e8c764b5f27f2095e4243e8af6fa145daea2b41b53c0c1414fe6ff139e3640f4e2207ae2b3d2153a1abd346f901c26c290ee7cb3881dd922d4ee9232
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-member-expression-to-functions@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/5762ad009b6a3d8b0e6e79ff6011b3b8fdda0fefad56cfa8bfbe6aa02d5a8a8a9680a45748fe3ac47e735a03d2d88c0a676e3f9f59f20ae9fadcc8d51ccd5a53
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.0.0, @babel/helper-module-imports@npm:^7.16.7, @babel/helper-module-imports@npm:^7.22.5, @babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/e00aace096e4e29290ff8648455c2bc4ed982f0d61dbf2db1b5e750b9b98f318bf5788d75a4f974c151bd318fd549e81dbcab595f46b14b81c12eda3023f51e8
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.1, @babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.3"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/fccb4f512a13b4c069af51e1b56b20f54024bcf1591e31e978a30f3502567f34f90a80da6a19a6148c249216292a8074a0121f9e52602510ef0f32dbce95ca01
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-optimise-call-expression@npm:7.27.1"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/6b861e7fcf6031b9c9fc2de3cd6c005e94a459d6caf3621d93346b52774925800ca29d4f64595a5ceacf4d161eb0d27649ae385110ed69491d9776686fa488e6
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 10c0/94cf22c81a0c11a09b197b41ab488d416ff62254ce13c57e62912c85700dc2e99e555225787a4099ff6bae7a1812d622c80fbaeda824b79baa10a6c5ac4cf69b
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-replace-supers@npm:7.27.1"
  dependencies:
    "@babel/helper-member-expression-to-functions": "npm:^7.27.1"
    "@babel/helper-optimise-call-expression": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/4f2eaaf5fcc196580221a7ccd0f8873447b5d52745ad4096418f6101a1d2e712e9f93722c9a32bc9769a1dc197e001f60d6f5438d4dfde4b9c6a9e4df719354c
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/f625013bcdea422c470223a2614e90d2c1cc9d832e97f32ca1b4f82b34bb4aa67c3904cb4b116375d3b5b753acfb3951ed50835a1e832e7225295c7b0c24dff7
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10c0/8bda3448e07b5583727c103560bcf9c4c24b3c1051a4c516d4050ef69df37bb9a4734a585fe12725b8c2763de0a265aa1e909b485a4e3270b7cfd3e4dbe4b602
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10c0/c558f11c4871d526498e49d07a84752d1800bf72ac0d3dad100309a2eaba24efbf56ea59af5137ff15e3a00280ebe588560534b0e894a4750f8b1411d8f78b84
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10c0/6fec5f006eba40001a20f26b1ef5dbbda377b7b68c8ad518c05baa9af3f396e780bdfded24c4eef95d14bb7b8fd56192a6ed38d5d439b97d10efc5f1a191d148
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.6":
  version: 7.28.2
  resolution: "@babel/helpers@npm:7.28.2"
  dependencies:
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.28.2"
  checksum: 10c0/f3e7b21517e2699c4ca193663ecfb1bf1b2ae2762d8ba4a9f1786feaca0d6984537fc60bf2206e92c43640a6dada6b438f523cc1ad78610d0151aeb061b37f63
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.23.6, @babel/parser@npm:^7.26.7, @babel/parser@npm:^7.27.2, @babel/parser@npm:^7.27.5, @babel/parser@npm:^7.28.0":
  version: 7.28.0
  resolution: "@babel/parser@npm:7.28.0"
  dependencies:
    "@babel/types": "npm:^7.28.0"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/c2ef81d598990fa949d1d388429df327420357cb5200271d0d0a2784f1e6d54afc8301eb8bdf96d8f6c77781e402da93c7dc07980fcc136ac5b9d5f1fce701b5
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.22.5, @babel/plugin-syntax-jsx@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/bc5afe6a458d5f0492c02a54ad98c5756a0c13bd6d20609aae65acd560a9e141b0876da5f358dce34ea136f271c1016df58b461184d7ae9c4321e0f98588bc84
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/11589b4c89c66ef02d57bf56c6246267851ec0c361f58929327dc3e070b0dab644be625bbe7fb4c4df30c3634bfdfe31244e1f517be397d2def1487dbbe3c37d
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.27.1"
  dependencies:
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4def972dcd23375a266ea1189115a4ff61744b2c9366fc1de648b3fab2c650faf1a94092de93a33ff18858d2e6c4dddeeee5384cb42ba0129baeab01a5cdf1e2
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-self@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-self@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/00a4f917b70a608f9aca2fb39aabe04a60aa33165a7e0105fd44b3a8531630eb85bf5572e9f242f51e6ad2fa38c2e7e780902176c863556c58b5ba6f6e164031
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-source@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/plugin-transform-react-jsx-source@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/5e67b56c39c4d03e59e03ba80692b24c5a921472079b63af711b1d250fc37c1733a17069b63537f750f3e937ec44a42b1ee6a46cd23b1a0df5163b17f741f7f2
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.27.1":
  version: 7.28.0
  resolution: "@babel/plugin-transform-typescript@npm:7.28.0"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.27.3"
    "@babel/helper-create-class-features-plugin": "npm:^7.27.1"
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers": "npm:^7.27.1"
    "@babel/plugin-syntax-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/049c2bd3407bbf5041d8c95805a4fadee6d176e034f6b94ce7967b92a846f1e00f323cf7dfbb2d06c93485f241fb8cf4c10520e30096a6059d251b94e80386e9
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/preset-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
    "@babel/helper-validator-option": "npm:^7.27.1"
    "@babel/plugin-syntax-jsx": "npm:^7.27.1"
    "@babel/plugin-transform-modules-commonjs": "npm:^7.27.1"
    "@babel/plugin-transform-typescript": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/cba6ca793d915f8aff9fe2f13b0dfbf5fd3f2e9a17f17478ec9878e9af0d206dcfe93154b9fd353727f16c1dca7c7a3ceb4943f8d28b216235f106bc0fbbcaa3
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.12.5, @babel/runtime@npm:^7.18.3, @babel/runtime@npm:^7.20.13, @babel/runtime@npm:^7.26.7, @babel/runtime@npm:^7.28.2, @babel/runtime@npm:^7.5.5, @babel/runtime@npm:^7.8.7":
  version: 7.28.2
  resolution: "@babel/runtime@npm:7.28.2"
  checksum: 10c0/c20afe253629d53a405a610b12a62ac74d341a2c1e0fb202bbef0c118f6b5c84f94bf16039f58fd0483dd256901259930a43976845bdeb180cab1f882c21b6e0
  languageName: node
  linkType: hard

"@babel/template@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/ed9e9022651e463cc5f2cc21942f0e74544f1754d231add6348ff1b472985a3b3502041c0be62dc99ed2d12cfae0c51394bf827452b98a2f8769c03b87aadc81
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.23.7, @babel/traverse@npm:^7.26.7, @babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3, @babel/traverse@npm:^7.27.7, @babel/traverse@npm:^7.28.0, @babel/traverse@npm:^7.4.5":
  version: 7.28.0
  resolution: "@babel/traverse@npm:7.28.0"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.28.0"
    "@babel/helper-globals": "npm:^7.28.0"
    "@babel/parser": "npm:^7.28.0"
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.28.0"
    debug: "npm:^4.3.1"
  checksum: 10c0/32794402457827ac558173bcebdcc0e3a18fa339b7c41ca35621f9f645f044534d91bb923ff385f5f960f2e495f56ce18d6c7b0d064d2f0ccb55b285fa6bc7b9
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.23.6, @babel/types@npm:^7.26.7, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3, @babel/types@npm:^7.27.7, @babel/types@npm:^7.28.0, @babel/types@npm:^7.28.2":
  version: 7.28.2
  resolution: "@babel/types@npm:7.28.2"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10c0/24b11c9368e7e2c291fe3c1bcd1ed66f6593a3975f479cbb9dd7b8c8d8eab8a962b0d2fca616c043396ce82500ac7d23d594fbbbd013828182c01596370a0b10
  languageName: node
  linkType: hard

"@crxjs/vite-plugin@npm:^2.0.2":
  version: 2.0.3
  resolution: "@crxjs/vite-plugin@npm:2.0.3"
  dependencies:
    "@rollup/pluginutils": "npm:^4.1.2"
    "@webcomponents/custom-elements": "npm:^1.5.0"
    acorn-walk: "npm:^8.2.0"
    cheerio: "npm:^1.0.0-rc.10"
    convert-source-map: "npm:^1.7.0"
    debug: "npm:^4.3.3"
    es-module-lexer: "npm:^0.10.0"
    fast-glob: "npm:^3.2.11"
    fs-extra: "npm:^10.0.1"
    jsesc: "npm:^3.0.2"
    magic-string: "npm:^0.30.12"
    pathe: "npm:^2.0.1"
    picocolors: "npm:^1.1.1"
    react-refresh: "npm:^0.13.0"
    rollup: "npm:2.79.2"
    rxjs: "npm:7.5.7"
  checksum: 10c0/3f42433efd44e7852be9995436852b362128885f3a2c4ad3bbfca60b7de2de92a5b3f9bba334f596b07a792df1b4e278f2cf9b37227d4d84d1a62afcc5eb0a33
  languageName: node
  linkType: hard

"@emotion/babel-plugin@npm:^11.13.5":
  version: 11.13.5
  resolution: "@emotion/babel-plugin@npm:11.13.5"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.16.7"
    "@babel/runtime": "npm:^7.18.3"
    "@emotion/hash": "npm:^0.9.2"
    "@emotion/memoize": "npm:^0.9.0"
    "@emotion/serialize": "npm:^1.3.3"
    babel-plugin-macros: "npm:^3.1.0"
    convert-source-map: "npm:^1.5.0"
    escape-string-regexp: "npm:^4.0.0"
    find-root: "npm:^1.1.0"
    source-map: "npm:^0.5.7"
    stylis: "npm:4.2.0"
  checksum: 10c0/8ccbfec7defd0e513cb8a1568fa179eac1e20c35fda18aed767f6c59ea7314363ebf2de3e9d2df66c8ad78928dc3dceeded84e6fa8059087cae5c280090aeeeb
  languageName: node
  linkType: hard

"@emotion/cache@npm:^11.14.0":
  version: 11.14.0
  resolution: "@emotion/cache@npm:11.14.0"
  dependencies:
    "@emotion/memoize": "npm:^0.9.0"
    "@emotion/sheet": "npm:^1.4.0"
    "@emotion/utils": "npm:^1.4.2"
    "@emotion/weak-memoize": "npm:^0.4.0"
    stylis: "npm:4.2.0"
  checksum: 10c0/3fa3e7a431ab6f8a47c67132a00ac8358f428c1b6c8421d4b20de9df7c18e95eec04a5a6ff5a68908f98d3280044f247b4965ac63df8302d2c94dba718769724
  languageName: node
  linkType: hard

"@emotion/hash@npm:^0.9.2":
  version: 0.9.2
  resolution: "@emotion/hash@npm:0.9.2"
  checksum: 10c0/0dc254561a3cc0a06a10bbce7f6a997883fd240c8c1928b93713f803a2e9153a257a488537012efe89dbe1246f2abfe2add62cdb3471a13d67137fcb808e81c2
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:^1.1.0, @emotion/is-prop-valid@npm:^1.3.0":
  version: 1.3.1
  resolution: "@emotion/is-prop-valid@npm:1.3.1"
  dependencies:
    "@emotion/memoize": "npm:^0.9.0"
  checksum: 10c0/123215540c816ff510737ec68dcc499c53ea4deb0bb6c2c27c03ed21046e2e69f6ad07a7a174d271c6cfcbcc9ea44e1763e0cf3875c92192f7689216174803cd
  languageName: node
  linkType: hard

"@emotion/memoize@npm:^0.9.0":
  version: 0.9.0
  resolution: "@emotion/memoize@npm:0.9.0"
  checksum: 10c0/13f474a9201c7f88b543e6ea42f55c04fb2fdc05e6c5a3108aced2f7e7aa7eda7794c56bba02985a46d8aaa914fcdde238727a98341a96e2aec750d372dadd15
  languageName: node
  linkType: hard

"@emotion/react@npm:^11.14.0":
  version: 11.14.0
  resolution: "@emotion/react@npm:11.14.0"
  dependencies:
    "@babel/runtime": "npm:^7.18.3"
    "@emotion/babel-plugin": "npm:^11.13.5"
    "@emotion/cache": "npm:^11.14.0"
    "@emotion/serialize": "npm:^1.3.3"
    "@emotion/use-insertion-effect-with-fallbacks": "npm:^1.2.0"
    "@emotion/utils": "npm:^1.4.2"
    "@emotion/weak-memoize": "npm:^0.4.0"
    hoist-non-react-statics: "npm:^3.3.1"
  peerDependencies:
    react: ">=16.8.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/d0864f571a9f99ec643420ef31fde09e2006d3943a6aba079980e4d5f6e9f9fecbcc54b8f617fe003c00092ff9d5241179149ffff2810cb05cf72b4620cfc031
  languageName: node
  linkType: hard

"@emotion/serialize@npm:^1.3.3":
  version: 1.3.3
  resolution: "@emotion/serialize@npm:1.3.3"
  dependencies:
    "@emotion/hash": "npm:^0.9.2"
    "@emotion/memoize": "npm:^0.9.0"
    "@emotion/unitless": "npm:^0.10.0"
    "@emotion/utils": "npm:^1.4.2"
    csstype: "npm:^3.0.2"
  checksum: 10c0/b28cb7de59de382021de2b26c0c94ebbfb16967a1b969a56fdb6408465a8993df243bfbd66430badaa6800e1834724e84895f5a6a9d97d0d224de3d77852acb4
  languageName: node
  linkType: hard

"@emotion/sheet@npm:^1.4.0":
  version: 1.4.0
  resolution: "@emotion/sheet@npm:1.4.0"
  checksum: 10c0/3ca72d1650a07d2fbb7e382761b130b4a887dcd04e6574b2d51ce578791240150d7072a9bcb4161933abbcd1e38b243a6fb4464a7fe991d700c17aa66bb5acc7
  languageName: node
  linkType: hard

"@emotion/styled@npm:^11.14.1":
  version: 11.14.1
  resolution: "@emotion/styled@npm:11.14.1"
  dependencies:
    "@babel/runtime": "npm:^7.18.3"
    "@emotion/babel-plugin": "npm:^11.13.5"
    "@emotion/is-prop-valid": "npm:^1.3.0"
    "@emotion/serialize": "npm:^1.3.3"
    "@emotion/use-insertion-effect-with-fallbacks": "npm:^1.2.0"
    "@emotion/utils": "npm:^1.4.2"
  peerDependencies:
    "@emotion/react": ^11.0.0-rc.0
    react: ">=16.8.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/2bbf8451df49c967e41fbcf8111a7f6dafe6757f0cc113f2f6e287206c45ac1d54dc8a95a483b7c0cee8614b8a8d08155bded6453d6721de1f8cc8d5b9216963
  languageName: node
  linkType: hard

"@emotion/stylis@npm:^0.8.4":
  version: 0.8.5
  resolution: "@emotion/stylis@npm:0.8.5"
  checksum: 10c0/f109e3f11cb0d48e8658aaa23578c5bcfe35e297819cfb089a3de6ba8dc0f89b0960474922690c6028df5d2e1895b4967f2fb280642c030054c312f1e137ce26
  languageName: node
  linkType: hard

"@emotion/unitless@npm:^0.10.0":
  version: 0.10.0
  resolution: "@emotion/unitless@npm:0.10.0"
  checksum: 10c0/150943192727b7650eb9a6851a98034ddb58a8b6958b37546080f794696141c3760966ac695ab9af97efe10178690987aee4791f9f0ad1ff76783cdca83c1d49
  languageName: node
  linkType: hard

"@emotion/unitless@npm:^0.7.4":
  version: 0.7.5
  resolution: "@emotion/unitless@npm:0.7.5"
  checksum: 10c0/4d0d94f53cb97b4481bbfa394953e1899a0b877644642ba9dd7247c27eb8c48e14e22aeb11411d7d9874685ad85dd5fb5b50eb78c6d8840eb56a84b92dcef2f4
  languageName: node
  linkType: hard

"@emotion/use-insertion-effect-with-fallbacks@npm:^1.2.0":
  version: 1.2.0
  resolution: "@emotion/use-insertion-effect-with-fallbacks@npm:1.2.0"
  peerDependencies:
    react: ">=16.8.0"
  checksum: 10c0/074dbc92b96bdc09209871070076e3b0351b6b47efefa849a7d9c37ab142130767609ca1831da0055988974e3b895c1de7606e4c421fecaa27c3e56a2afd3b08
  languageName: node
  linkType: hard

"@emotion/utils@npm:^1.4.2":
  version: 1.4.2
  resolution: "@emotion/utils@npm:1.4.2"
  checksum: 10c0/7d0010bf60a2a8c1a033b6431469de4c80e47aeb8fd856a17c1d1f76bbc3a03161a34aeaa78803566e29681ca551e7bf9994b68e9c5f5c796159923e44f78d9a
  languageName: node
  linkType: hard

"@emotion/weak-memoize@npm:^0.4.0":
  version: 0.4.0
  resolution: "@emotion/weak-memoize@npm:0.4.0"
  checksum: 10c0/64376af11f1266042d03b3305c30b7502e6084868e33327e944b539091a472f089db307af69240f7188f8bc6b319276fd7b141a36613f1160d73d12a60f6ca1a
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/aix-ppc64@npm:0.25.8"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/android-arm64@npm:0.25.8"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/android-arm@npm:0.25.8"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/android-x64@npm:0.25.8"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/darwin-arm64@npm:0.25.8"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/darwin-x64@npm:0.25.8"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/freebsd-arm64@npm:0.25.8"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/freebsd-x64@npm:0.25.8"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/linux-arm64@npm:0.25.8"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/linux-arm@npm:0.25.8"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/linux-ia32@npm:0.25.8"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/linux-loong64@npm:0.25.8"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/linux-mips64el@npm:0.25.8"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/linux-ppc64@npm:0.25.8"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/linux-riscv64@npm:0.25.8"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/linux-s390x@npm:0.25.8"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/linux-x64@npm:0.25.8"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/netbsd-arm64@npm:0.25.8"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/netbsd-x64@npm:0.25.8"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/openbsd-arm64@npm:0.25.8"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/openbsd-x64@npm:0.25.8"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openharmony-arm64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/openharmony-arm64@npm:0.25.8"
  conditions: os=openharmony & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/sunos-x64@npm:0.25.8"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/win32-arm64@npm:0.25.8"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/win32-ia32@npm:0.25.8"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.25.8":
  version: 0.25.8
  resolution: "@esbuild/win32-x64@npm:0.25.8"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.7.0":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10c0/c0f4f2bd73b7b7a9de74b716a664873d08ab71ab439e51befe77d61915af41a81ecec93b408778b3a7856185244c34c2c8ee28912072ec14def84ba2dec70adf
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10c0/a03d98c246bcb9109aec2c08e4d10c8d010256538dcb3f56610191607214523d4fb1b00aa81df830b6dffb74c5fa0be03642513a289c567949d3e550ca11cdf6
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.21.0":
  version: 0.21.0
  resolution: "@eslint/config-array@npm:0.21.0"
  dependencies:
    "@eslint/object-schema": "npm:^2.1.6"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.1.2"
  checksum: 10c0/0ea801139166c4aa56465b309af512ef9b2d3c68f9198751bbc3e21894fe70f25fbf26e1b0e9fffff41857bc21bfddeee58649ae6d79aadcd747db0c5dca771f
  languageName: node
  linkType: hard

"@eslint/config-helpers@npm:^0.3.0":
  version: 0.3.0
  resolution: "@eslint/config-helpers@npm:0.3.0"
  checksum: 10c0/013ae7b189eeae8b30cc2ee87bc5c9c091a9cd615579003290eb28bebad5d78806a478e74ba10b3fe08ed66975b52af7d2cd4b4b43990376412b14e5664878c8
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.15.0, @eslint/core@npm:^0.15.1":
  version: 0.15.1
  resolution: "@eslint/core@npm:0.15.1"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10c0/abaf641940776638b8c15a38d99ce0dac551a8939310ec81b9acd15836a574cf362588eaab03ab11919bc2a0f9648b19ea8dee33bf12675eb5b6fd38bda6f25e
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.3.1":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^10.0.1"
    globals: "npm:^14.0.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10c0/b0e63f3bc5cce4555f791a4e487bf999173fcf27c65e1ab6e7d63634d8a43b33c3693e79f192cbff486d7df1be8ebb2bd2edc6e70ddd486cbfa84a359a3e3b41
  languageName: node
  linkType: hard

"@eslint/js@npm:9.32.0, @eslint/js@npm:^9.30.1":
  version: 9.32.0
  resolution: "@eslint/js@npm:9.32.0"
  checksum: 10c0/f71e8f9146638d11fb15238279feff98801120a4d4130f1c587c4f09b024ff5ec01af1ba88e97ba6b7013488868898a668f77091300cc3d4394c7a8ed32d2667
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: 10c0/b8cdb7edea5bc5f6a96173f8d768d3554a628327af536da2fc6967a93b040f2557114d98dbcdbf389d5a7b290985ad6a9ce5babc547f36fc1fde42e674d11a56
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.3.4":
  version: 0.3.4
  resolution: "@eslint/plugin-kit@npm:0.3.4"
  dependencies:
    "@eslint/core": "npm:^0.15.1"
    levn: "npm:^0.4.1"
  checksum: 10c0/64331ca100f62a0115d10419a28059d0f377e390192163b867b9019517433d5073d10b4ec21f754fa01faf832aceb34178745924baab2957486f8bf95fd628d2
  languageName: node
  linkType: hard

"@fontsource/roboto@npm:^5.2.6":
  version: 5.2.6
  resolution: "@fontsource/roboto@npm:5.2.6"
  checksum: 10c0/a2fbadf2f3b8ebb859e7b13ad82d25e8b43901c30ad93b08095c201617fcfded279d99e64a7b110614d2a1b5bca77631df7d009abf1ad6c6a4df301ceb330a51
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 10c0/aa4e0152171c07879b458d0e8a704b8c3a89a8c0541726c6b65b81e84fd8b7564b5d6c633feadc6598307d34564bd53294b533491424e8e313d7ab6c7bc5dc67
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": "npm:^0.19.1"
    "@humanwhocodes/retry": "npm:^0.3.0"
  checksum: 10c0/8356359c9f60108ec204cbd249ecd0356667359b2524886b357617c4a7c3b6aace0fd5a369f63747b926a762a88f8a25bc066fa1778508d110195ce7686243e1
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10c0/909b69c3b86d482c26b3359db16e46a32e0fb30bd306a3c176b8313b9e7313dba0f37f519de6aa8b0a1921349e505f259d19475e123182416a506d7f87e7f529
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 10c0/f0da1282dfb45e8120480b9e2e275e2ac9bbe1cf016d046fdad8e27cc1285c45bb9e711681237944445157b430093412b4446c1ab3fc4bb037861b5904101d3b
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.2":
  version: 0.4.3
  resolution: "@humanwhocodes/retry@npm:0.4.3"
  checksum: 10c0/3775bb30087d4440b3f7406d5a057777d90e4b9f435af488a4923ef249e93615fb78565a85f173a186a076c7706a81d0d57d563a2624e4de2c5c9c66c486ce42
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.12, @jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.12
  resolution: "@jridgewell/gen-mapping@npm:0.3.12"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/32f771ae2467e4d440be609581f7338d786d3d621bac3469e943b9d6d116c23c4becb36f84898a92bbf2f3c0511365c54a945a3b86a83141547a2a360a5ec0c7
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10c0/d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.4
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.4"
  checksum: 10c0/c5aab3e6362a8dd94ad80ab90845730c825fc4c8d9cf07ebca7a2eb8a832d155d62558800fc41d42785f989ddbb21db6df004d1786e8ecb65e428ab8dff71309
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.28":
  version: 0.3.29
  resolution: "@jridgewell/trace-mapping@npm:0.3.29"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/fb547ba31658c4d74eb17e7389f4908bf7c44cef47acb4c5baa57289daf68e6fe53c639f41f751b3923aca67010501264f70e7b49978ad1f040294b22c37b333
  languageName: node
  linkType: hard

"@mapbox/node-pre-gyp@npm:^1.0.0":
  version: 1.0.11
  resolution: "@mapbox/node-pre-gyp@npm:1.0.11"
  dependencies:
    detect-libc: "npm:^2.0.0"
    https-proxy-agent: "npm:^5.0.0"
    make-dir: "npm:^3.1.0"
    node-fetch: "npm:^2.6.7"
    nopt: "npm:^5.0.0"
    npmlog: "npm:^5.0.1"
    rimraf: "npm:^3.0.2"
    semver: "npm:^7.3.5"
    tar: "npm:^6.1.11"
  bin:
    node-pre-gyp: bin/node-pre-gyp
  checksum: 10c0/2b24b93c31beca1c91336fa3b3769fda98e202fb7f9771f0f4062588d36dcc30fcf8118c36aa747fa7f7610d8cf601872bdaaf62ce7822bb08b545d1bbe086cc
  languageName: node
  linkType: hard

"@mui/core-downloads-tracker@npm:^7.3.0":
  version: 7.3.0
  resolution: "@mui/core-downloads-tracker@npm:7.3.0"
  checksum: 10c0/0779bb5688b8d5a538cde148f235708f8f4700aab304f28aaa88fc46557274bed9115c5143f5276368512bc0fb0f32a4bc1d8a1049628a3c7800fce01ea8130e
  languageName: node
  linkType: hard

"@mui/icons-material@npm:^7.2.0":
  version: 7.3.0
  resolution: "@mui/icons-material@npm:7.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.28.2"
  peerDependencies:
    "@mui/material": ^7.3.0
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/d836b592a4065e0b1dea32250b83c341cd7ff5006842ca9d2cc4f1341d2f1d8a65bd79e2fdd28c5fed7afa53a59c0ab350f4c6c33a2b490d7c4c9ad2708b1de4
  languageName: node
  linkType: hard

"@mui/material@npm:^7.2.0":
  version: 7.3.0
  resolution: "@mui/material@npm:7.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.28.2"
    "@mui/core-downloads-tracker": "npm:^7.3.0"
    "@mui/system": "npm:^7.3.0"
    "@mui/types": "npm:^7.4.5"
    "@mui/utils": "npm:^7.3.0"
    "@popperjs/core": "npm:^2.11.8"
    "@types/react-transition-group": "npm:^4.4.12"
    clsx: "npm:^2.1.1"
    csstype: "npm:^3.1.3"
    prop-types: "npm:^15.8.1"
    react-is: "npm:^19.1.1"
    react-transition-group: "npm:^4.4.5"
  peerDependencies:
    "@emotion/react": ^11.5.0
    "@emotion/styled": ^11.3.0
    "@mui/material-pigment-css": ^7.3.0
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    "@mui/material-pigment-css":
      optional: true
    "@types/react":
      optional: true
  checksum: 10c0/1bf6041123db261571cab73016fffdc73c2ef7de79f50a42c01259f153348e5bced99f4f9ff8def5c5a9e3612e77e006e009b949738d4fc716cbda742fd26284
  languageName: node
  linkType: hard

"@mui/private-theming@npm:^7.3.0":
  version: 7.3.0
  resolution: "@mui/private-theming@npm:7.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.28.2"
    "@mui/utils": "npm:^7.3.0"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/f3f791057480568563fb0fb8766a819efddc9e56445a8c65e3158d74d8e716263f9a1f520827627e0553f81d6ca007aed861c7665d019f1dcef9a1a57a22ec60
  languageName: node
  linkType: hard

"@mui/styled-engine@npm:^7.3.0":
  version: 7.3.0
  resolution: "@mui/styled-engine@npm:7.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.28.2"
    "@emotion/cache": "npm:^11.14.0"
    "@emotion/serialize": "npm:^1.3.3"
    "@emotion/sheet": "npm:^1.4.0"
    csstype: "npm:^3.1.3"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@emotion/react": ^11.4.1
    "@emotion/styled": ^11.3.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
  checksum: 10c0/d63720bc6b5c38242244c938ee1461c152698cb9246beb5c987fcd5bfa009577d4647d897af8170f4ae5b7a45d9064eb88b44f9b5a92c340124de711f0a17a90
  languageName: node
  linkType: hard

"@mui/system@npm:^7.2.0, @mui/system@npm:^7.3.0":
  version: 7.3.0
  resolution: "@mui/system@npm:7.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.28.2"
    "@mui/private-theming": "npm:^7.3.0"
    "@mui/styled-engine": "npm:^7.3.0"
    "@mui/types": "npm:^7.4.5"
    "@mui/utils": "npm:^7.3.0"
    clsx: "npm:^2.1.1"
    csstype: "npm:^3.1.3"
    prop-types: "npm:^15.8.1"
  peerDependencies:
    "@emotion/react": ^11.5.0
    "@emotion/styled": ^11.3.0
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    "@types/react":
      optional: true
  checksum: 10c0/4b6fdf58934a78aa7fc169f89d5153c1066461369778c8d62c400578d1233992fab87ca3f71d7a0f5370a83b644573f46ee448a67ce7bdff57345fe409bac75d
  languageName: node
  linkType: hard

"@mui/types@npm:^7.4.5":
  version: 7.4.5
  resolution: "@mui/types@npm:7.4.5"
  dependencies:
    "@babel/runtime": "npm:^7.28.2"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/65015aacdc388a48e62c7bf11d167709f6d7e19e32fa8d4b36d34d391e10edc315bc2d64bf7f542d54fd973e812533800e715c5b0f149c29c3852e2c3dc11587
  languageName: node
  linkType: hard

"@mui/utils@npm:^7.2.0, @mui/utils@npm:^7.3.0":
  version: 7.3.0
  resolution: "@mui/utils@npm:7.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.28.2"
    "@mui/types": "npm:^7.4.5"
    "@types/prop-types": "npm:^15.7.15"
    clsx: "npm:^2.1.1"
    prop-types: "npm:^15.8.1"
    react-is: "npm:^19.1.1"
  peerDependencies:
    "@types/react": ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/a7e58df17f7ab6a6545c859b528a55eab842aa082fc830106a2b433f4af776d29dbd8a4a29ecd19b374b92bf096a46e2d1c49a043d2cc50d26dfa7d342921e1b
  languageName: node
  linkType: hard

"@mui/x-date-pickers@npm:^8.9.2":
  version: 8.9.2
  resolution: "@mui/x-date-pickers@npm:8.9.2"
  dependencies:
    "@babel/runtime": "npm:^7.28.2"
    "@mui/utils": "npm:^7.2.0"
    "@mui/x-internals": "npm:8.9.2"
    "@types/react-transition-group": "npm:^4.4.12"
    clsx: "npm:^2.1.1"
    prop-types: "npm:^15.8.1"
    react-transition-group: "npm:^4.4.5"
  peerDependencies:
    "@emotion/react": ^11.9.0
    "@emotion/styled": ^11.8.1
    "@mui/material": ^5.15.14 || ^6.0.0 || ^7.0.0
    "@mui/system": ^5.15.14 || ^6.0.0 || ^7.0.0
    date-fns: ^2.25.0 || ^3.2.0 || ^4.0.0
    date-fns-jalali: ^2.13.0-0 || ^3.2.0-0 || ^4.0.0-0
    dayjs: ^1.10.7
    luxon: ^3.0.2
    moment: ^2.29.4
    moment-hijri: ^2.1.2 || ^3.0.0
    moment-jalaali: ^0.7.4 || ^0.8.0 || ^0.9.0 || ^0.10.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@emotion/react":
      optional: true
    "@emotion/styled":
      optional: true
    date-fns:
      optional: true
    date-fns-jalali:
      optional: true
    dayjs:
      optional: true
    luxon:
      optional: true
    moment:
      optional: true
    moment-hijri:
      optional: true
    moment-jalaali:
      optional: true
  checksum: 10c0/65883c735b76c4e7fcd77d27f149c1cc9993ae23dba4814706d5a29b8beadaf867346674b581cbebf1e3ced7fe3f48a3533e37e79710603d5f8f80f9eb0d13a5
  languageName: node
  linkType: hard

"@mui/x-internals@npm:8.9.2":
  version: 8.9.2
  resolution: "@mui/x-internals@npm:8.9.2"
  dependencies:
    "@babel/runtime": "npm:^7.28.2"
    "@mui/utils": "npm:^7.2.0"
    reselect: "npm:^5.1.1"
    use-sync-external-store: "npm:^1.5.0"
  peerDependencies:
    "@mui/system": ^5.15.14 || ^6.0.0 || ^7.0.0
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/56d459aa947452fdcc415364bc3fd4f2b2e71b7b2843da28eb18cba1e9ed0914576e6fda51be41b5dae061e31fba526afd2a138aa1f2a654223d72f667b1b256
  languageName: node
  linkType: hard

"@napi-rs/canvas-android-arm64@npm:0.1.74":
  version: 0.1.74
  resolution: "@napi-rs/canvas-android-arm64@npm:0.1.74"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@napi-rs/canvas-darwin-arm64@npm:0.1.74":
  version: 0.1.74
  resolution: "@napi-rs/canvas-darwin-arm64@npm:0.1.74"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@napi-rs/canvas-darwin-x64@npm:0.1.74":
  version: 0.1.74
  resolution: "@napi-rs/canvas-darwin-x64@npm:0.1.74"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@napi-rs/canvas-linux-arm-gnueabihf@npm:0.1.74":
  version: 0.1.74
  resolution: "@napi-rs/canvas-linux-arm-gnueabihf@npm:0.1.74"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@napi-rs/canvas-linux-arm64-gnu@npm:0.1.74":
  version: 0.1.74
  resolution: "@napi-rs/canvas-linux-arm64-gnu@npm:0.1.74"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/canvas-linux-arm64-musl@npm:0.1.74":
  version: 0.1.74
  resolution: "@napi-rs/canvas-linux-arm64-musl@npm:0.1.74"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@napi-rs/canvas-linux-riscv64-gnu@npm:0.1.74":
  version: 0.1.74
  resolution: "@napi-rs/canvas-linux-riscv64-gnu@npm:0.1.74"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/canvas-linux-x64-gnu@npm:0.1.74":
  version: 0.1.74
  resolution: "@napi-rs/canvas-linux-x64-gnu@npm:0.1.74"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@napi-rs/canvas-linux-x64-musl@npm:0.1.74":
  version: 0.1.74
  resolution: "@napi-rs/canvas-linux-x64-musl@npm:0.1.74"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@napi-rs/canvas-win32-x64-msvc@npm:0.1.74":
  version: 0.1.74
  resolution: "@napi-rs/canvas-win32-x64-msvc@npm:0.1.74"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@napi-rs/canvas@npm:^0.1.65, @napi-rs/canvas@npm:^0.1.67, @napi-rs/canvas@npm:^0.1.71":
  version: 0.1.74
  resolution: "@napi-rs/canvas@npm:0.1.74"
  dependencies:
    "@napi-rs/canvas-android-arm64": "npm:0.1.74"
    "@napi-rs/canvas-darwin-arm64": "npm:0.1.74"
    "@napi-rs/canvas-darwin-x64": "npm:0.1.74"
    "@napi-rs/canvas-linux-arm-gnueabihf": "npm:0.1.74"
    "@napi-rs/canvas-linux-arm64-gnu": "npm:0.1.74"
    "@napi-rs/canvas-linux-arm64-musl": "npm:0.1.74"
    "@napi-rs/canvas-linux-riscv64-gnu": "npm:0.1.74"
    "@napi-rs/canvas-linux-x64-gnu": "npm:0.1.74"
    "@napi-rs/canvas-linux-x64-musl": "npm:0.1.74"
    "@napi-rs/canvas-win32-x64-msvc": "npm:0.1.74"
  dependenciesMeta:
    "@napi-rs/canvas-android-arm64":
      optional: true
    "@napi-rs/canvas-darwin-arm64":
      optional: true
    "@napi-rs/canvas-darwin-x64":
      optional: true
    "@napi-rs/canvas-linux-arm-gnueabihf":
      optional: true
    "@napi-rs/canvas-linux-arm64-gnu":
      optional: true
    "@napi-rs/canvas-linux-arm64-musl":
      optional: true
    "@napi-rs/canvas-linux-riscv64-gnu":
      optional: true
    "@napi-rs/canvas-linux-x64-gnu":
      optional: true
    "@napi-rs/canvas-linux-x64-musl":
      optional: true
    "@napi-rs/canvas-win32-x64-msvc":
      optional: true
  checksum: 10c0/01f55af96045e3024b825446951dd2adc293679d619cc57611171f0c358e617255d6ec5e7e66cace3125e00844f2ac7cbfd222e8aae75a59584281948cccc1be
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@pdf-lib/standard-fonts@npm:^1.0.0":
  version: 1.0.0
  resolution: "@pdf-lib/standard-fonts@npm:1.0.0"
  dependencies:
    pako: "npm:^1.0.6"
  checksum: 10c0/c683adfb764cd235a8370a0c1d5a8d7e90e3499ad33cdecfb92e4d48b0d36cfd038e3a875ebd0937a5646ee1578d793ab98f9c374be360c9a05d2699c1caedf4
  languageName: node
  linkType: hard

"@pdf-lib/upng@npm:^1.0.1":
  version: 1.0.1
  resolution: "@pdf-lib/upng@npm:1.0.1"
  dependencies:
    pako: "npm:^1.0.10"
  checksum: 10c0/9c300c513c1089e561c0cccac01f396a24efb9b0e9c922a39248cb09dfced70c05b9facdfce11a7f22cbedb4129593630a18111b90a57ef34ea4c3df98f2ac1d
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@popperjs/core@npm:^2.11.8":
  version: 2.11.8
  resolution: "@popperjs/core@npm:2.11.8"
  checksum: 10c0/4681e682abc006d25eb380d0cf3efc7557043f53b6aea7a5057d0d1e7df849a00e281cd8ea79c902a35a414d7919621fc2ba293ecec05f413598e0b23d5a1e63
  languageName: node
  linkType: hard

"@react-pdf/fns@npm:3.1.2":
  version: 3.1.2
  resolution: "@react-pdf/fns@npm:3.1.2"
  checksum: 10c0/e4af2428cbc267e2c9b1e5f3bdc7426a956d9800a0f7efc56ac50ebb643e35a267f982c6d9e30436ec44fe4a840fec6da48231391d9eb07ec09bcd58b4fd2578
  languageName: node
  linkType: hard

"@react-pdf/font@npm:^4.0.2":
  version: 4.0.2
  resolution: "@react-pdf/font@npm:4.0.2"
  dependencies:
    "@react-pdf/pdfkit": "npm:^4.0.3"
    "@react-pdf/types": "npm:^2.9.0"
    fontkit: "npm:^2.0.2"
    is-url: "npm:^1.2.4"
  checksum: 10c0/423cf6370fe73f17f747eb479ee0204f9064dc83d3af52225c1a97265cae3fa0c07ac9fe21b32addfc16a5292572599b6c2248c8ccdaef9408eda8d7ad0d9df0
  languageName: node
  linkType: hard

"@react-pdf/image@npm:^3.0.3":
  version: 3.0.3
  resolution: "@react-pdf/image@npm:3.0.3"
  dependencies:
    "@react-pdf/png-js": "npm:^3.0.0"
    jay-peg: "npm:^1.1.1"
  checksum: 10c0/cfe584a8c300f020153dd7917f17b0130a11c535bc96a08bc1ad1f23c9e0c27b176b40e68e9e180bf2384f76d3ea7340cbb763daa1e3e37e1bf95c51491bcb39
  languageName: node
  linkType: hard

"@react-pdf/layout@npm:^4.4.0":
  version: 4.4.0
  resolution: "@react-pdf/layout@npm:4.4.0"
  dependencies:
    "@react-pdf/fns": "npm:3.1.2"
    "@react-pdf/image": "npm:^3.0.3"
    "@react-pdf/primitives": "npm:^4.1.1"
    "@react-pdf/stylesheet": "npm:^6.1.0"
    "@react-pdf/textkit": "npm:^6.0.0"
    "@react-pdf/types": "npm:^2.9.0"
    emoji-regex: "npm:^10.3.0"
    queue: "npm:^6.0.1"
    yoga-layout: "npm:^3.2.1"
  checksum: 10c0/7a45c40a087b6deb1aa5c5b3b2a700a89037d442c875a65d43b6af8598d0404e5d9431d79036bc34e2c27c19d0f88b162864b88e0e39751aa71a3450dbe4c43f
  languageName: node
  linkType: hard

"@react-pdf/pdfkit@npm:^4.0.3":
  version: 4.0.3
  resolution: "@react-pdf/pdfkit@npm:4.0.3"
  dependencies:
    "@babel/runtime": "npm:^7.20.13"
    "@react-pdf/png-js": "npm:^3.0.0"
    browserify-zlib: "npm:^0.2.0"
    crypto-js: "npm:^4.2.0"
    fontkit: "npm:^2.0.2"
    jay-peg: "npm:^1.1.1"
    linebreak: "npm:^1.1.0"
    vite-compatible-readable-stream: "npm:^3.6.1"
  checksum: 10c0/314414ef4db27d272f177367b2b75406a5111513f571e6ee57d93977955dd475b1c284fc2f9652e0432898bba108c9d6416226b4c0a8418abc04f5c7fbeea910
  languageName: node
  linkType: hard

"@react-pdf/png-js@npm:^3.0.0":
  version: 3.0.0
  resolution: "@react-pdf/png-js@npm:3.0.0"
  dependencies:
    browserify-zlib: "npm:^0.2.0"
  checksum: 10c0/8ed9b4936df2440b4cec66b56d4da9150458a8d38d4f53fd569a590e8d7cf7629819a0ce0cff22bcb60ccda2252734501cd2b6d6d372b5b7e50026a1a840a43e
  languageName: node
  linkType: hard

"@react-pdf/primitives@npm:^4.1.1":
  version: 4.1.1
  resolution: "@react-pdf/primitives@npm:4.1.1"
  checksum: 10c0/595feb7997534382bedb0ec005d17fe788559372e832802a0fb96cf1e9100373076281b5ef309fd1ade09f2eafc33ab4b2113c5e1f31ca55d267fa4ee3626c59
  languageName: node
  linkType: hard

"@react-pdf/reconciler@npm:^1.1.4":
  version: 1.1.4
  resolution: "@react-pdf/reconciler@npm:1.1.4"
  dependencies:
    object-assign: "npm:^4.1.1"
    scheduler: "npm:0.25.0-rc-603e6108-20241029"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/f179e38f5ffd3d8f44c8ac7f2875f82bacbc03b2994056c3cc24da90d07ccaa22e4a7bd5fc023734e66f7a1f082bb059a88201011aa10346054274c01f138ddb
  languageName: node
  linkType: hard

"@react-pdf/render@npm:^4.3.0":
  version: 4.3.0
  resolution: "@react-pdf/render@npm:4.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.20.13"
    "@react-pdf/fns": "npm:3.1.2"
    "@react-pdf/primitives": "npm:^4.1.1"
    "@react-pdf/textkit": "npm:^6.0.0"
    "@react-pdf/types": "npm:^2.9.0"
    abs-svg-path: "npm:^0.1.1"
    color-string: "npm:^1.9.1"
    normalize-svg-path: "npm:^1.1.0"
    parse-svg-path: "npm:^0.1.2"
    svg-arc-to-cubic-bezier: "npm:^3.2.0"
  checksum: 10c0/ac26b4dccf045a44dcd04b714cef2553d924ac985a30b9c50ef9dd4678e7fec2c459ff4b93a5ed60203094408ccdda24a7604e4b5bf944f86c03527dc1735226
  languageName: node
  linkType: hard

"@react-pdf/renderer@npm:^4.3.0":
  version: 4.3.0
  resolution: "@react-pdf/renderer@npm:4.3.0"
  dependencies:
    "@babel/runtime": "npm:^7.20.13"
    "@react-pdf/fns": "npm:3.1.2"
    "@react-pdf/font": "npm:^4.0.2"
    "@react-pdf/layout": "npm:^4.4.0"
    "@react-pdf/pdfkit": "npm:^4.0.3"
    "@react-pdf/primitives": "npm:^4.1.1"
    "@react-pdf/reconciler": "npm:^1.1.4"
    "@react-pdf/render": "npm:^4.3.0"
    "@react-pdf/types": "npm:^2.9.0"
    events: "npm:^3.3.0"
    object-assign: "npm:^4.1.1"
    prop-types: "npm:^15.6.2"
    queue: "npm:^6.0.1"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/71d01dfbd5a113424b2b38030714144e7dea2739f3681b29f83668fdeb8bbcd03f81d0909b6fa0bd203fab6e7cf29dd2e66ac1117e715469ca5bab2b311234f8
  languageName: node
  linkType: hard

"@react-pdf/stylesheet@npm:^6.1.0":
  version: 6.1.0
  resolution: "@react-pdf/stylesheet@npm:6.1.0"
  dependencies:
    "@react-pdf/fns": "npm:3.1.2"
    "@react-pdf/types": "npm:^2.9.0"
    color-string: "npm:^1.9.1"
    hsl-to-hex: "npm:^1.0.0"
    media-engine: "npm:^1.0.3"
    postcss-value-parser: "npm:^4.1.0"
  checksum: 10c0/c0cf5adca79e325c94a13227642ee367fd52f100e2790d5fe83f2f1bf71955249aa6b6cc8e8b72d65de3c400b993547c502578e81124b47ac305024f2ac84acf
  languageName: node
  linkType: hard

"@react-pdf/textkit@npm:^6.0.0":
  version: 6.0.0
  resolution: "@react-pdf/textkit@npm:6.0.0"
  dependencies:
    "@react-pdf/fns": "npm:3.1.2"
    bidi-js: "npm:^1.0.2"
    hyphen: "npm:^1.6.4"
    unicode-properties: "npm:^1.4.1"
  checksum: 10c0/9895980190f2d8eed92c49adb84fbf79f47b2fb5756baa8c83b6a8d51e952ed9bd49ee3746d13a366bb3a5ba078ae1f0d4aaa9511615776e7b4c7a6ad0908566
  languageName: node
  linkType: hard

"@react-pdf/types@npm:^2.9.0":
  version: 2.9.0
  resolution: "@react-pdf/types@npm:2.9.0"
  dependencies:
    "@react-pdf/font": "npm:^4.0.2"
    "@react-pdf/primitives": "npm:^4.1.1"
    "@react-pdf/stylesheet": "npm:^6.1.0"
  checksum: 10c0/6b2236eb53495c2e9aed834b156d267b206c0973307ab07318cd1a0ac874eef1b106f743dd393430ae58fb7f20baeda6fbbd38add5a2a9108ba452b76b8e248c
  languageName: node
  linkType: hard

"@rolldown/pluginutils@npm:1.0.0-beta.27":
  version: 1.0.0-beta.27
  resolution: "@rolldown/pluginutils@npm:1.0.0-beta.27"
  checksum: 10c0/9658f235b345201d4f6bfb1f32da9754ca164f892d1cb68154fe5f53c1df42bd675ecd409836dff46884a7847d6c00bdc38af870f7c81e05bba5c2645eb4ab9c
  languageName: node
  linkType: hard

"@rollup/pluginutils@npm:^4.1.2":
  version: 4.2.1
  resolution: "@rollup/pluginutils@npm:4.2.1"
  dependencies:
    estree-walker: "npm:^2.0.1"
    picomatch: "npm:^2.2.2"
  checksum: 10c0/3ee56b2c8f1ed8dfd0a92631da1af3a2dfdd0321948f089b3752b4de1b54dc5076701eadd0e5fc18bd191b77af594ac1db6279e83951238ba16bf8a414c64c48
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.46.2"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-android-arm64@npm:4.46.2"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-darwin-arm64@npm:4.46.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-darwin-x64@npm:4.46.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-arm64@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-freebsd-arm64@npm:4.46.2"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-x64@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-freebsd-x64@npm:4.46.2"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.46.2"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-musleabihf@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-linux-arm-musleabihf@npm:4.46.2"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.46.2"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.46.2"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-loongarch64-gnu@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-linux-loongarch64-gnu@npm:4.46.2"
  conditions: os=linux & cpu=loong64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-ppc64-gnu@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-linux-ppc64-gnu@npm:4.46.2"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.46.2"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-musl@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-linux-riscv64-musl@npm:4.46.2"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.46.2"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.46.2"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.46.2"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.46.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.46.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.46.2":
  version: 4.46.2
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.46.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@socket.io/component-emitter@npm:~3.1.0":
  version: 3.1.2
  resolution: "@socket.io/component-emitter@npm:3.1.2"
  checksum: 10c0/c4242bad66f67e6f7b712733d25b43cbb9e19a595c8701c3ad99cbeb5901555f78b095e24852f862fffb43e96f1d8552e62def885ca82ae1bb05da3668fd87d7
  languageName: node
  linkType: hard

"@swc/helpers@npm:^0.5.12":
  version: 0.5.17
  resolution: "@swc/helpers@npm:0.5.17"
  dependencies:
    tslib: "npm:^2.8.0"
  checksum: 10c0/fe1f33ebb968558c5a0c595e54f2e479e4609bff844f9ca9a2d1ffd8dd8504c26f862a11b031f48f75c95b0381c2966c3dd156e25942f90089badd24341e7dbb
  languageName: node
  linkType: hard

"@tanstack/history@npm:1.130.12":
  version: 1.130.12
  resolution: "@tanstack/history@npm:1.130.12"
  checksum: 10c0/d9af1fb5d6ad7c9099c17ed6a9d3d5e7b23b270aa28b3cb53badf1ac87fc766c5ba7d878d47ae310f951a0d3ab7181ab1f3177eedf73ef45dadb987a8fa280e7
  languageName: node
  linkType: hard

"@tanstack/react-router@npm:^1.125.6":
  version: 1.130.12
  resolution: "@tanstack/react-router@npm:1.130.12"
  dependencies:
    "@tanstack/history": "npm:1.130.12"
    "@tanstack/react-store": "npm:^0.7.0"
    "@tanstack/router-core": "npm:1.130.12"
    isbot: "npm:^5.1.22"
    tiny-invariant: "npm:^1.3.3"
    tiny-warning: "npm:^1.0.3"
  peerDependencies:
    react: ">=18.0.0 || >=19.0.0"
    react-dom: ">=18.0.0 || >=19.0.0"
  checksum: 10c0/c6ce0fa8e0d06a04c3e75982330e855ca655c0be7f9dcf9ccbff0ea892c14f2551e3454aa467aa2d26511d4a8d99fee9250a3920554d25b4e85e4f0b03b440f7
  languageName: node
  linkType: hard

"@tanstack/react-store@npm:^0.7.0":
  version: 0.7.3
  resolution: "@tanstack/react-store@npm:0.7.3"
  dependencies:
    "@tanstack/store": "npm:0.7.2"
    use-sync-external-store: "npm:^1.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/3487bdf25d43e52f2230f195c4e9266c270af44acbaa7665b87d7d1d809560aa1e84cef8065b0acbe3ca5745a616cd2be3d7bab69c06de6ff57e8eef88ee8499
  languageName: node
  linkType: hard

"@tanstack/router-core@npm:1.130.12":
  version: 1.130.12
  resolution: "@tanstack/router-core@npm:1.130.12"
  dependencies:
    "@tanstack/history": "npm:1.130.12"
    "@tanstack/store": "npm:^0.7.0"
    cookie-es: "npm:^1.2.2"
    seroval: "npm:^1.3.2"
    seroval-plugins: "npm:^1.3.2"
    tiny-invariant: "npm:^1.3.3"
    tiny-warning: "npm:^1.0.3"
  checksum: 10c0/664bef745d3f32f2bea43711954845867eec5ac9ad41c8041256a911d061a76ffe58ef98aeddd7321a12aecdb61383e75a24d88dbe6df988f169dc17e327e512
  languageName: node
  linkType: hard

"@tanstack/router-generator@npm:1.130.15":
  version: 1.130.15
  resolution: "@tanstack/router-generator@npm:1.130.15"
  dependencies:
    "@tanstack/router-core": "npm:1.130.12"
    "@tanstack/router-utils": "npm:1.130.12"
    "@tanstack/virtual-file-routes": "npm:1.129.7"
    prettier: "npm:^3.5.0"
    recast: "npm:^0.23.11"
    source-map: "npm:^0.7.4"
    tsx: "npm:^4.19.2"
    zod: "npm:^3.24.2"
  checksum: 10c0/7ce535acb652dd943ed002b5b8be788e8ab552754c706dfb6aebc48a08b2c3c2e4ca32ece47da6b8052e43dc7f51373e421bc714dd5933d340636de7f4be37ad
  languageName: node
  linkType: hard

"@tanstack/router-plugin@npm:^1.125.6":
  version: 1.130.15
  resolution: "@tanstack/router-plugin@npm:1.130.15"
  dependencies:
    "@babel/core": "npm:^7.27.7"
    "@babel/plugin-syntax-jsx": "npm:^7.27.1"
    "@babel/plugin-syntax-typescript": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.27.7"
    "@babel/types": "npm:^7.27.7"
    "@tanstack/router-core": "npm:1.130.12"
    "@tanstack/router-generator": "npm:1.130.15"
    "@tanstack/router-utils": "npm:1.130.12"
    "@tanstack/virtual-file-routes": "npm:1.129.7"
    babel-dead-code-elimination: "npm:^1.0.10"
    chokidar: "npm:^3.6.0"
    unplugin: "npm:^2.1.2"
    zod: "npm:^3.24.2"
  peerDependencies:
    "@rsbuild/core": ">=1.0.2"
    "@tanstack/react-router": ^1.130.12
    vite: ">=5.0.0 || >=6.0.0"
    vite-plugin-solid: ^2.11.2
    webpack: ">=5.92.0"
  peerDependenciesMeta:
    "@rsbuild/core":
      optional: true
    "@tanstack/react-router":
      optional: true
    vite:
      optional: true
    vite-plugin-solid:
      optional: true
    webpack:
      optional: true
  checksum: 10c0/474a17c08a6cccdfcff5bc0f2864871465b141b4c24dd2fc7411316ef12b6d5f0f5ec140f6ce22f3a9f65811382a4f2a9614bf9683228dc9fcd99cbdfa5742f4
  languageName: node
  linkType: hard

"@tanstack/router-utils@npm:1.130.12":
  version: 1.130.12
  resolution: "@tanstack/router-utils@npm:1.130.12"
  dependencies:
    "@babel/core": "npm:^7.27.4"
    "@babel/generator": "npm:^7.27.5"
    "@babel/parser": "npm:^7.27.5"
    "@babel/preset-typescript": "npm:^7.27.1"
    ansis: "npm:^4.1.0"
    diff: "npm:^8.0.2"
  checksum: 10c0/4e28a18172038eacbfc98b86ec24f89b540b7984b6ceb1a01399b9c736759f918470aa3d8bb7d2fbbcaa0ee25b2d9e177c749a2fe1e06ae0715eb721c4013670
  languageName: node
  linkType: hard

"@tanstack/store@npm:0.7.2, @tanstack/store@npm:^0.7.0":
  version: 0.7.2
  resolution: "@tanstack/store@npm:0.7.2"
  checksum: 10c0/7b9764d156365d0a65b8be87fc67dcad16a45dd260b43e7c90162b39a940a1c764701e6b5f9e5e1e34ec77c86348332db343c658901f67972119d6f1ddf58f1f
  languageName: node
  linkType: hard

"@tanstack/virtual-file-routes@npm:1.129.7":
  version: 1.129.7
  resolution: "@tanstack/virtual-file-routes@npm:1.129.7"
  checksum: 10c0/67a5730573ba1bd6fc50219d252cfefb1dd32ef9786193125f970927799c65459b8143aa93998b6e3aa20b4d5e9ec38555f3da82dd03cb6b33c12bb7b912794c
  languageName: node
  linkType: hard

"@tootallnate/once@npm:2":
  version: 2.0.0
  resolution: "@tootallnate/once@npm:2.0.0"
  checksum: 10c0/073bfa548026b1ebaf1659eb8961e526be22fa77139b10d60e712f46d2f0f05f4e6c8bec62a087d41088ee9e29faa7f54838568e475ab2f776171003c3920858
  languageName: node
  linkType: hard

"@trivago/prettier-plugin-sort-imports@npm:^5.2.2":
  version: 5.2.2
  resolution: "@trivago/prettier-plugin-sort-imports@npm:5.2.2"
  dependencies:
    "@babel/generator": "npm:^7.26.5"
    "@babel/parser": "npm:^7.26.7"
    "@babel/traverse": "npm:^7.26.7"
    "@babel/types": "npm:^7.26.7"
    javascript-natural-sort: "npm:^0.7.1"
    lodash: "npm:^4.17.21"
  peerDependencies:
    "@vue/compiler-sfc": 3.x
    prettier: 2.x - 3.x
    prettier-plugin-svelte: 3.x
    svelte: 4.x || 5.x
  peerDependenciesMeta:
    "@vue/compiler-sfc":
      optional: true
    prettier-plugin-svelte:
      optional: true
    svelte:
      optional: true
  checksum: 10c0/2a4f0464f1f5a294bcd34558fb053f8263f0c62c4a7fcdd3ce40c9822a68ac8b4d951700ab6d01eb3919efe0ed44e4191997edd494d59679b22db1c0db00474e
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.20.5":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": "npm:^7.20.7"
    "@babel/types": "npm:^7.20.7"
    "@types/babel__generator": "npm:*"
    "@types/babel__template": "npm:*"
    "@types/babel__traverse": "npm:*"
  checksum: 10c0/bdee3bb69951e833a4b811b8ee9356b69a61ed5b7a23e1a081ec9249769117fa83aaaf023bb06562a038eb5845155ff663e2d5c75dd95c1d5ccc91db012868ff
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.27.0
  resolution: "@types/babel__generator@npm:7.27.0"
  dependencies:
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/9f9e959a8792df208a9d048092fda7e1858bddc95c6314857a8211a99e20e6830bdeb572e3587ae8be5429e37f2a96fcf222a9f53ad232f5537764c9e13a2bbd
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": "npm:^7.1.0"
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/cc84f6c6ab1eab1427e90dd2b76ccee65ce940b778a9a67be2c8c39e1994e6f5bbc8efa309f6cea8dc6754994524cd4d2896558df76d92e7a1f46ecffee7112b
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*":
  version: 7.28.0
  resolution: "@types/babel__traverse@npm:7.28.0"
  dependencies:
    "@babel/types": "npm:^7.28.2"
  checksum: 10c0/b52d7d4e8fc6a9018fe7361c4062c1c190f5778cf2466817cb9ed19d69fbbb54f9a85ffedeb748ed8062d2cf7d4cc088ee739848f47c57740de1c48cbf0d0994
  languageName: node
  linkType: hard

"@types/bcryptjs@npm:^3.0.0":
  version: 3.0.0
  resolution: "@types/bcryptjs@npm:3.0.0"
  dependencies:
    bcryptjs: "npm:*"
  checksum: 10c0/5d61ce381736f8252627cf32f2bbc17003c0361c6cf63ac23034a651b9c1edfdbf8f786730816e0b5257a927ffa369658562183a68495eb07ca2ddc77fbb171c
  languageName: node
  linkType: hard

"@types/estree@npm:1.0.8, @types/estree@npm:^1.0.6":
  version: 1.0.8
  resolution: "@types/estree@npm:1.0.8"
  checksum: 10c0/39d34d1afaa338ab9763f37ad6066e3f349444f9052b9676a7cc0252ef9485a41c6d81c9c4e0d26e9077993354edf25efc853f3224dd4b447175ef62bdcc86a5
  languageName: node
  linkType: hard

"@types/fabric@npm:^5.3.10":
  version: 5.3.10
  resolution: "@types/fabric@npm:5.3.10"
  checksum: 10c0/5c3a81246202cbce9421afa09c2daf454d514dd2fa4246b1a9bf64d601a008666aa13dde99a9580817df1b81b6b7fa94da8343af91662ac3d96142c8d5a9a7e6
  languageName: node
  linkType: hard

"@types/file-saver@npm:^2.0.7":
  version: 2.0.7
  resolution: "@types/file-saver@npm:2.0.7"
  checksum: 10c0/c6b88a1aea8eec58469da2a90828fef6e9d5d590c7094fb959783d7c32878af80d39439734f3d41b78355dadb507f606e3d04a29a160c85411c65251e58df847
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10c0/a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:^24.0.10":
  version: 24.2.0
  resolution: "@types/node@npm:24.2.0"
  dependencies:
    undici-types: "npm:~7.10.0"
  checksum: 10c0/0b55af4d7b37fea47bbeffffaff908462fa19ea9b1a18f92d9ed6d8415d97971b254f8cb3f629cd238916e94711fdb6ac939aa750cb353dfd6df6c0339435740
  languageName: node
  linkType: hard

"@types/parse-json@npm:^4.0.0":
  version: 4.0.2
  resolution: "@types/parse-json@npm:4.0.2"
  checksum: 10c0/b1b863ac34a2c2172fbe0807a1ec4d5cb684e48d422d15ec95980b81475fac4fdb3768a8b13eef39130203a7c04340fc167bae057c7ebcafd7dec9fe6c36aeb1
  languageName: node
  linkType: hard

"@types/prop-types@npm:^15.7.15":
  version: 15.7.15
  resolution: "@types/prop-types@npm:15.7.15"
  checksum: 10c0/b59aad1ad19bf1733cf524fd4e618196c6c7690f48ee70a327eb450a42aab8e8a063fbe59ca0a5701aebe2d92d582292c0fb845ea57474f6a15f6994b0e260b2
  languageName: node
  linkType: hard

"@types/raf@npm:^3.4.0":
  version: 3.4.3
  resolution: "@types/raf@npm:3.4.3"
  checksum: 10c0/dea835f0daa399c51db9137f5337dc08a2b4a5f61f645658966ecabaebbbd0fd59551f384a1141e14e22a1cc5a591da7d4d88c60a525ad1399108b6dd2641d75
  languageName: node
  linkType: hard

"@types/react-dom@npm:^19.1.6":
  version: 19.1.7
  resolution: "@types/react-dom@npm:19.1.7"
  peerDependencies:
    "@types/react": ^19.0.0
  checksum: 10c0/8db5751c1567552fe4e1ece9f5823b682f2994ec8d30ed34ba0ef984e3c8ace1435f8be93d02f55c350147e78ac8c4dbcd8ed2c3b6a60f575bc5374f588c51c9
  languageName: node
  linkType: hard

"@types/react-transition-group@npm:^4.4.12":
  version: 4.4.12
  resolution: "@types/react-transition-group@npm:4.4.12"
  peerDependencies:
    "@types/react": "*"
  checksum: 10c0/0441b8b47c69312c89ec0760ba477ba1a0808a10ceef8dc1c64b1013ed78517332c30f18681b0ec0b53542731f1ed015169fed1d127cc91222638ed955478ec7
  languageName: node
  linkType: hard

"@types/react@npm:^19.1.8":
  version: 19.1.9
  resolution: "@types/react@npm:19.1.9"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10c0/b418da4aaf18fbc6df4f1b7096dda7ee152d697cac00d729ffd855b2b44a3a9cfb2ecb017ed20979ea3a7d98a5ce5fcf01ac1a3614d4a3e4d7069a0c45e49b0f
  languageName: node
  linkType: hard

"@types/trusted-types@npm:^2.0.7":
  version: 2.0.7
  resolution: "@types/trusted-types@npm:2.0.7"
  checksum: 10c0/4c4855f10de7c6c135e0d32ce462419d8abbbc33713b31d294596c0cc34ae1fa6112a2f9da729c8f7a20707782b0d69da3b1f8df6645b0366d08825ca1522e0c
  languageName: node
  linkType: hard

"@types/watch@npm:^1":
  version: 1.0.6
  resolution: "@types/watch@npm:1.0.6"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/60e07238bfd265fa034b61f7f85f58f4131cb2b4e35012e8f1afe0b695c3c64c00cb5051c6c2ea5604950804ed43eaf1b0064f04c7ca56eb2e13c6c46ce27ee7
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/eslint-plugin@npm:8.39.0"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.10.0"
    "@typescript-eslint/scope-manager": "npm:8.39.0"
    "@typescript-eslint/type-utils": "npm:8.39.0"
    "@typescript-eslint/utils": "npm:8.39.0"
    "@typescript-eslint/visitor-keys": "npm:8.39.0"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^7.0.0"
    natural-compare: "npm:^1.4.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    "@typescript-eslint/parser": ^8.39.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10c0/c735a99622e2a4a95d89fa02cc47e65279f61972a68b62f58c32a384e766473289b6234cdaa34b5caa9372d4bdf1b22ad34b45feada482c4ed7320784fa19312
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/parser@npm:8.39.0"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:8.39.0"
    "@typescript-eslint/types": "npm:8.39.0"
    "@typescript-eslint/typescript-estree": "npm:8.39.0"
    "@typescript-eslint/visitor-keys": "npm:8.39.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10c0/cb437362ea80303e728eccada1ba630769e90d863471d2cb65abbeda540679f93a566bb4ecdcd3aca39c01f48f865a70aed3e94fbaacc6a81e79bb804c596f0b
  languageName: node
  linkType: hard

"@typescript-eslint/project-service@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/project-service@npm:8.39.0"
  dependencies:
    "@typescript-eslint/tsconfig-utils": "npm:^8.39.0"
    "@typescript-eslint/types": "npm:^8.39.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10c0/67ac21bcc715d8e3281b8cab36a7e285b01244a48817ea74910186e76e714918dd2e939b465d0e4e9a30c4ceffa6c8946eb9b1f0ec0dab6708c4416d3a66e731
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/scope-manager@npm:8.39.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.39.0"
    "@typescript-eslint/visitor-keys": "npm:8.39.0"
  checksum: 10c0/ae61721e85fa67f64cab02db88599a6e78e9395dd13c211ab60c5728abdf01b9ceb970c0722671d1958e83c8f00a8ee4f9b3a5c462ea21fb117729b73d53a7e7
  languageName: node
  linkType: hard

"@typescript-eslint/tsconfig-utils@npm:8.39.0, @typescript-eslint/tsconfig-utils@npm:^8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/tsconfig-utils@npm:8.39.0"
  peerDependencies:
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10c0/1437c0004d4d852128c72559232470e82c9b9635156c6d8eec7be7c5b08c01e9528cda736587bdaba0d5c71f2f5480855c406f224eab45ba81c6850210280fc3
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/type-utils@npm:8.39.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.39.0"
    "@typescript-eslint/typescript-estree": "npm:8.39.0"
    "@typescript-eslint/utils": "npm:8.39.0"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10c0/918de86cc99e90a74a02ee5dfe26f0d7a22872ac00d84e59630a15f50fa9688c2db545c8bf26ba8923c72a74c09386b994d0b7da3dac4104da4ca8c80b4353ac
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.39.0, @typescript-eslint/types@npm:^8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/types@npm:8.39.0"
  checksum: 10c0/4240b01b218f3ef8a4f6343cb78cd531c12b2a134b6edd6ab67a9de4d1808790bc468f7579d5d38e507a206457d14a5e8970f6f74d29b9858633f77258f7e43b
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/typescript-estree@npm:8.39.0"
  dependencies:
    "@typescript-eslint/project-service": "npm:8.39.0"
    "@typescript-eslint/tsconfig-utils": "npm:8.39.0"
    "@typescript-eslint/types": "npm:8.39.0"
    "@typescript-eslint/visitor-keys": "npm:8.39.0"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10c0/9eaf44af35b7bd8a8298909c0b2153f4c69e582b86f84dbe4a58c6afb6496253e955ee2b6ff0517e7717a6e8557537035ce631e0aa10fa848354a15620c387d2
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/utils@npm:8.39.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.7.0"
    "@typescript-eslint/scope-manager": "npm:8.39.0"
    "@typescript-eslint/types": "npm:8.39.0"
    "@typescript-eslint/typescript-estree": "npm:8.39.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10c0/61956004dea90835b9f8de581019bc4f360dd44cebb9e0f8014ede39fc7cbc71d7d0093a65547bea004a865a1eff81dfd822520ba0a37e636f359291c27e1bd2
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.39.0":
  version: 8.39.0
  resolution: "@typescript-eslint/visitor-keys@npm:8.39.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.39.0"
    eslint-visitor-keys: "npm:^4.2.1"
  checksum: 10c0/657766d4e9ad01e8fd8e8fd39f8f3d043ecdffb78f1ab9653acbed3c971e221b1f680e90752394308c532703211f9f441bb449f62c0f61a48750b24ccb4379ef
  languageName: node
  linkType: hard

"@vitejs/plugin-react@npm:^4.6.0":
  version: 4.7.0
  resolution: "@vitejs/plugin-react@npm:4.7.0"
  dependencies:
    "@babel/core": "npm:^7.28.0"
    "@babel/plugin-transform-react-jsx-self": "npm:^7.27.1"
    "@babel/plugin-transform-react-jsx-source": "npm:^7.27.1"
    "@rolldown/pluginutils": "npm:1.0.0-beta.27"
    "@types/babel__core": "npm:^7.20.5"
    react-refresh: "npm:^0.17.0"
  peerDependencies:
    vite: ^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0
  checksum: 10c0/692f23960972879485d647713663ec299c478222c96567d60285acf7c7dc5c178e71abfe9d2eefddef1eeb01514dacbc2ed68aad84628debf9c7116134734253
  languageName: node
  linkType: hard

"@webcomponents/custom-elements@npm:^1.5.0":
  version: 1.6.0
  resolution: "@webcomponents/custom-elements@npm:1.6.0"
  checksum: 10c0/8c3c3b0250ad7b063fe92b550fb725cc6074c8c5caea4a80901f9d9a93cdacf6dc0c73f715fa7b16f86e2ca1630e43cd80499bbf80e3a9b5c6ec042e074d22b4
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:^0.8.6":
  version: 0.8.10
  resolution: "@xmldom/xmldom@npm:0.8.10"
  checksum: 10c0/c7647c442502720182b0d65b17d45d2d95317c1c8c497626fe524bda79b4fb768a9aa4fae2da919f308e7abcff7d67c058b102a9d641097e9a57f0b80187851f
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:^0.9.8":
  version: 0.9.8
  resolution: "@xmldom/xmldom@npm:0.9.8"
  checksum: 10c0/2ea984270832de2843ab0bbb6df71bde9aa02126b69e5fd56b5512b98ace48e94aff7186e77d0b22fe4b6780483862be752bcf8577436638a9210109029a0503
  languageName: node
  linkType: hard

"abab@npm:^2.0.6":
  version: 2.0.6
  resolution: "abab@npm:2.0.6"
  checksum: 10c0/0b245c3c3ea2598fe0025abf7cc7bb507b06949d51e8edae5d12c1b847a0a0c09639abcb94788332b4e2044ac4491c1e8f571b51c7826fd4b0bda1685ad4a278
  languageName: node
  linkType: hard

"abbrev@npm:1":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: 10c0/3f762677702acb24f65e813070e306c61fafe25d4b2583f9dfc935131f774863f3addd5741572ed576bd69cabe473c5af18e1e108b829cb7b6b4747884f726e6
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10c0/21ba8f574ea57a3106d6d35623f2c4a9111d9ee3e9a5be47baed46ec2457d2eac46e07a5c4a60186f88cb98abbe3e24f2d4cca70bc2b12f1692523e2209a9ccf
  languageName: node
  linkType: hard

"abs-svg-path@npm:^0.1.1":
  version: 0.1.1
  resolution: "abs-svg-path@npm:0.1.1"
  checksum: 10c0/aa763f3843cd4d7c3eabcddc91834ab27def4c1f470b98a3bf01ebe82928629c5aeaa97766252781449e0c722e1785f8e512fea79f86d3d10f8eca220d6aa292
  languageName: node
  linkType: hard

"accepts@npm:~1.3.4, accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10c0/3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"acorn-dynamic-import@npm:^2.0.0":
  version: 2.0.2
  resolution: "acorn-dynamic-import@npm:2.0.2"
  dependencies:
    acorn: "npm:^4.0.3"
  checksum: 10c0/5dcac220ae8ddc8f8439402691c6da21f4e44f3a91b5645884cd2a5f03c8e39a40006a189de9ccd50fdfcdeda6c52724db45cd2100e56e9e9656d7b19e2bfd22
  languageName: node
  linkType: hard

"acorn-globals@npm:^7.0.0":
  version: 7.0.1
  resolution: "acorn-globals@npm:7.0.1"
  dependencies:
    acorn: "npm:^8.1.0"
    acorn-walk: "npm:^8.0.2"
  checksum: 10c0/7437f58e92d99292dbebd0e79531af27d706c9f272f31c675d793da6c82d897e75302a8744af13c7f7978a8399840f14a353b60cf21014647f71012982456d2b
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/4c54868fbef3b8d58927d5e33f0a4de35f59012fe7b12cf9dfbb345fb8f46607709e1c4431be869a23fb63c151033d84c4198fa9f79385cec34fcb1dd53974c1
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.0.2, acorn-walk@npm:^8.2.0":
  version: 8.3.4
  resolution: "acorn-walk@npm:8.3.4"
  dependencies:
    acorn: "npm:^8.11.0"
  checksum: 10c0/76537ac5fb2c37a64560feaf3342023dadc086c46da57da363e64c6148dc21b57d49ace26f949e225063acb6fb441eabffd89f7a3066de5ad37ab3e328927c62
  languageName: node
  linkType: hard

"acorn@npm:^4.0.3":
  version: 4.0.13
  resolution: "acorn@npm:4.0.13"
  bin:
    acorn: ./bin/acorn
  checksum: 10c0/ed73e42711474f80d7f2f94d68109e83a40d48c9596f0bcfd1a4c64e086636495259432421357524d04bfe317eed6de5b6202d2ab30f1b1c26bca12684979aa5
  languageName: node
  linkType: hard

"acorn@npm:^5.0.0":
  version: 5.7.4
  resolution: "acorn@npm:5.7.4"
  bin:
    acorn: bin/acorn
  checksum: 10c0/b29e61d48fa31ae69d38d74bb213b77b32de6317f125890a6cb76b44d173adccbcd3a07fc9a86acdfe8ab0a80f42b5ec6290df8b7944e0506504ac3b716232bd
  languageName: node
  linkType: hard

"acorn@npm:^8.1.0, acorn@npm:^8.11.0, acorn@npm:^8.14.1, acorn@npm:^8.15.0, acorn@npm:^8.8.1":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 10c0/dec73ff59b7d6628a01eebaece7f2bdb8bb62b9b5926dcad0f8931f2b8b79c2be21f6c68ac095592adb5adb15831a3635d9343e6a91d028bbe85d564875ec3ec
  languageName: node
  linkType: hard

"agent-base@npm:6":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: "npm:4"
  checksum: 10c0/dc4f757e40b5f3e3d674bc9beb4f1048f4ee83af189bae39be99f57bf1f48dde166a8b0a5342a84b5944ee8e6ed1e5a9d801858f4ad44764e84957122fe46261
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.4
  resolution: "agent-base@npm:7.1.4"
  checksum: 10c0/c2c9ab7599692d594b6a161559ada307b7a624fa4c7b03e3afdb5a5e31cd0e53269115b620fcab024c5ac6a6f37fa5eb2e004f076ad30f5f7e6b8b671f7b35fe
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.1.0":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 10c0/0c57a47cbd656e8cdfd99d7c2264de5868918ffa207c8d7a72a7f63379d4333254b2ba03d69e3c035e996a3fd3eb6d5725d7a1597cca10694296e32510546360
  languageName: node
  linkType: hard

"ajv@npm:^6.1.0, ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"align-text@npm:^0.1.1, align-text@npm:^0.1.3":
  version: 0.1.4
  resolution: "align-text@npm:0.1.4"
  dependencies:
    kind-of: "npm:^3.0.2"
    longest: "npm:^1.0.1"
    repeat-string: "npm:^1.5.2"
  checksum: 10c0/c0fc03fe5de15cda89f9babb91e77a255013b49912031e86e79f25547aa666622f0a68be3da47fc834ab2c97cfb6b0a967509b2ce883f5306d9c78f6069ced7a
  languageName: node
  linkType: hard

"ansi-html@npm:0.0.7":
  version: 0.0.7
  resolution: "ansi-html@npm:0.0.7"
  bin:
    ansi-html: ./bin/ansi-html
  checksum: 10c0/f6d3072422dc8d4c795142fd4ee8ee596538ddd02ac23676ec6c61dc61c1149f61acfc651b28ff49e7828a6372d4adab2d94d14e95feff73f656388803e13929
  languageName: node
  linkType: hard

"ansi-regex@npm:^2.0.0":
  version: 2.1.1
  resolution: "ansi-regex@npm:2.1.1"
  checksum: 10c0/78cebaf50bce2cb96341a7230adf28d804611da3ce6bf338efa7b72f06cc6ff648e29f80cd95e582617ba58d5fdbec38abfeed3500a98bce8381a9daec7c548b
  languageName: node
  linkType: hard

"ansi-regex@npm:^3.0.0":
  version: 3.0.1
  resolution: "ansi-regex@npm:3.0.1"
  checksum: 10c0/d108a7498b8568caf4a46eea4f1784ab4e0dfb2e3f3938c697dee21443d622d765c958f2b7e2b9f6b9e55e2e2af0584eaa9915d51782b89a841c28e744e7a167
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"ansis@npm:^4.1.0":
  version: 4.1.0
  resolution: "ansis@npm:4.1.0"
  checksum: 10c0/df62d017a7791babdaf45b93f930d2cfd6d1dab5568b610735c11434c9a5ef8f513740e7cfd80bcbc3530fc8bd892b88f8476f26621efc251230e53cbd1a2c24
  languageName: node
  linkType: hard

"anymatch@npm:^2.0.0":
  version: 2.0.0
  resolution: "anymatch@npm:2.0.0"
  dependencies:
    micromatch: "npm:^3.1.4"
    normalize-path: "npm:^2.1.1"
  checksum: 10c0/a0d745e52f0233048724b9c9d7b1d8a650f7a50151a0f1d2cce1857b09fd096052d334f8c570cc88596edef8249ae778f767db94025cd00f81e154a37bb7e34e
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.1.0
  resolution: "aproba@npm:2.1.0"
  checksum: 10c0/ec8c1d351bac0717420c737eb062766fb63bde1552900e0f4fdad9eb064c3824fef23d1c416aa5f7a80f21ca682808e902d79b7c9ae756d342b5f1884f36932f
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^2.0.0":
  version: 2.0.0
  resolution: "are-we-there-yet@npm:2.0.0"
  dependencies:
    delegates: "npm:^1.0.0"
    readable-stream: "npm:^3.6.0"
  checksum: 10c0/375f753c10329153c8d66dc95e8f8b6c7cc2aa66e05cb0960bd69092b10dae22900cacc7d653ad11d26b3ecbdbfe1e8bfb6ccf0265ba8077a7d979970f16b99c
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"argparse@npm:~1.0.3":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10c0/b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"arr-diff@npm:^4.0.0":
  version: 4.0.0
  resolution: "arr-diff@npm:4.0.0"
  checksum: 10c0/67b80067137f70c89953b95f5c6279ad379c3ee39f7143578e13bd51580a40066ee2a55da066e22d498dce10f68c2d70056d7823f972fab99dfbf4c78d0bc0f7
  languageName: node
  linkType: hard

"arr-flatten@npm:^1.1.0":
  version: 1.1.0
  resolution: "arr-flatten@npm:1.1.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"arr-union@npm:^3.1.0":
  version: 3.1.0
  resolution: "arr-union@npm:3.1.0"
  checksum: 10c0/7d5aa05894e54aa93c77c5726c1dd5d8e8d3afe4f77983c0aa8a14a8a5cbe8b18f0cf4ecaa4ac8c908ef5f744d2cbbdaa83fd6e96724d15fea56cfa7f5efdd51
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    is-array-buffer: "npm:^3.0.5"
  checksum: 10c0/74e1d2d996941c7a1badda9cabb7caab8c449db9086407cad8a1b71d2604cc8abf105db8ca4e02c04579ec58b7be40279ddb09aea4784832984485499f48432d
  languageName: node
  linkType: hard

"array-find-index@npm:^1.0.1":
  version: 1.0.2
  resolution: "array-find-index@npm:1.0.2"
  checksum: 10c0/86b9485c74ddd324feab807e10a6de3f9c1683856267236fac4bb4d4667ada6463e106db3f6c540ae6b720e0442b590ec701d13676df4c6af30ebf4da09b4f57
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-flatten@npm:^2.1.0":
  version: 2.1.2
  resolution: "array-flatten@npm:2.1.2"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-includes@npm:^3.0.3, array-includes@npm:^3.1.6, array-includes@npm:^3.1.8":
  version: 3.1.9
  resolution: "array-includes@npm:3.1.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.24.0"
    es-object-atoms: "npm:^1.1.1"
    get-intrinsic: "npm:^1.3.0"
    is-string: "npm:^1.1.1"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/0235fa69078abeac05ac4250699c44996bc6f774a9cbe45db48674ce6bd142f09b327d31482ff75cf03344db4ea03eae23edb862d59378b484b47ed842574856
  languageName: node
  linkType: hard

"array-union@npm:^1.0.1":
  version: 1.0.2
  resolution: "array-union@npm:1.0.2"
  dependencies:
    array-uniq: "npm:^1.0.1"
  checksum: 10c0/18686767c0cfdae8dc4acf5ac119b0f0eacad82b7fcc0aa62cc41f93c5ad406d494b6a6e53d85e52e8f0349b67a4fec815feeb537e95c02510d747bc9a4157c7
  languageName: node
  linkType: hard

"array-uniq@npm:^1.0.1":
  version: 1.0.3
  resolution: "array-uniq@npm:1.0.3"
  checksum: 10c0/3acbaf9e6d5faeb1010e2db04ab171b8d265889e46c61762e502979bdc5e55656013726e9a61507de3c82d329a0dc1e8072630a3454b4f2b881cb19ba7fd8aa6
  languageName: node
  linkType: hard

"array-unique@npm:^0.3.2":
  version: 0.3.2
  resolution: "array-unique@npm:0.3.2"
  checksum: 10c0/dbf4462cdba8a4b85577be07705210b3d35be4b765822a3f52962d907186617638ce15e0603a4fefdcf82f4cbbc9d433f8cbbd6855148a68872fa041b6474121
  languageName: node
  linkType: hard

"array.prototype.findlast@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlast@npm:1.2.5"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/ddc952b829145ab45411b9d6adcb51a8c17c76bf89c9dd64b52d5dffa65d033da8c076ed2e17091779e83bc892b9848188d7b4b33453c5565e65a92863cb2775
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1":
  version: 1.3.3
  resolution: "array.prototype.flat@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/d90e04dfbc43bb96b3d2248576753d1fb2298d2d972e29ca7ad5ec621f0d9e16ff8074dae647eac4f31f4fb7d3f561a7ac005fb01a71f51705a13b5af06a7d8a
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.3":
  version: 1.3.3
  resolution: "array.prototype.flatmap@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/ba899ea22b9dc9bf276e773e98ac84638ed5e0236de06f13d63a90b18ca9e0ec7c97d622d899796e3773930b946cd2413d098656c0c5d8cc58c6f25c21e6bd54
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.4":
  version: 1.1.4
  resolution: "array.prototype.tosorted@npm:1.1.4"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.3"
    es-errors: "npm:^1.3.0"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10c0/eb3c4c4fc0381b0bf6dba2ea4d48d367c2827a0d4236a5718d97caaccc6b78f11f4cadf090736e86301d295a6aa4967ed45568f92ced51be8cbbacd9ca410943
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.1"
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    is-array-buffer: "npm:^3.0.4"
  checksum: 10c0/2f2459caa06ae0f7f615003f9104b01f6435cc803e11bd2a655107d52a1781dc040532dc44d93026b694cc18793993246237423e13a5337e86b43ed604932c06
  languageName: node
  linkType: hard

"asn1.js@npm:^4.10.1":
  version: 4.10.1
  resolution: "asn1.js@npm:4.10.1"
  dependencies:
    bn.js: "npm:^4.0.0"
    inherits: "npm:^2.0.1"
    minimalistic-assert: "npm:^1.0.0"
  checksum: 10c0/afa7f3ab9e31566c80175a75b182e5dba50589dcc738aa485be42bdd787e2a07246a4b034d481861123cbe646a7656f318f4f1cad2e9e5e808a210d5d6feaa88
  languageName: node
  linkType: hard

"assert@npm:^1.1.1":
  version: 1.5.1
  resolution: "assert@npm:1.5.1"
  dependencies:
    object.assign: "npm:^4.1.4"
    util: "npm:^0.10.4"
  checksum: 10c0/836688b928b68b7fc5bbc165443e16a62623d57676a1e8a980a0316f9ae86e5e0a102c63470491bf55a8545e75766303640c0c7ad1cf6bfa5450130396043bbd
  languageName: node
  linkType: hard

"assign-symbols@npm:^1.0.0":
  version: 1.0.0
  resolution: "assign-symbols@npm:1.0.0"
  checksum: 10c0/29a654b8a6da6889a190d0d0efef4b1bfb5948fa06cbc245054aef05139f889f2f7c75b989917e3fde853fc4093b88048e4de8578a73a76f113d41bfd66e5775
  languageName: node
  linkType: hard

"ast-types@npm:^0.16.1":
  version: 0.16.1
  resolution: "ast-types@npm:0.16.1"
  dependencies:
    tslib: "npm:^2.0.1"
  checksum: 10c0/abcc49e42eb921a7ebc013d5bec1154651fb6dbc3f497541d488859e681256901b2990b954d530ba0da4d0851271d484f7057d5eff5e07cb73e8b10909f711bf
  languageName: node
  linkType: hard

"async-each@npm:^1.0.1":
  version: 1.0.6
  resolution: "async-each@npm:1.0.6"
  checksum: 10c0/d4e45e8f077e20e015952c065ceae75f82b30ee2d4a8e56a5c454ae44331aaa009d8c94fe043ba254c177bffae9f6ebeefebb7daf9f7ce4d27fac0274dc328ae
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 10c0/669a32c2cb7e45091330c680e92eaeb791bc1d4132d827591e499cd1f776ff5a873e77e5f92d0ce795a8d60f10761dec9ddfe7225a5de680f5d357f67b1aac73
  languageName: node
  linkType: hard

"async@npm:^2.1.2":
  version: 2.6.4
  resolution: "async@npm:2.6.4"
  dependencies:
    lodash: "npm:^4.17.14"
  checksum: 10c0/0ebb3273ef96513389520adc88e0d3c45e523d03653cc9b66f5c46f4239444294899bfd13d2b569e7dbfde7da2235c35cf5fd3ece9524f935d41bbe4efccdad0
  languageName: node
  linkType: hard

"async@npm:^3.2.6":
  version: 3.2.6
  resolution: "async@npm:3.2.6"
  checksum: 10c0/36484bb15ceddf07078688d95e27076379cc2f87b10c03b6dd8a83e89475a3c8df5848859dd06a4c95af1e4c16fc973de0171a77f18ea00be899aca2a4f85e70
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"atob@npm:^2.1.2":
  version: 2.1.2
  resolution: "atob@npm:2.1.2"
  bin:
    atob: bin/atob.js
  checksum: 10c0/ada635b519dc0c576bb0b3ca63a73b50eefacf390abb3f062558342a8d68f2db91d0c8db54ce81b0d89de3b0f000de71f3ae7d761fd7d8cc624278fe443d6c7e
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: "npm:^1.0.0"
  checksum: 10c0/d07226ef4f87daa01bd0fe80f8f310982e345f372926da2e5296aecc25c41cab440916bbaa4c5e1034b453af3392f67df5961124e4b586df1e99793a1374bdb2
  languageName: node
  linkType: hard

"axios@npm:^1.10.0":
  version: 1.11.0
  resolution: "axios@npm:1.11.0"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.4"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10c0/5de273d33d43058610e4d252f0963cc4f10714da0bfe872e8ef2cbc23c2c999acc300fd357b6bce0fc84a2ca9bd45740fa6bb28199ce2c1266c8b1a393f2b36e
  languageName: node
  linkType: hard

"babel-dead-code-elimination@npm:^1.0.10":
  version: 1.0.10
  resolution: "babel-dead-code-elimination@npm:1.0.10"
  dependencies:
    "@babel/core": "npm:^7.23.7"
    "@babel/parser": "npm:^7.23.6"
    "@babel/traverse": "npm:^7.23.7"
    "@babel/types": "npm:^7.23.6"
  checksum: 10c0/9503662f28cf8f86e7a27c5cc1fa63fc556100cd3bc6f1a4382aa8e9c6df54b15d2e0fcc073016f315d26a9e4004bc4d70829a395f056172b8f9240314da8973
  languageName: node
  linkType: hard

"babel-plugin-macros@npm:^3.1.0":
  version: 3.1.0
  resolution: "babel-plugin-macros@npm:3.1.0"
  dependencies:
    "@babel/runtime": "npm:^7.12.5"
    cosmiconfig: "npm:^7.0.0"
    resolve: "npm:^1.19.0"
  checksum: 10c0/c6dfb15de96f67871d95bd2e8c58b0c81edc08b9b087dc16755e7157f357dc1090a8dc60ebab955e92587a9101f02eba07e730adc253a1e4cf593ca3ebd3839c
  languageName: node
  linkType: hard

"babel-plugin-styled-components@npm:>= 1.12.0":
  version: 2.1.4
  resolution: "babel-plugin-styled-components@npm:2.1.4"
  dependencies:
    "@babel/helper-annotate-as-pure": "npm:^7.22.5"
    "@babel/helper-module-imports": "npm:^7.22.5"
    "@babel/plugin-syntax-jsx": "npm:^7.22.5"
    lodash: "npm:^4.17.21"
    picomatch: "npm:^2.3.1"
  peerDependencies:
    styled-components: ">= 2"
  checksum: 10c0/553f35f5feb4b51fda9c9aeef8a31c1b66f430687ab17830b7cdacfe7e93f912aef55bf59e402f4e0a1fa7ad039768ab3626512bbb9bf1f76fcc67ba47e7a56e
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"base64-arraybuffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "base64-arraybuffer@npm:1.0.2"
  checksum: 10c0/3acac95c70f9406e87a41073558ba85b6be9dbffb013a3d2a710e3f2d534d506c911847d5d9be4de458af6362c676de0a5c4c2d7bdf4def502d00b313368e72f
  languageName: node
  linkType: hard

"base64-js@npm:0.0.8":
  version: 0.0.8
  resolution: "base64-js@npm:0.0.8"
  checksum: 10c0/60f02a9fdbbbb251c0a1064834d4062f5a3c4237edd9f0313282d75980a80ce303316795f7a80c8e240e524169644d88445ec0697b03f81ab9f4458090979375
  languageName: node
  linkType: hard

"base64-js@npm:^1.0.2, base64-js@npm:^1.1.2, base64-js@npm:^1.3.0, base64-js@npm:^1.5.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"base@npm:^0.11.1":
  version: 0.11.2
  resolution: "base@npm:0.11.2"
  dependencies:
    cache-base: "npm:^1.0.1"
    class-utils: "npm:^0.3.5"
    component-emitter: "npm:^1.2.1"
    define-property: "npm:^1.0.0"
    isobject: "npm:^3.0.1"
    mixin-deep: "npm:^1.2.0"
    pascalcase: "npm:^0.1.1"
  checksum: 10c0/30a2c0675eb52136b05ef496feb41574d9f0bb2d6d677761da579c00a841523fccf07f1dbabec2337b5f5750f428683b8ca60d89e56a1052c4ae1c0cd05de64d
  languageName: node
  linkType: hard

"batch@npm:0.6.1":
  version: 0.6.1
  resolution: "batch@npm:0.6.1"
  checksum: 10c0/925a13897b4db80d4211082fe287bcf96d297af38e26448c857cee3e095c9792e3b8f26b37d268812e7f38a589f694609de8534a018b1937d7dc9f84e6b387c5
  languageName: node
  linkType: hard

"bcryptjs@npm:*, bcryptjs@npm:^3.0.2":
  version: 3.0.2
  resolution: "bcryptjs@npm:3.0.2"
  bin:
    bcrypt: bin/bcrypt
  checksum: 10c0/a0923cac99f83e913f8f4e4f42df6a27c6593b24d509900331d1280c4050b1544e602a0ac67b43f7bb5c969991c3ed77fd72f19b7dc873be8ee794da3d925c7e
  languageName: node
  linkType: hard

"bidi-js@npm:^1.0.2":
  version: 1.0.3
  resolution: "bidi-js@npm:1.0.3"
  dependencies:
    require-from-string: "npm:^2.0.2"
  checksum: 10c0/fdddea4aa4120a34285486f2267526cd9298b6e8b773ad25e765d4f104b6d7437ab4ba542e6939e3ac834a7570bcf121ee2cf6d3ae7cd7082c4b5bedc8f271e1
  languageName: node
  linkType: hard

"big.js@npm:^5.2.2":
  version: 5.2.2
  resolution: "big.js@npm:5.2.2"
  checksum: 10c0/230520f1ff920b2d2ce3e372d77a33faa4fa60d802fe01ca4ffbc321ee06023fe9a741ac02793ee778040a16b7e497f7d60c504d1c402b8fdab6f03bb785a25f
  languageName: node
  linkType: hard

"binary-extensions@npm:^1.0.0":
  version: 1.13.1
  resolution: "binary-extensions@npm:1.13.1"
  checksum: 10c0/2d616938ac23d828ec3fbe0dea429b566fd2c137ddc38f166f16561ccd58029deac3fa9fddb489ab13d679c8fb5f1bd0e82824041299e5e39d8dd3cc68fbb9f9
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10c0/75a59cafc10fb12a11d510e77110c6c7ae3f4ca22463d52487709ca7f18f69d886aa387557cc9864fbdb10153d0bdb4caacabf11541f55e89ed6e18d12ece2b5
  languageName: node
  linkType: hard

"bindings@npm:^1.5.0":
  version: 1.5.0
  resolution: "bindings@npm:1.5.0"
  dependencies:
    file-uri-to-path: "npm:1.0.0"
  checksum: 10c0/3dab2491b4bb24124252a91e656803eac24292473e56554e35bbfe3cc1875332cfa77600c3bac7564049dc95075bf6fcc63a4609920ff2d64d0fe405fcf0d4ba
  languageName: node
  linkType: hard

"bluebird@npm:~3.4.0":
  version: 3.4.7
  resolution: "bluebird@npm:3.4.7"
  checksum: 10c0/ac7e3df09a433b985a0ba61a0be4fc23e3874bf62440ffbca2f275a8498b00c11336f1f633631f38419b2c842515473985f9c4aaa9e4c9b36105535026d94144
  languageName: node
  linkType: hard

"bn.js@npm:^4.0.0, bn.js@npm:^4.1.0, bn.js@npm:^4.11.9":
  version: 4.12.2
  resolution: "bn.js@npm:4.12.2"
  checksum: 10c0/09a249faa416a9a1ce68b5f5ec8bbca87fe54e5dd4ef8b1cc8a4969147b80035592bddcb1e9cc814c3ba79e573503d5c5178664b722b509fb36d93620dba9b57
  languageName: node
  linkType: hard

"bn.js@npm:^5.2.1":
  version: 5.2.2
  resolution: "bn.js@npm:5.2.2"
  checksum: 10c0/cb97827d476aab1a0194df33cd84624952480d92da46e6b4a19c32964aa01553a4a613502396712704da2ec8f831cf98d02e74ca03398404bd78a037ba93f2ab
  languageName: node
  linkType: hard

"body-parser@npm:1.20.3":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.5"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.13.0"
    raw-body: "npm:2.5.2"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 10c0/0a9a93b7518f222885498dcecaad528cf010dd109b071bf471c93def4bfe30958b83e03496eb9c1ad4896db543d999bb62be1a3087294162a88cfa1b42c16310
  languageName: node
  linkType: hard

"bonjour@npm:^3.5.0":
  version: 3.5.0
  resolution: "bonjour@npm:3.5.0"
  dependencies:
    array-flatten: "npm:^2.1.0"
    deep-equal: "npm:^1.0.1"
    dns-equal: "npm:^1.0.0"
    dns-txt: "npm:^2.0.2"
    multicast-dns: "npm:^6.0.1"
    multicast-dns-service-types: "npm:^1.1.0"
  checksum: 10c0/0be7c4cd96df563571973706226e750f6feeacd81d01c1ade11247eb3a7e14846af49cffe397ab970059b828dd89f694f456e22bca4ca315a7f0326e9303e241
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 10c0/e4b53deb4f2b85c52be0e21a273f2045c7b6a6ea002b0e139c744cb6f95e9ec044439a52883b0d74dedd1ff3da55ed140cfdddfed7fb0cccbed373de5dce1bcf
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.12
  resolution: "brace-expansion@npm:1.1.12"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/975fecac2bb7758c062c20d0b3b6288c7cc895219ee25f0a64a9de662dbac981ff0b6e89909c3897c1f84fa353113a721923afdec5f8b2350255b097f12b1f73
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/6d117a4c793488af86b83172deb6af143e94c17bc53b0b3cec259733923b4ca84679d506ac261f4ba3c7ed37c46018e2ff442f9ce453af8643ecd64f4a54e6cf
  languageName: node
  linkType: hard

"braces@npm:^2.3.1, braces@npm:^2.3.2":
  version: 2.3.2
  resolution: "braces@npm:2.3.2"
  dependencies:
    arr-flatten: "npm:^1.1.0"
    array-unique: "npm:^0.3.2"
    extend-shallow: "npm:^2.0.1"
    fill-range: "npm:^4.0.0"
    isobject: "npm:^3.0.1"
    repeat-element: "npm:^1.1.2"
    snapdragon: "npm:^0.8.1"
    snapdragon-node: "npm:^2.0.1"
    split-string: "npm:^3.0.2"
    to-regex: "npm:^3.0.1"
  checksum: 10c0/72b27ea3ea2718f061c29e70fd6e17606e37c65f5801abddcf0b0052db1de7d60f3bf92cfc220ab57b44bd0083a5f69f9d03b3461d2816cfe9f9398207acc728
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10c0/7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"brorand@npm:^1.0.1, brorand@npm:^1.1.0":
  version: 1.1.0
  resolution: "brorand@npm:1.1.0"
  checksum: 10c0/6f366d7c4990f82c366e3878492ba9a372a73163c09871e80d82fb4ae0d23f9f8924cb8a662330308206e6b3b76ba1d528b4601c9ef73c2166b440b2ea3b7571
  languageName: node
  linkType: hard

"brotli@npm:^1.3.2":
  version: 1.3.3
  resolution: "brotli@npm:1.3.3"
  dependencies:
    base64-js: "npm:^1.1.2"
  checksum: 10c0/9d24e24f8b7eabf44af034ed5f7d5530008b835f09a107a84ac060723e86dd43c6aa68958691fe5df524f59473b35f5ce2e0854aa1152c0a254d1010f51bcf22
  languageName: node
  linkType: hard

"browserify-aes@npm:^1.0.4, browserify-aes@npm:^1.2.0":
  version: 1.2.0
  resolution: "browserify-aes@npm:1.2.0"
  dependencies:
    buffer-xor: "npm:^1.0.3"
    cipher-base: "npm:^1.0.0"
    create-hash: "npm:^1.1.0"
    evp_bytestokey: "npm:^1.0.3"
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/967f2ae60d610b7b252a4cbb55a7a3331c78293c94b4dd9c264d384ca93354c089b3af9c0dd023534efdc74ffbc82510f7ad4399cf82bc37bc07052eea485f18
  languageName: node
  linkType: hard

"browserify-cipher@npm:^1.0.1":
  version: 1.0.1
  resolution: "browserify-cipher@npm:1.0.1"
  dependencies:
    browserify-aes: "npm:^1.0.4"
    browserify-des: "npm:^1.0.0"
    evp_bytestokey: "npm:^1.0.0"
  checksum: 10c0/aa256dcb42bc53a67168bbc94ab85d243b0a3b56109dee3b51230b7d010d9b78985ffc1fb36e145c6e4db151f888076c1cfc207baf1525d3e375cbe8187fe27d
  languageName: node
  linkType: hard

"browserify-des@npm:^1.0.0":
  version: 1.0.2
  resolution: "browserify-des@npm:1.0.2"
  dependencies:
    cipher-base: "npm:^1.0.1"
    des.js: "npm:^1.0.0"
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.1.2"
  checksum: 10c0/943eb5d4045eff80a6cde5be4e5fbb1f2d5002126b5a4789c3c1aae3cdddb1eb92b00fb92277f512288e5c6af330730b1dbabcf7ce0923e749e151fcee5a074d
  languageName: node
  linkType: hard

"browserify-rsa@npm:^4.0.0, browserify-rsa@npm:^4.1.0":
  version: 4.1.1
  resolution: "browserify-rsa@npm:4.1.1"
  dependencies:
    bn.js: "npm:^5.2.1"
    randombytes: "npm:^2.1.0"
    safe-buffer: "npm:^5.2.1"
  checksum: 10c0/b650ee1192e3d7f3d779edc06dd96ed8720362e72ac310c367b9d7fe35f7e8dbb983c1829142b2b3215458be8bf17c38adc7224920843024ed8cf39e19c513c0
  languageName: node
  linkType: hard

"browserify-sign@npm:^4.2.3":
  version: 4.2.3
  resolution: "browserify-sign@npm:4.2.3"
  dependencies:
    bn.js: "npm:^5.2.1"
    browserify-rsa: "npm:^4.1.0"
    create-hash: "npm:^1.2.0"
    create-hmac: "npm:^1.1.7"
    elliptic: "npm:^6.5.5"
    hash-base: "npm:~3.0"
    inherits: "npm:^2.0.4"
    parse-asn1: "npm:^5.1.7"
    readable-stream: "npm:^2.3.8"
    safe-buffer: "npm:^5.2.1"
  checksum: 10c0/30c0eba3f5970a20866a4d3fbba2c5bd1928cd24f47faf995f913f1499214c6f3be14bb4d6ec1ab5c6cafb1eca9cb76ba1c2e1c04ed018370634d4e659c77216
  languageName: node
  linkType: hard

"browserify-zlib@npm:^0.2.0":
  version: 0.2.0
  resolution: "browserify-zlib@npm:0.2.0"
  dependencies:
    pako: "npm:~1.0.5"
  checksum: 10c0/9ab10b6dc732c6c5ec8ebcbe5cb7fe1467f97402c9b2140113f47b5f187b9438f93a8e065d8baf8b929323c18324fbf1105af479ee86d9d36cab7d7ef3424ad9
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0":
  version: 4.25.1
  resolution: "browserslist@npm:4.25.1"
  dependencies:
    caniuse-lite: "npm:^1.0.30001726"
    electron-to-chromium: "npm:^1.5.173"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10c0/acba5f0bdbd5e72dafae1e6ec79235b7bad305ed104e082ed07c34c38c7cb8ea1bc0f6be1496958c40482e40166084458fc3aee15111f15faa79212ad9081b2a
  languageName: node
  linkType: hard

"btoa@npm:^1.2.1":
  version: 1.2.1
  resolution: "btoa@npm:1.2.1"
  bin:
    btoa: bin/btoa.js
  checksum: 10c0/557b9682e40a68ae057af1b377e28884e6ff756ba0f499fe0f8c7b725a5bfb5c0d891604ac09944dbe330c9d43fb3976fef734f9372608d0d8e78a30eda292ae
  languageName: node
  linkType: hard

"buffer-indexof@npm:^1.0.0":
  version: 1.1.1
  resolution: "buffer-indexof@npm:1.1.1"
  checksum: 10c0/67906b0a9892854e24ac717ef823c3b19790c653a8b1902835bbf3c3c46ea8d99f0680a92f7394fc5acbbecb3385775ccd504ea00587d2d67d8dfaadd460eeae
  languageName: node
  linkType: hard

"buffer-xor@npm:^1.0.3":
  version: 1.0.3
  resolution: "buffer-xor@npm:1.0.3"
  checksum: 10c0/fd269d0e0bf71ecac3146187cfc79edc9dbb054e2ee69b4d97dfb857c6d997c33de391696d04bdd669272751fa48e7872a22f3a6c7b07d6c0bc31dbe02a4075c
  languageName: node
  linkType: hard

"buffer@npm:^4.3.0":
  version: 4.9.2
  resolution: "buffer@npm:4.9.2"
  dependencies:
    base64-js: "npm:^1.0.2"
    ieee754: "npm:^1.1.4"
    isarray: "npm:^1.0.0"
  checksum: 10c0/dc443d7e7caab23816b58aacdde710b72f525ad6eecd7d738fcaa29f6d6c12e8d9c13fed7219fd502be51ecf0615f5c077d4bdc6f9308dde2e53f8e5393c5b21
  languageName: node
  linkType: hard

"builtin-status-codes@npm:^3.0.0":
  version: 3.0.0
  resolution: "builtin-status-codes@npm:3.0.0"
  checksum: 10c0/c37bbba11a34c4431e56bd681b175512e99147defbe2358318d8152b3a01df7bf25e0305873947e5b350073d5ef41a364a22b37e48f1fb6d2fe6d5286a0f348c
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10c0/76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"cache-base@npm:^1.0.1":
  version: 1.0.1
  resolution: "cache-base@npm:1.0.1"
  dependencies:
    collection-visit: "npm:^1.0.0"
    component-emitter: "npm:^1.2.1"
    get-value: "npm:^2.0.6"
    has-value: "npm:^1.0.0"
    isobject: "npm:^3.0.1"
    set-value: "npm:^2.0.0"
    to-object-path: "npm:^0.3.0"
    union-value: "npm:^1.0.0"
    unset-value: "npm:^1.0.0"
  checksum: 10c0/a7142e25c73f767fa520957dcd179b900b86eac63b8cfeaa3b2a35e18c9ca5968aa4e2d2bed7a3e7efd10f13be404344cfab3a4156217e71f9bdb95940bb9c8c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10c0/a13819be0681d915144467741b69875ae5f4eba8961eb0bf322aab63ec87f8250eb6d6b0dcbb2e1349876412a56129ca338592b3829ef4343527f5f18a0752d4
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10c0/f4796a6a0941e71c766aea672f63b72bc61234c4f4964dc6d7606e3664c307e7d77845328a8f3359ce39ddb377fed67318f9ee203dea1d47e46165dcf2917644
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10c0/fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"camelcase-keys@npm:^2.0.0":
  version: 2.1.0
  resolution: "camelcase-keys@npm:2.1.0"
  dependencies:
    camelcase: "npm:^2.0.0"
    map-obj: "npm:^1.0.0"
  checksum: 10c0/d9431f8b5ac52644cfc45377c0d3897f045137d645c8890bd2bfb48c282d22e76644974198dbba3a2d96b33f9bf3af07aacb712b0dd6d2671330a7e2531b72f9
  languageName: node
  linkType: hard

"camelcase@npm:^1.0.2":
  version: 1.2.1
  resolution: "camelcase@npm:1.2.1"
  checksum: 10c0/dec70dfd46be8e31c5f8a4616f441cc3902da9b807f843c2ad4f2a0c79a8907d91914184b40166e2111bfa76cb66de6107924c0529017204e810ef14390381fa
  languageName: node
  linkType: hard

"camelcase@npm:^2.0.0":
  version: 2.1.1
  resolution: "camelcase@npm:2.1.1"
  checksum: 10c0/610db65fa7dd50a400525ec2188fd65a1939dda4afe5de7d08608670013269c3743c3737fb0f138d1df8aa74e257cc83e3b756e776b604af16dac297b4a0d054
  languageName: node
  linkType: hard

"camelcase@npm:^3.0.0":
  version: 3.0.0
  resolution: "camelcase@npm:3.0.0"
  checksum: 10c0/98871bb40b936430beca49490d325759f8d8ade32bea538ee63c20b17b326abb6bbd3e1d84daf63d9332b2fc7637f28696bf76da59180b1247051b955cb1da12
  languageName: node
  linkType: hard

"camelcase@npm:^4.1.0":
  version: 4.1.0
  resolution: "camelcase@npm:4.1.0"
  checksum: 10c0/54c0b6a85b54fb4e96a9d834a9d0d8f760fd608ab6752a6789042b5e1c38d3dd60f878d2c590d005046445d32d77f6e05e568a91fe8db3e282da0a1560d83058
  languageName: node
  linkType: hard

"camelize@npm:^1.0.0":
  version: 1.0.1
  resolution: "camelize@npm:1.0.1"
  checksum: 10c0/4c9ac55efd356d37ac483bad3093758236ab686192751d1c9daa43188cc5a07b09bd431eb7458a4efd9ca22424bba23253e7b353feb35d7c749ba040de2385fb
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001726":
  version: 1.0.30001731
  resolution: "caniuse-lite@npm:1.0.30001731"
  checksum: 10c0/d8cddf817d5bec8e7c2106affdbf1bfc3923463ca16697c992b2efeb043e6a5d9dcb70cda913bc6acf9112fd66f9e80279316c08e7800359116925066a63fdfa
  languageName: node
  linkType: hard

"canvas@npm:^2.11.2":
  version: 2.11.2
  resolution: "canvas@npm:2.11.2"
  dependencies:
    "@mapbox/node-pre-gyp": "npm:^1.0.0"
    nan: "npm:^2.17.0"
    node-gyp: "npm:latest"
    simple-get: "npm:^3.0.3"
  checksum: 10c0/943368798ad1b66b18633aa34b6181e1038dac5433fc9727cd07be35f0a633f572b60d9edb95f5ff90b6a9128e86d5312035f91a2934101c73185b15d906230a
  languageName: node
  linkType: hard

"canvg@npm:^3.0.11":
  version: 3.0.11
  resolution: "canvg@npm:3.0.11"
  dependencies:
    "@babel/runtime": "npm:^7.12.5"
    "@types/raf": "npm:^3.4.0"
    core-js: "npm:^3.8.3"
    raf: "npm:^3.4.1"
    regenerator-runtime: "npm:^0.13.7"
    rgbcolor: "npm:^1.0.1"
    stackblur-canvas: "npm:^2.0.0"
    svg-pathdata: "npm:^6.0.3"
  checksum: 10c0/03ea5b69e97a4a476ef5fc8455d0da2718bd68cd8ace3e42718176bf4b7dfd80eb1d0eba47dcd04b34ed9a0c5c8ed849ead0f364dad0e35369254a45d5e098c4
  languageName: node
  linkType: hard

"center-align@npm:^0.1.1":
  version: 0.1.3
  resolution: "center-align@npm:0.1.3"
  dependencies:
    align-text: "npm:^0.1.3"
    lazy-cache: "npm:^1.0.3"
  checksum: 10c0/d12d17b53c4ffce900ecddeb87b781e65af9fe197973015bc2d8fb114fcccdd6457995df26bfe156ffe44573ffbabbba7c67653d92cfc8e1b2e7ec88404cbb61
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"cheerio-select@npm:^2.1.0":
  version: 2.1.0
  resolution: "cheerio-select@npm:2.1.0"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-select: "npm:^5.1.0"
    css-what: "npm:^6.1.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.0.1"
  checksum: 10c0/2242097e593919dba4aacb97d7b8275def8b9ec70b00aa1f43335456870cfc9e284eae2080bdc832ed232dabb9eefcf56c722d152da4a154813fb8814a55d282
  languageName: node
  linkType: hard

"cheerio@npm:^1.0.0-rc.10":
  version: 1.1.2
  resolution: "cheerio@npm:1.1.2"
  dependencies:
    cheerio-select: "npm:^2.1.0"
    dom-serializer: "npm:^2.0.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.2.2"
    encoding-sniffer: "npm:^0.2.1"
    htmlparser2: "npm:^10.0.0"
    parse5: "npm:^7.3.0"
    parse5-htmlparser2-tree-adapter: "npm:^7.1.0"
    parse5-parser-stream: "npm:^7.1.2"
    undici: "npm:^7.12.0"
    whatwg-mimetype: "npm:^4.0.0"
  checksum: 10c0/2c6d2274666fe122f54fdca457ee76453e1a993b19563acaa23eb565bf7776f0f01e4c3800092f00e84aa13c83a161f0cf000ac0a8332d1d7f2b2387d6ecc5fc
  languageName: node
  linkType: hard

"chokidar@npm:^2.1.2, chokidar@npm:^2.1.8":
  version: 2.1.8
  resolution: "chokidar@npm:2.1.8"
  dependencies:
    anymatch: "npm:^2.0.0"
    async-each: "npm:^1.0.1"
    braces: "npm:^2.3.2"
    fsevents: "npm:^1.2.7"
    glob-parent: "npm:^3.1.0"
    inherits: "npm:^2.0.3"
    is-binary-path: "npm:^1.0.0"
    is-glob: "npm:^4.0.0"
    normalize-path: "npm:^3.0.0"
    path-is-absolute: "npm:^1.0.0"
    readdirp: "npm:^2.2.1"
    upath: "npm:^1.1.1"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/5631cc00080224f9482cf5418dcbea111aec02fa8d81a8cfe37e47b9cf36089e071de52d503647e3a821a01426a40adc926ba899f657af86a51b8f8d4eef12a7
  languageName: node
  linkType: hard

"chokidar@npm:^3.4.1, chokidar@npm:^3.6.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/8361dcd013f2ddbe260eacb1f3cb2f2c6f2b0ad118708a343a5ed8158941a39cb8fb1d272e0f389712e74ee90ce8ba864eece9e0e62b9705cb468a2f6d917462
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: 10c0/594754e1303672171cc04e50f6c398ae16128eb134a88f801bf5354fd96f205320f23536a045d9abd8b51024a149696e51231565891d4efdab8846021ecf88e6
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"chrome-extension-boilerplate-react-vite@workspace:.":
  version: 0.0.0-use.local
  resolution: "chrome-extension-boilerplate-react-vite@workspace:."
  dependencies:
    "@crxjs/vite-plugin": "npm:^2.0.2"
    "@emotion/react": "npm:^11.14.0"
    "@emotion/styled": "npm:^11.14.1"
    "@eslint/js": "npm:^9.30.1"
    "@fontsource/roboto": "npm:^5.2.6"
    "@mui/icons-material": "npm:^7.2.0"
    "@mui/material": "npm:^7.2.0"
    "@mui/system": "npm:^7.2.0"
    "@mui/x-date-pickers": "npm:^8.9.2"
    "@react-pdf/renderer": "npm:^4.3.0"
    "@tanstack/react-router": "npm:^1.125.6"
    "@tanstack/router-plugin": "npm:^1.125.6"
    "@trivago/prettier-plugin-sort-imports": "npm:^5.2.2"
    "@types/bcryptjs": "npm:^3.0.0"
    "@types/fabric": "npm:^5.3.10"
    "@types/file-saver": "npm:^2.0.7"
    "@types/node": "npm:^24.0.10"
    "@types/react": "npm:^19.1.8"
    "@types/react-dom": "npm:^19.1.6"
    "@types/watch": "npm:^1"
    "@vitejs/plugin-react": "npm:^4.6.0"
    axios: "npm:^1.10.0"
    bcryptjs: "npm:^3.0.2"
    chrome-types: "npm:^0.1.359"
    dayjs: "npm:^1.11.13"
    docx-preview: "npm:^0.3.5"
    docx-templates: "npm:^4.14.1"
    docxtemplater: "npm:^3.65.2"
    eslint: "npm:^9.30.1"
    eslint-config-prettier: "npm:^10.1.5"
    eslint-plugin-react: "npm:^7.37.5"
    eslint-plugin-react-hooks: "npm:^5.2.0"
    fabric: "npm:^6.7.0"
    file-saver: "npm:^2.0.5"
    html-react-parser: "npm:^5.2.6"
    html2canvas: "npm:^1.4.1"
    jspdf: "npm:^3.0.1"
    mammoth: "npm:^1.9.1"
    notistack: "npm:^3.0.2"
    pdf-lib: "npm:^1.17.1"
    pdfjs-dist: "npm:^5.3.93"
    pizzip: "npm:^3.2.0"
    prettier: "npm:^3.6.2"
    react: "npm:^19.1.0"
    react-doc-viewer: "npm:^0.1.14"
    react-dom: "npm:^19.1.0"
    react-pdf: "npm:^10.0.1"
    react-pdf-viewer: "npm:^0.1.0"
    socket.io-client: "npm:^4.8.1"
    typescript: "npm:^5.8.3"
    typescript-eslint: "npm:^8.36.0"
    vite: "npm:^7.0.2"
    watch: "npm:^1.0.2"
  languageName: unknown
  linkType: soft

"chrome-types@npm:^0.1.359":
  version: 0.1.368
  resolution: "chrome-types@npm:0.1.368"
  checksum: 10c0/be5e5d8aee4666b919ffda0329045f7f8643c74372e4fab252ae743b92ec4ce4d6694adaef5932925c8ccc25657809687ec60a206b8930c085f4e5371443020b
  languageName: node
  linkType: hard

"cipher-base@npm:^1.0.0, cipher-base@npm:^1.0.1, cipher-base@npm:^1.0.3":
  version: 1.0.6
  resolution: "cipher-base@npm:1.0.6"
  dependencies:
    inherits: "npm:^2.0.4"
    safe-buffer: "npm:^5.2.1"
  checksum: 10c0/f73268e0ee6585800875d9748f2a2377ae7c2c3375cba346f75598ac6f6bc3a25dec56e984a168ced1a862529ffffe615363f750c40349039d96bd30fba0fca8
  languageName: node
  linkType: hard

"class-utils@npm:^0.3.5":
  version: 0.3.6
  resolution: "class-utils@npm:0.3.6"
  dependencies:
    arr-union: "npm:^3.1.0"
    define-property: "npm:^0.2.5"
    isobject: "npm:^3.0.0"
    static-extend: "npm:^0.1.1"
  checksum: 10c0/d44f4afc7a3e48dba4c2d3fada5f781a1adeeff371b875c3b578bc33815c6c29d5d06483c2abfd43a32d35b104b27b67bfa39c2e8a422fa858068bd756cfbd42
  languageName: node
  linkType: hard

"cliui@npm:^2.1.0":
  version: 2.1.0
  resolution: "cliui@npm:2.1.0"
  dependencies:
    center-align: "npm:^0.1.1"
    right-align: "npm:^0.1.1"
    wordwrap: "npm:0.0.2"
  checksum: 10c0/a5e7a3c1f354f3dd4cdea613822633a3c73604d4852ab2f5ac24fb7e10a2bef4475b4064e1bdd3db61e9527130a634ca4cef7e18666d03519611e70213606245
  languageName: node
  linkType: hard

"cliui@npm:^3.2.0":
  version: 3.2.0
  resolution: "cliui@npm:3.2.0"
  dependencies:
    string-width: "npm:^1.0.1"
    strip-ansi: "npm:^3.0.1"
    wrap-ansi: "npm:^2.0.0"
  checksum: 10c0/07b121fac7fd33ff8dbf3523f0d3dca0329d4e457e57dee54502aa5f27a33cbd9e66aa3e248f0260d8a1431b65b2bad8f510cd97fb8ab6a8e0506310a92e18d5
  languageName: node
  linkType: hard

"clone@npm:^2.1.2":
  version: 2.1.2
  resolution: "clone@npm:2.1.2"
  checksum: 10c0/ed0601cd0b1606bc7d82ee7175b97e68d1dd9b91fd1250a3617b38d34a095f8ee0431d40a1a611122dcccb4f93295b4fdb94942aa763392b5fe44effa50c2d5e
  languageName: node
  linkType: hard

"clsx@npm:^1.1.0":
  version: 1.2.1
  resolution: "clsx@npm:1.2.1"
  checksum: 10c0/34dead8bee24f5e96f6e7937d711978380647e936a22e76380290e35486afd8634966ce300fc4b74a32f3762c7d4c0303f442c3e259f4ce02374eb0c82834f27
  languageName: node
  linkType: hard

"clsx@npm:^2.0.0, clsx@npm:^2.1.1":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: 10c0/c4c8eb865f8c82baab07e71bfa8897c73454881c4f99d6bc81585aecd7c441746c1399d08363dc096c550cceaf97bd4ce1e8854e1771e9998d9f94c4fe075839
  languageName: node
  linkType: hard

"code-point-at@npm:^1.0.0":
  version: 1.1.0
  resolution: "code-point-at@npm:1.1.0"
  checksum: 10c0/33f6b234084e46e6e369b6f0b07949392651b4dde70fc6a592a8d3dafa08d5bb32e3981a02f31f6fc323a26bc03a4c063a9d56834848695bda7611c2417ea2e6
  languageName: node
  linkType: hard

"collection-visit@npm:^1.0.0":
  version: 1.0.0
  resolution: "collection-visit@npm:1.0.0"
  dependencies:
    map-visit: "npm:^1.0.0"
    object-visit: "npm:^1.0.0"
  checksum: 10c0/add72a8d1c37cb90e53b1aaa2c31bf1989bfb733f0b02ce82c9fa6828c7a14358dba2e4f8e698c02f69e424aeccae1ffb39acdeaf872ade2f41369e84a2fcf8a
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"color-string@npm:^1.9.1":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: "npm:^1.0.0"
    simple-swizzle: "npm:^0.2.2"
  checksum: 10c0/b0bfd74c03b1f837f543898b512f5ea353f71630ccdd0d66f83028d1f0924a7d4272deb278b9aef376cacf1289b522ac3fb175e99895283645a2dc3a33af2404
  languageName: node
  linkType: hard

"color-support@npm:^1.1.2":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 10c0/8ffeaa270a784dc382f62d9be0a98581db43e11eee301af14734a6d089bd456478b1a8b3e7db7ca7dc5b18a75f828f775c44074020b51c05fc00e6d0992b1cc6
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"component-emitter@npm:^1.2.1":
  version: 1.3.1
  resolution: "component-emitter@npm:1.3.1"
  checksum: 10c0/e4900b1b790b5e76b8d71b328da41482118c0f3523a516a41be598dc2785a07fd721098d9bf6e22d89b19f4fa4e1025160dc00317ea111633a3e4f75c2b86032
  languageName: node
  linkType: hard

"compressible@npm:~2.0.18":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: "npm:>= 1.43.0 < 2"
  checksum: 10c0/8a03712bc9f5b9fe530cc5a79e164e665550d5171a64575d7dcf3e0395d7b4afa2d79ab176c61b5b596e28228b350dd07c1a2a6ead12fd81d1b6cd632af2fef7
  languageName: node
  linkType: hard

"compression@npm:^1.7.3":
  version: 1.8.1
  resolution: "compression@npm:1.8.1"
  dependencies:
    bytes: "npm:3.1.2"
    compressible: "npm:~2.0.18"
    debug: "npm:2.6.9"
    negotiator: "npm:~0.6.4"
    on-headers: "npm:~1.1.0"
    safe-buffer: "npm:5.2.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/85114b0b91c16594dc8c671cd9b05ef5e465066a60e5a4ed8b4551661303559a896ed17bb72c4234c04064e078f6ca86a34b8690349499a43f6fc4b844475da4
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"connect-history-api-fallback@npm:^1.3.0":
  version: 1.6.0
  resolution: "connect-history-api-fallback@npm:1.6.0"
  checksum: 10c0/6d59c68070fcb2f6d981992f88d050d7544e8e1af6600c23ad680d955e316216794a742a1669d1f14ed5171fc628b916f8a4e15c5a1e55bffc8ccc60bfeb0b2c
  languageName: node
  linkType: hard

"console-browserify@npm:^1.1.0":
  version: 1.2.0
  resolution: "console-browserify@npm:1.2.0"
  checksum: 10c0/89b99a53b7d6cee54e1e64fa6b1f7ac24b844b4019c5d39db298637e55c1f4ffa5c165457ad984864de1379df2c8e1886cbbdac85d9dbb6876a9f26c3106f226
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.0.0, console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 10c0/7ab51d30b52d461412cd467721bb82afe695da78fff8f29fe6f6b9cbaac9a2328e27a22a966014df9532100f6dd85370460be8130b9c677891ba36d96a343f50
  languageName: node
  linkType: hard

"constants-browserify@npm:^1.0.0":
  version: 1.0.0
  resolution: "constants-browserify@npm:1.0.0"
  checksum: 10c0/ab49b1d59a433ed77c964d90d19e08b2f77213fb823da4729c0baead55e3c597f8f97ebccfdfc47bd896d43854a117d114c849a6f659d9986420e97da0f83ac5
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10c0/bac0316ebfeacb8f381b38285dc691c9939bf0a78b0b7c2d5758acadad242d04783cee5337ba7d12a565a19075af1b3c11c728e1e4946de73c6ff7ce45f3f1bb
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 10c0/b76ebed15c000aee4678c3707e0860cb6abd4e680a598c0a26e17f0bfae723ec9cc2802f0ff1bc6e4d80603719010431d2231018373d4dde10f9ccff9dadf5af
  languageName: node
  linkType: hard

"convert-source-map@npm:^1.5.0, convert-source-map@npm:^1.7.0":
  version: 1.9.0
  resolution: "convert-source-map@npm:1.9.0"
  checksum: 10c0/281da55454bf8126cbc6625385928c43479f2060984180c42f3a86c8b8c12720a24eac260624a7d1e090004028d2dee78602330578ceec1a08e27cb8bb0a8a5b
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10c0/8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"cookie-es@npm:^1.2.2":
  version: 1.2.2
  resolution: "cookie-es@npm:1.2.2"
  checksum: 10c0/210eb67cd40a53986fda99d6f47118cfc45a69c4abc03490d15ab1b83ac978d5518356aecdd7a7a4969292445e3063c2302deda4c73706a67edc008127608638
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: 10c0/b36fd0d4e3fef8456915fcf7742e58fbfcc12a17a018e0eb9501c9d5ef6893b596466f03b0564b81af29ff2538fd0aa4b9d54fe5ccbfb4c90ea50ad29fe2d221
  languageName: node
  linkType: hard

"cookie@npm:0.7.1":
  version: 0.7.1
  resolution: "cookie@npm:0.7.1"
  checksum: 10c0/5de60c67a410e7c8dc8a46a4b72eb0fe925871d057c9a5d2c0e8145c4270a4f81076de83410c4d397179744b478e33cd80ccbcc457abf40a9409ad27dcd21dde
  languageName: node
  linkType: hard

"copy-descriptor@npm:^0.1.0":
  version: 0.1.1
  resolution: "copy-descriptor@npm:0.1.1"
  checksum: 10c0/161f6760b7348c941007a83df180588fe2f1283e0867cc027182734e0f26134e6cc02de09aa24a95dc267b2e2025b55659eef76c8019df27bc2d883033690181
  languageName: node
  linkType: hard

"core-js@npm:^3.6.0, core-js@npm:^3.8.3":
  version: 3.44.0
  resolution: "core-js@npm:3.44.0"
  checksum: 10c0/759bf3dc5f75068e9425dddf895fd5531c38794a11ea1c2b65e5ef7c527fe3652d59e8c287e574a211af9bab3c057c5c3fa6f6a773f4e142af895106efad38a4
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10c0/90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"cosmiconfig@npm:^7.0.0":
  version: 7.1.0
  resolution: "cosmiconfig@npm:7.1.0"
  dependencies:
    "@types/parse-json": "npm:^4.0.0"
    import-fresh: "npm:^3.2.1"
    parse-json: "npm:^5.0.0"
    path-type: "npm:^4.0.0"
    yaml: "npm:^1.10.0"
  checksum: 10c0/b923ff6af581638128e5f074a5450ba12c0300b71302398ea38dbeabd33bbcaa0245ca9adbedfcf284a07da50f99ede5658c80bb3e39e2ce770a99d28a21ef03
  languageName: node
  linkType: hard

"create-ecdh@npm:^4.0.4":
  version: 4.0.4
  resolution: "create-ecdh@npm:4.0.4"
  dependencies:
    bn.js: "npm:^4.1.0"
    elliptic: "npm:^6.5.3"
  checksum: 10c0/77b11a51360fec9c3bce7a76288fc0deba4b9c838d5fb354b3e40c59194d23d66efe6355fd4b81df7580da0661e1334a235a2a5c040b7569ba97db428d466e7f
  languageName: node
  linkType: hard

"create-hash@npm:^1.1.0, create-hash@npm:^1.2.0":
  version: 1.2.0
  resolution: "create-hash@npm:1.2.0"
  dependencies:
    cipher-base: "npm:^1.0.1"
    inherits: "npm:^2.0.1"
    md5.js: "npm:^1.3.4"
    ripemd160: "npm:^2.0.1"
    sha.js: "npm:^2.4.0"
  checksum: 10c0/d402e60e65e70e5083cb57af96d89567954d0669e90550d7cec58b56d49c4b193d35c43cec8338bc72358198b8cbf2f0cac14775b651e99238e1cf411490f915
  languageName: node
  linkType: hard

"create-hash@npm:~1.1.3":
  version: 1.1.3
  resolution: "create-hash@npm:1.1.3"
  dependencies:
    cipher-base: "npm:^1.0.1"
    inherits: "npm:^2.0.1"
    ripemd160: "npm:^2.0.0"
    sha.js: "npm:^2.4.0"
  checksum: 10c0/dbcf4a1b13c8dd5f2a69f5f30bd2701f919ed7d3fbf5aa530cf00b17a950c2b77f63bfe6a2981735a646ae2620d96c8f4584bf70aeeabf050a31de4e46219d08
  languageName: node
  linkType: hard

"create-hmac@npm:^1.1.7":
  version: 1.1.7
  resolution: "create-hmac@npm:1.1.7"
  dependencies:
    cipher-base: "npm:^1.0.3"
    create-hash: "npm:^1.1.0"
    inherits: "npm:^2.0.1"
    ripemd160: "npm:^2.0.0"
    safe-buffer: "npm:^5.0.1"
    sha.js: "npm:^2.4.8"
  checksum: 10c0/24332bab51011652a9a0a6d160eed1e8caa091b802335324ae056b0dcb5acbc9fcf173cf10d128eba8548c3ce98dfa4eadaa01bd02f44a34414baee26b651835
  languageName: node
  linkType: hard

"cross-spawn@npm:^5.0.1":
  version: 5.1.0
  resolution: "cross-spawn@npm:5.1.0"
  dependencies:
    lru-cache: "npm:^4.0.1"
    shebang-command: "npm:^1.2.0"
    which: "npm:^1.2.9"
  checksum: 10c0/1918621fddb9f8c61e02118b2dbf81f611ccd1544ceaca0d026525341832b8511ce2504c60f935dbc06b35e5ef156fe8c1e72708c27dd486f034e9c0e1e07201
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"crypto-browserify@npm:^3.11.0":
  version: 3.12.1
  resolution: "crypto-browserify@npm:3.12.1"
  dependencies:
    browserify-cipher: "npm:^1.0.1"
    browserify-sign: "npm:^4.2.3"
    create-ecdh: "npm:^4.0.4"
    create-hash: "npm:^1.2.0"
    create-hmac: "npm:^1.1.7"
    diffie-hellman: "npm:^5.0.3"
    hash-base: "npm:~3.0.4"
    inherits: "npm:^2.0.4"
    pbkdf2: "npm:^3.1.2"
    public-encrypt: "npm:^4.0.3"
    randombytes: "npm:^2.1.0"
    randomfill: "npm:^1.0.4"
  checksum: 10c0/184a2def7b16628e79841243232ab5497f18d8e158ac21b7ce90ab172427d0a892a561280adc08f9d4d517bce8db2a5b335dc21abb970f787f8e874bd7b9db7d
  languageName: node
  linkType: hard

"crypto-js@npm:^4.2.0":
  version: 4.2.0
  resolution: "crypto-js@npm:4.2.0"
  checksum: 10c0/8fbdf9d56f47aea0794ab87b0eb9833baf80b01a7c5c1b0edc7faf25f662fb69ab18dc2199e2afcac54670ff0cd9607a9045a3f7a80336cccd18d77a55b9fdf0
  languageName: node
  linkType: hard

"css-color-keywords@npm:^1.0.0":
  version: 1.0.0
  resolution: "css-color-keywords@npm:1.0.0"
  checksum: 10c0/af205a86c68e0051846ed91eb3e30b4517e1904aac040013ff1d742019b3f9369ba5658ba40901dbbc121186fc4bf0e75a814321cc3e3182fbb2feb81c6d9cb7
  languageName: node
  linkType: hard

"css-line-break@npm:^2.1.0":
  version: 2.1.0
  resolution: "css-line-break@npm:2.1.0"
  dependencies:
    utrie: "npm:^1.0.2"
  checksum: 10c0/b2222d99d5daf7861ecddc050244fdce296fad74b000dcff6bdfb1eb16dc2ef0b9ffe2c1c965e3239bd05ebe9eadb6d5438a91592fa8648d27a338e827cf9048
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.2.2
  resolution: "css-select@npm:5.2.2"
  dependencies:
    boolbase: "npm:^1.0.0"
    css-what: "npm:^6.1.0"
    domhandler: "npm:^5.0.2"
    domutils: "npm:^3.0.1"
    nth-check: "npm:^2.0.1"
  checksum: 10c0/d79fffa97106007f2802589f3ed17b8c903f1c961c0fc28aa8a051eee0cbad394d8446223862efd4c1b40445a6034f626bb639cf2035b0bfc468544177593c99
  languageName: node
  linkType: hard

"css-to-react-native@npm:^3.0.0":
  version: 3.2.0
  resolution: "css-to-react-native@npm:3.2.0"
  dependencies:
    camelize: "npm:^1.0.0"
    css-color-keywords: "npm:^1.0.0"
    postcss-value-parser: "npm:^4.0.2"
  checksum: 10c0/fde850a511d5d3d7c55a1e9b8ed26b69a8ad4868b3487e36ebfbfc0b96fc34bc977d9cd1d61a289d0c74d3f9a662d8cee297da53d4433bf2e27d6acdff8e1003
  languageName: node
  linkType: hard

"css-what@npm:^6.1.0":
  version: 6.2.2
  resolution: "css-what@npm:6.2.2"
  checksum: 10c0/91e24c26fb977b4ccef30d7007d2668c1c10ac0154cc3f42f7304410e9594fb772aea4f30c832d2993b132ca8d99338050866476210316345ec2e7d47b248a56
  languageName: node
  linkType: hard

"cssom@npm:^0.5.0":
  version: 0.5.0
  resolution: "cssom@npm:0.5.0"
  checksum: 10c0/8c4121c243baf0678c65dcac29b201ff0067dfecf978de9d5c83b2ff127a8fdefd2bfd54577f5ad8c80ed7d2c8b489ae01c82023545d010c4ecb87683fb403dd
  languageName: node
  linkType: hard

"cssom@npm:~0.3.6":
  version: 0.3.8
  resolution: "cssom@npm:0.3.8"
  checksum: 10c0/d74017b209440822f9e24d8782d6d2e808a8fdd58fa626a783337222fe1c87a518ba944d4c88499031b4786e68772c99dfae616638d71906fe9f203aeaf14411
  languageName: node
  linkType: hard

"cssstyle@npm:^2.3.0":
  version: 2.3.0
  resolution: "cssstyle@npm:2.3.0"
  dependencies:
    cssom: "npm:~0.3.6"
  checksum: 10c0/863400da2a458f73272b9a55ba7ff05de40d850f22eb4f37311abebd7eff801cf1cd2fb04c4c92b8c3daed83fe766e52e4112afb7bc88d86c63a9c2256a7d178
  languageName: node
  linkType: hard

"csstype@npm:^3.0.10, csstype@npm:^3.0.2, csstype@npm:^3.1.3":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10c0/80c089d6f7e0c5b2bd83cf0539ab41474198579584fa10d86d0cafe0642202343cbc119e076a0b1aece191989477081415d66c9fefbf3c957fc2fc4b7009f248
  languageName: node
  linkType: hard

"currently-unhandled@npm:^0.4.1":
  version: 0.4.1
  resolution: "currently-unhandled@npm:0.4.1"
  dependencies:
    array-find-index: "npm:^1.0.1"
  checksum: 10c0/32d197689ec32f035910202c1abb0dc6424dce01d7b51779c685119b380d98535c110ffff67a262fc7e367612a7dfd30d3d3055f9a6634b5a9dd1302de7ef11c
  languageName: node
  linkType: hard

"d@npm:1, d@npm:^1.0.1, d@npm:^1.0.2":
  version: 1.0.2
  resolution: "d@npm:1.0.2"
  dependencies:
    es5-ext: "npm:^0.10.64"
    type: "npm:^2.7.2"
  checksum: 10c0/3e6ede10cd3b77586c47da48423b62bed161bf1a48bdbcc94d87263522e22f5dfb0e678a6dba5323fdc14c5d8612b7f7eb9e7d9e37b2e2d67a7bf9f116dabe5a
  languageName: node
  linkType: hard

"data-urls@npm:^3.0.2":
  version: 3.0.2
  resolution: "data-urls@npm:3.0.2"
  dependencies:
    abab: "npm:^2.0.6"
    whatwg-mimetype: "npm:^3.0.0"
    whatwg-url: "npm:^11.0.0"
  checksum: 10c0/051c3aaaf3e961904f136aab095fcf6dff4db23a7fc759dd8ba7b3e6ba03fc07ef608086caad8ab910d864bd3b5e57d0d2f544725653d77c96a2c971567045f4
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/7986d40fc7979e9e6241f85db8d17060dd9a71bd53c894fa29d126061715e322a4cd47a00b0b8c710394854183d4120462b980b8554012acc1c0fa49df7ad38c
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10c0/f8a4534b5c69384d95ac18137d381f18a5cfae1f0fc1df0ef6feef51ef0d568606d970b69e02ea186c6c0f0eac77fe4e6ad96fec2569cc86c3afcc7475068c55
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.1"
  checksum: 10c0/fa7aa40078025b7810dcffc16df02c480573b7b53ef1205aa6a61533011005c1890e5ba17018c692ce7c900212b547262d33279fde801ad9843edc0863bf78c4
  languageName: node
  linkType: hard

"dayjs@npm:^1.11.13":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: 10c0/a3caf6ac8363c7dade9d1ee797848ddcf25c1ace68d9fe8678ecf8ba0675825430de5d793672ec87b24a69bf04a1544b176547b2539982275d5542a7955f35b7
  languageName: node
  linkType: hard

"debug@npm:2.6.9, debug@npm:^2.2.0, debug@npm:^2.3.3, debug@npm:^2.6.6":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10c0/121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.3, debug@npm:^4.3.4, debug@npm:^4.3.6":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/d2b44bc1afd912b49bb7ebb0d50a860dc93a4dd7d946e8de94abc957bb63726b7dd5aa48c18c2386c379ec024c46692e15ed3ed97d481729f929201e671fcd55
  languageName: node
  linkType: hard

"debug@npm:^3.1.0":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: 10c0/37d96ae42cbc71c14844d2ae3ba55adf462ec89fd3a999459dec3833944cd999af6007ff29c780f1c61153bcaaf2c842d1e4ce1ec621e4fc4923244942e4a02a
  languageName: node
  linkType: hard

"debug@npm:~4.3.1, debug@npm:~4.3.2":
  version: 4.3.7
  resolution: "debug@npm:4.3.7"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/1471db19c3b06d485a622d62f65947a19a23fbd0dd73f7fd3eafb697eec5360cde447fb075919987899b1a2096e85d35d4eb5a4de09a57600ac9cf7e6c8e768b
  languageName: node
  linkType: hard

"decamelize@npm:^1.0.0, decamelize@npm:^1.1.1, decamelize@npm:^1.1.2":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: 10c0/85c39fe8fbf0482d4a1e224ef0119db5c1897f8503bcef8b826adff7a1b11414972f6fef2d7dec2ee0b4be3863cf64ac1439137ae9e6af23a3d8dcbe26a5b4b2
  languageName: node
  linkType: hard

"decimal.js@npm:^10.4.2":
  version: 10.6.0
  resolution: "decimal.js@npm:10.6.0"
  checksum: 10c0/07d69fbcc54167a340d2d97de95f546f9ff1f69d2b45a02fd7a5292412df3cd9eb7e23065e532a318f5474a2e1bccf8392fdf0443ef467f97f3bf8cb0477e5aa
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.0":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 10c0/1f4fa54eb740414a816b3f6c24818fbfcabd74ac478391e9f4e2282c994127db02010ce804f3d08e38255493cfe68608b3f5c8e09fd6efc4ae46c807691f7a31
  languageName: node
  linkType: hard

"decompress-response@npm:^4.2.0":
  version: 4.2.1
  resolution: "decompress-response@npm:4.2.1"
  dependencies:
    mimic-response: "npm:^2.0.0"
  checksum: 10c0/5e4821be332e80e3639acee2441c41d245fc07ac3ee85a6f28893c10c079d66d9bf09e8d84bffeae5656a4625e09e9b93fb4a5705adbe6b07202eea64fae1c8d
  languageName: node
  linkType: hard

"deep-equal@npm:^1.0.1":
  version: 1.1.2
  resolution: "deep-equal@npm:1.1.2"
  dependencies:
    is-arguments: "npm:^1.1.1"
    is-date-object: "npm:^1.0.5"
    is-regex: "npm:^1.1.4"
    object-is: "npm:^1.1.5"
    object-keys: "npm:^1.1.1"
    regexp.prototype.flags: "npm:^1.5.1"
  checksum: 10c0/cd85d822d18e9b3e1532d0f6ba412d229aa9d22881d70da161674428ae96e47925191296f7cda29306bac252889007da40ed8449363bd1c96c708acb82068a00
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10c0/7f0ee496e0dff14a573dc6127f14c95061b448b87b995fc96c017ce0a1e66af1675e73f1d6064407975bc4ea6ab679497a29fff7b5b9c4e99cb10797c1ad0b4c
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10c0/dea0606d1483eb9db8d930d4eac62ca0fa16738b0b3e07046cddfacf7d8c868bbe13fa0cb263eb91c7d0d527960dc3f2f2471a69ed7816210307f6744fe62e37
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/88a152319ffe1396ccc6ded510a3896e77efac7a1bfbaa174a7b00414a1747377e0bb525d303794a47cf30e805c2ec84e575758512c6e44a993076d29fd4e6c3
  languageName: node
  linkType: hard

"define-property@npm:^0.2.5":
  version: 0.2.5
  resolution: "define-property@npm:0.2.5"
  dependencies:
    is-descriptor: "npm:^0.1.0"
  checksum: 10c0/9986915c0893818dedc9ca23eaf41370667762fd83ad8aa4bf026a28563120dbaacebdfbfbf2b18d3b929026b9c6ee972df1dbf22de8fafb5fe6ef18361e4750
  languageName: node
  linkType: hard

"define-property@npm:^1.0.0":
  version: 1.0.0
  resolution: "define-property@npm:1.0.0"
  dependencies:
    is-descriptor: "npm:^1.0.0"
  checksum: 10c0/d7cf09db10d55df305f541694ed51dafc776ad9bb8a24428899c9f2d36b11ab38dce5527a81458d1b5e7c389f8cbe803b4abad6e91a0037a329d153b84fc975e
  languageName: node
  linkType: hard

"define-property@npm:^2.0.2":
  version: 2.0.2
  resolution: "define-property@npm:2.0.2"
  dependencies:
    is-descriptor: "npm:^1.0.2"
    isobject: "npm:^3.0.1"
  checksum: 10c0/f91a08ad008fa764172a2c072adc7312f10217ade89ddaea23018321c6d71b2b68b8c229141ed2064179404e345c537f1a2457c379824813695b51a6ad3e4969
  languageName: node
  linkType: hard

"del@npm:^3.0.0":
  version: 3.0.0
  resolution: "del@npm:3.0.0"
  dependencies:
    globby: "npm:^6.1.0"
    is-path-cwd: "npm:^1.0.0"
    is-path-in-cwd: "npm:^1.0.0"
    p-map: "npm:^1.1.1"
    pify: "npm:^3.0.0"
    rimraf: "npm:^2.2.8"
  checksum: 10c0/f97ffb91f5d0bc168f9b7f31dd793fdf902f21aca7347671d94ca12834e35e5665787d0cc1e785d0b04f2efbe9a756cd3f51defe17df493d8da6b713e3b30604
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: 10c0/ba05874b91148e1db4bf254750c042bf2215febd23a6d3cda2e64896aef79745fbd4b9996488bd3cafb39ce19dbce0fd6e3b6665275638befffe1c9b312b91b5
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10c0/58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"depd@npm:~1.1.2":
  version: 1.1.2
  resolution: "depd@npm:1.1.2"
  checksum: 10c0/acb24aaf936ef9a227b6be6d495f0d2eb20108a9a6ad40585c5bda1a897031512fef6484e4fdbb80bd249fdaa82841fa1039f416ece03188e677ba11bcfda249
  languageName: node
  linkType: hard

"dequal@npm:^2.0.3":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 10c0/f98860cdf58b64991ae10205137c0e97d384c3a4edc7f807603887b7c4b850af1224a33d88012009f150861cbee4fa2d322c4cc04b9313bee312e47f6ecaa888
  languageName: node
  linkType: hard

"des.js@npm:^1.0.0":
  version: 1.1.0
  resolution: "des.js@npm:1.1.0"
  dependencies:
    inherits: "npm:^2.0.1"
    minimalistic-assert: "npm:^1.0.0"
  checksum: 10c0/671354943ad67493e49eb4c555480ab153edd7cee3a51c658082fcde539d2690ed2a4a0b5d1f401f9cde822edf3939a6afb2585f32c091f2d3a1b1665cd45236
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10c0/bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.0":
  version: 2.0.4
  resolution: "detect-libc@npm:2.0.4"
  checksum: 10c0/c15541f836eba4b1f521e4eecc28eefefdbc10a94d3b8cb4c507689f332cc111babb95deda66f2de050b22122113189986d5190be97d51b5a2b23b938415e67c
  languageName: node
  linkType: hard

"detect-node@npm:^2.0.4":
  version: 2.1.0
  resolution: "detect-node@npm:2.1.0"
  checksum: 10c0/f039f601790f2e9d4654e499913259a798b1f5246ae24f86ab5e8bd4aaf3bce50484234c494f11fb00aecb0c6e2733aa7b1cf3f530865640b65fbbd65b2c4e09
  languageName: node
  linkType: hard

"dfa@npm:^1.2.0":
  version: 1.2.0
  resolution: "dfa@npm:1.2.0"
  checksum: 10c0/ad12f0bc73b530876672e0a9dfbaa350eeff0c876580042734a004e462eca86d7749b9dedf6b067ba54f346137ab23d16615826bbfa424a3e01ab0e2786fad3c
  languageName: node
  linkType: hard

"diff@npm:^8.0.2":
  version: 8.0.2
  resolution: "diff@npm:8.0.2"
  checksum: 10c0/abfb387f033e089df3ec3be960205d17b54df8abf0924d982a7ced3a94c557a4e6cbff2e78b121f216b85f466b3d8d041673a386177c311aaea41459286cc9bc
  languageName: node
  linkType: hard

"diffie-hellman@npm:^5.0.3":
  version: 5.0.3
  resolution: "diffie-hellman@npm:5.0.3"
  dependencies:
    bn.js: "npm:^4.1.0"
    miller-rabin: "npm:^4.0.0"
    randombytes: "npm:^2.0.0"
  checksum: 10c0/ce53ccafa9ca544b7fc29b08a626e23a9b6562efc2a98559a0c97b4718937cebaa9b5d7d0a05032cc9c1435e9b3c1532b9e9bf2e0ede868525922807ad6e1ecf
  languageName: node
  linkType: hard

"dingbat-to-unicode@npm:^1.0.1":
  version: 1.0.1
  resolution: "dingbat-to-unicode@npm:1.0.1"
  checksum: 10c0/4def812dadd17122929ad31df574539e4066d85d07b0f824fc70533ff44ce75733e25cdd5817b80604d0c6e6091159a51f9b67487b926611518718c796354e26
  languageName: node
  linkType: hard

"dns-equal@npm:^1.0.0":
  version: 1.0.0
  resolution: "dns-equal@npm:1.0.0"
  checksum: 10c0/da966e5275ac50546e108af6bc29aaae2164d2ae96d60601b333c4a3aff91f50b6ca14929cf91f20a9cad1587b356323e300cea3ff6588a6a816988485f445f1
  languageName: node
  linkType: hard

"dns-packet@npm:^1.3.1":
  version: 1.3.4
  resolution: "dns-packet@npm:1.3.4"
  dependencies:
    ip: "npm:^1.1.0"
    safe-buffer: "npm:^5.0.1"
  checksum: 10c0/ee06478da192f9014ab43c7e9118c77b9e353a8d5c06b0d2cba367b3501dd7453bcfed89354a8890cf740491379dcf4b28153d064d051e55c30cfbdf92b88608
  languageName: node
  linkType: hard

"dns-txt@npm:^2.0.2":
  version: 2.0.2
  resolution: "dns-txt@npm:2.0.2"
  dependencies:
    buffer-indexof: "npm:^1.0.0"
  checksum: 10c0/71703e65156a2d626216157e6c4fddd844e7e790b6cd3cec830ef8eed80e7ea2697e5f4f2f3eb3aae809be3c91e370cad7a5d91b05ce6b6fcd5e191e7e3d31ca
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 10c0/b6416aaff1f380bf56c3b552f31fdf7a69b45689368deca72d28636f41c16bb28ec3ebc40ace97db4c1afc0ceeb8120e8492fe0046841c94c2933b2e30a7d5ac
  languageName: node
  linkType: hard

"docx-preview@npm:^0.3.5":
  version: 0.3.5
  resolution: "docx-preview@npm:0.3.5"
  dependencies:
    jszip: "npm:>=3.0.0"
  checksum: 10c0/66fb9212d34dcde88eafff22220bcddc19aaed58542626c878f8141bb758ab1f8ced23e82291628348112f756594dcd01df2424b303012a53b6a3d4b58cc9313
  languageName: node
  linkType: hard

"docx-templates@npm:^4.14.1":
  version: 4.14.1
  resolution: "docx-templates@npm:4.14.1"
  dependencies:
    jszip: "npm:^3.10.1"
    sax: "npm:1.3.0"
  checksum: 10c0/db6448b1bdc005ae562675cf2cc68d155fc45d3eb6ddc261a0e78e50bab41d82cb0cb596c9980775c0c16ddd0d36ac7eea235be21957e7fc31cdcfc30b633233
  languageName: node
  linkType: hard

"docxtemplater@npm:^3.65.2":
  version: 3.65.2
  resolution: "docxtemplater@npm:3.65.2"
  dependencies:
    "@xmldom/xmldom": "npm:^0.9.8"
  checksum: 10c0/96061b2d6fafc3a71c4bac824ff3a7fc2cb926fc0593b38b79ac917e4e94c50aaf0eb5faaeb643f0d7eb25268822e7092ee537b9fd425db6a0a5cd3150db4c2a
  languageName: node
  linkType: hard

"dom-helpers@npm:^5.0.1":
  version: 5.2.1
  resolution: "dom-helpers@npm:5.2.1"
  dependencies:
    "@babel/runtime": "npm:^7.8.7"
    csstype: "npm:^3.0.2"
  checksum: 10c0/f735074d66dd759b36b158fa26e9d00c9388ee0e8c9b16af941c38f014a37fc80782de83afefd621681b19ac0501034b4f1c4a3bff5caa1b8667f0212b5e124c
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.2"
    entities: "npm:^4.2.0"
  checksum: 10c0/d5ae2b7110ca3746b3643d3ef60ef823f5f078667baf530cec096433f1627ec4b6fa8c072f09d079d7cda915fd2c7bc1b7b935681e9b09e591e1e15f4040b8e2
  languageName: node
  linkType: hard

"domain-browser@npm:^1.1.1":
  version: 1.2.0
  resolution: "domain-browser@npm:1.2.0"
  checksum: 10c0/a955f482f4b4710fbd77c12a33e77548d63603c30c80f61a80519f27e3db1ba8530b914584cc9e9365d2038753d6b5bd1f4e6c81e432b007b0ec95b8b5e69b1b
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10c0/686f5a9ef0fff078c1412c05db73a0dce096190036f33e400a07e2a4518e9f56b1e324f5c576a0a747ef0e75b5d985c040b0d51945ce780c0dd3c625a18cd8c9
  languageName: node
  linkType: hard

"domexception@npm:^4.0.0":
  version: 4.0.0
  resolution: "domexception@npm:4.0.0"
  dependencies:
    webidl-conversions: "npm:^7.0.0"
  checksum: 10c0/774277cd9d4df033f852196e3c0077a34dbd15a96baa4d166e0e47138a80f4c0bdf0d94e4703e6ff5883cec56bb821a6fff84402d8a498e31de7c87eb932a294
  languageName: node
  linkType: hard

"domhandler@npm:5.0.3, domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: "npm:^2.3.0"
  checksum: 10c0/bba1e5932b3e196ad6862286d76adc89a0dbf0c773e5ced1eb01f9af930c50093a084eff14b8de5ea60b895c56a04d5de8bbc4930c5543d029091916770b2d2a
  languageName: node
  linkType: hard

"dommatrix@npm:^1.0.3":
  version: 1.0.3
  resolution: "dommatrix@npm:1.0.3"
  checksum: 10c0/498e630888ba138c1d140cbb25ca8dbd9ed747486aa0693dcc7a8318f910e4791d6b0fa4ef4c383c419b72e119fa2c26d9ee15a3c7b0e8088979cf02092e2a18
  languageName: node
  linkType: hard

"dompurify@npm:^3.2.4":
  version: 3.2.6
  resolution: "dompurify@npm:3.2.6"
  dependencies:
    "@types/trusted-types": "npm:^2.0.7"
  dependenciesMeta:
    "@types/trusted-types":
      optional: true
  checksum: 10c0/c8f8e5b0879a0d93c84a2e5e78649a47d0c057ed0f7850ca3d573d2cca64b84fb1ff85bd4b20980ade69c4e5b80ae73011340f1c2ff375c7ef98bb8268e1d13a
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1, domutils@npm:^3.2.1, domutils@npm:^3.2.2":
  version: 3.2.2
  resolution: "domutils@npm:3.2.2"
  dependencies:
    dom-serializer: "npm:^2.0.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
  checksum: 10c0/47938f473b987ea71cd59e59626eb8666d3aa8feba5266e45527f3b636c7883cca7e582d901531961f742c519d7514636b7973353b648762b2e3bedbf235fada
  languageName: node
  linkType: hard

"duck@npm:^0.1.12":
  version: 0.1.12
  resolution: "duck@npm:0.1.12"
  dependencies:
    underscore: "npm:^1.13.1"
  checksum: 10c0/dfbe163481cae832c783016c5026f228e95e32bd5dfc9636607d981faf5b1e7aaa5ac27cf181ceefba6a01d42b54239908dee41ae3af2e7cea42e4fa925dbff3
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10c0/b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.173":
  version: 1.5.197
  resolution: "electron-to-chromium@npm:1.5.197"
  checksum: 10c0/2e48557a78bb17f1becdbcb4352ac570025cf7e304e5ad4657548a020c16646d1d5b00ffa149db3ebffb42a72981431305c566e7d7f971adaf5d15b9cb4c744a
  languageName: node
  linkType: hard

"elliptic@npm:^6.5.3, elliptic@npm:^6.5.5":
  version: 6.6.1
  resolution: "elliptic@npm:6.6.1"
  dependencies:
    bn.js: "npm:^4.11.9"
    brorand: "npm:^1.1.0"
    hash.js: "npm:^1.0.0"
    hmac-drbg: "npm:^1.0.1"
    inherits: "npm:^2.0.4"
    minimalistic-assert: "npm:^1.0.1"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 10c0/8b24ef782eec8b472053793ea1e91ae6bee41afffdfcb78a81c0a53b191e715cbe1292aa07165958a9bbe675bd0955142560b1a007ffce7d6c765bcaf951a867
  languageName: node
  linkType: hard

"emoji-regex@npm:^10.3.0":
  version: 10.4.0
  resolution: "emoji-regex@npm:10.4.0"
  checksum: 10c0/a3fcedfc58bfcce21a05a5f36a529d81e88d602100145fcca3dc6f795e3c8acc4fc18fe773fbf9b6d6e9371205edb3afa2668ec3473fa2aa7fd47d2a9d46482d
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"emojis-list@npm:^3.0.0":
  version: 3.0.0
  resolution: "emojis-list@npm:3.0.0"
  checksum: 10c0/7dc4394b7b910444910ad64b812392159a21e1a7ecc637c775a440227dcb4f80eff7fe61f4453a7d7603fa23d23d30cc93fe9e4b5ed985b88d6441cd4a35117b
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10c0/f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 10c0/5d317306acb13e6590e28e27924c754163946a2480de11865c991a3a7eed4315cd3fba378b543ca145829569eefe9b899f3d84bb09870f675ae60bc924b01ceb
  languageName: node
  linkType: hard

"encoding-sniffer@npm:^0.2.1":
  version: 0.2.1
  resolution: "encoding-sniffer@npm:0.2.1"
  dependencies:
    iconv-lite: "npm:^0.6.3"
    whatwg-encoding: "npm:^3.1.1"
  checksum: 10c0/d6b591880788f3baf8dd1744636dd189d24a1ec93e6f9817267c60ac3458a5191ca70ab1a186fb67731beff1c3489c6527dfdc4718158ed8460ab2f400dd5e7d
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"engine.io-client@npm:~6.6.1":
  version: 6.6.3
  resolution: "engine.io-client@npm:6.6.3"
  dependencies:
    "@socket.io/component-emitter": "npm:~3.1.0"
    debug: "npm:~4.3.1"
    engine.io-parser: "npm:~5.2.1"
    ws: "npm:~8.17.1"
    xmlhttprequest-ssl: "npm:~2.1.1"
  checksum: 10c0/ebe0b1da6831d5a68564f9ffb80efe682da4f0538488eaffadf0bcf5177a8b4472cdb01d18a9f20dece2f8de30e2df951eb4635bef2f1b492e9f08a523db91a0
  languageName: node
  linkType: hard

"engine.io-parser@npm:~5.2.1":
  version: 5.2.3
  resolution: "engine.io-parser@npm:5.2.3"
  checksum: 10c0/ed4900d8dbef470ab3839ccf3bfa79ee518ea8277c7f1f2759e8c22a48f64e687ea5e474291394d0c94f84054749fd93f3ef0acb51fa2f5f234cc9d9d8e7c536
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^3.4.0":
  version: 3.4.1
  resolution: "enhanced-resolve@npm:3.4.1"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    memory-fs: "npm:^0.4.0"
    object-assign: "npm:^4.0.1"
    tapable: "npm:^0.2.7"
  checksum: 10c0/91f2da7e8e5ab2376823843a0e0176f683cfdb5749efbb6e1f26580b285593bb5b69006a1ae5e068c21ab1eb90271eec8b8fd6aa3ac36cc6f1f6de4d37a06456
  languageName: node
  linkType: hard

"entities@npm:^4.2.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10c0/5b039739f7621f5d1ad996715e53d964035f75ad3b9a4d38c6b3804bb226e282ffeae2443624d8fdd9c47d8e926ae9ac009c54671243f0c3294c26af7cc85250
  languageName: node
  linkType: hard

"entities@npm:^6.0.0":
  version: 6.0.1
  resolution: "entities@npm:6.0.1"
  checksum: 10c0/ed836ddac5acb34341094eb495185d527bd70e8632b6c0d59548cbfa23defdbae70b96f9a405c82904efa421230b5b3fd2283752447d737beffd3f3e6ee74414
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"errno@npm:^0.1.3":
  version: 0.1.8
  resolution: "errno@npm:0.1.8"
  dependencies:
    prr: "npm:~1.0.1"
  bin:
    errno: cli.js
  checksum: 10c0/83758951967ec57bf00b5f5b7dc797e6d65a6171e57ea57adcf1bd1a0b477fd9b5b35fae5be1ff18f4090ed156bce1db749fe7e317aac19d485a5d150f6a4936
  languageName: node
  linkType: hard

"error-ex@npm:^1.2.0, error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10c0/ba827f89369b4c93382cfca5a264d059dfefdaa56ecc5e338ffa58a6471f5ed93b71a20add1d52290a4873d92381174382658c885ac1a2305f7baca363ce9cce
  languageName: node
  linkType: hard

"es-abstract@npm:^1.17.5, es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.3, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.6, es-abstract@npm:^1.23.9, es-abstract@npm:^1.24.0":
  version: 1.24.0
  resolution: "es-abstract@npm:1.24.0"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.2"
    arraybuffer.prototype.slice: "npm:^1.0.4"
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    data-view-buffer: "npm:^1.0.2"
    data-view-byte-length: "npm:^1.0.2"
    data-view-byte-offset: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    es-set-tostringtag: "npm:^2.1.0"
    es-to-primitive: "npm:^1.3.0"
    function.prototype.name: "npm:^1.1.8"
    get-intrinsic: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    get-symbol-description: "npm:^1.1.0"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    internal-slot: "npm:^1.1.0"
    is-array-buffer: "npm:^3.0.5"
    is-callable: "npm:^1.2.7"
    is-data-view: "npm:^1.0.2"
    is-negative-zero: "npm:^2.0.3"
    is-regex: "npm:^1.2.1"
    is-set: "npm:^2.0.3"
    is-shared-array-buffer: "npm:^1.0.4"
    is-string: "npm:^1.1.1"
    is-typed-array: "npm:^1.1.15"
    is-weakref: "npm:^1.1.1"
    math-intrinsics: "npm:^1.1.0"
    object-inspect: "npm:^1.13.4"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.7"
    own-keys: "npm:^1.0.1"
    regexp.prototype.flags: "npm:^1.5.4"
    safe-array-concat: "npm:^1.1.3"
    safe-push-apply: "npm:^1.0.0"
    safe-regex-test: "npm:^1.1.0"
    set-proto: "npm:^1.0.0"
    stop-iteration-iterator: "npm:^1.1.0"
    string.prototype.trim: "npm:^1.2.10"
    string.prototype.trimend: "npm:^1.0.9"
    string.prototype.trimstart: "npm:^1.0.8"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-length: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
    typed-array-length: "npm:^1.0.7"
    unbox-primitive: "npm:^1.1.0"
    which-typed-array: "npm:^1.1.19"
  checksum: 10c0/b256e897be32df5d382786ce8cce29a1dd8c97efbab77a26609bd70f2ed29fbcfc7a31758cb07488d532e7ccccdfca76c1118f2afe5a424cdc05ca007867c318
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-iterator-helpers@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-iterator-helpers@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.6"
    es-errors: "npm:^1.3.0"
    es-set-tostringtag: "npm:^2.0.3"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.6"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    internal-slot: "npm:^1.1.0"
    iterator.prototype: "npm:^1.1.4"
    safe-array-concat: "npm:^1.1.3"
  checksum: 10c0/97e3125ca472d82d8aceea11b790397648b52c26d8768ea1c1ee6309ef45a8755bb63225a43f3150c7591cffc17caf5752459f1e70d583b4184370a8f04ebd2f
  languageName: node
  linkType: hard

"es-module-lexer@npm:^0.10.0":
  version: 0.10.5
  resolution: "es-module-lexer@npm:0.10.5"
  checksum: 10c0/5a199242971341fefe12ce5984602698d8f9c477e207f847aaed0f70519cf2c68ddbd22dd92b2cc4669a9d421a3b89a67d371994b64604ea24da21d35c42089e
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.3, es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.2":
  version: 1.1.0
  resolution: "es-shim-unscopables@npm:1.1.0"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/1b9702c8a1823fc3ef39035a4e958802cf294dd21e917397c561d0b3e195f383b978359816b1732d02b255ccf63e1e4815da0065b95db8d7c992037be3bbbcdb
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: "npm:^1.2.7"
    is-date-object: "npm:^1.0.5"
    is-symbol: "npm:^1.0.4"
  checksum: 10c0/c7e87467abb0b438639baa8139f701a06537d2b9bc758f23e8622c3b42fd0fdb5bde0f535686119e446dd9d5e4c0f238af4e14960f4771877cf818d023f6730b
  languageName: node
  linkType: hard

"es5-ext@npm:^0.10.35, es5-ext@npm:^0.10.46, es5-ext@npm:^0.10.62, es5-ext@npm:^0.10.64, es5-ext@npm:~0.10.14":
  version: 0.10.64
  resolution: "es5-ext@npm:0.10.64"
  dependencies:
    es6-iterator: "npm:^2.0.3"
    es6-symbol: "npm:^3.1.3"
    esniff: "npm:^2.0.1"
    next-tick: "npm:^1.1.0"
  checksum: 10c0/4459b6ae216f3c615db086e02437bdfde851515a101577fd61b19f9b3c1ad924bab4d197981eb7f0ccb915f643f2fc10ff76b97a680e96cbb572d15a27acd9a3
  languageName: node
  linkType: hard

"es6-iterator@npm:^2.0.3, es6-iterator@npm:~2.0.1, es6-iterator@npm:~2.0.3":
  version: 2.0.3
  resolution: "es6-iterator@npm:2.0.3"
  dependencies:
    d: "npm:1"
    es5-ext: "npm:^0.10.35"
    es6-symbol: "npm:^3.1.1"
  checksum: 10c0/91f20b799dba28fb05bf623c31857fc1524a0f1c444903beccaf8929ad196c8c9ded233e5ac7214fc63a92b3f25b64b7f2737fcca8b1f92d2d96cf3ac902f5d8
  languageName: node
  linkType: hard

"es6-map@npm:^0.1.3":
  version: 0.1.5
  resolution: "es6-map@npm:0.1.5"
  dependencies:
    d: "npm:1"
    es5-ext: "npm:~0.10.14"
    es6-iterator: "npm:~2.0.1"
    es6-set: "npm:~0.1.5"
    es6-symbol: "npm:~3.1.1"
    event-emitter: "npm:~0.3.5"
  checksum: 10c0/62fe1a90ead1704bed699cb9c975e642023f590d5338c2a640d60ce0a846de7d5197561393ac03dd11473b96087cf8793c9e4836fe05ad4d2ef6afda6b9a3511
  languageName: node
  linkType: hard

"es6-set@npm:~0.1.5":
  version: 0.1.6
  resolution: "es6-set@npm:0.1.6"
  dependencies:
    d: "npm:^1.0.1"
    es5-ext: "npm:^0.10.62"
    es6-iterator: "npm:~2.0.3"
    es6-symbol: "npm:^3.1.3"
    event-emitter: "npm:^0.3.5"
    type: "npm:^2.7.2"
  checksum: 10c0/50416775e45350b5c55b35aeb18dc25e6c2017af0ec056336b8e9e7c57e73d8342df398557909e815c523779628c842fbf1b0126c4483a9fbf42b0414358866a
  languageName: node
  linkType: hard

"es6-symbol@npm:^3.1.1, es6-symbol@npm:^3.1.3, es6-symbol@npm:~3.1.1":
  version: 3.1.4
  resolution: "es6-symbol@npm:3.1.4"
  dependencies:
    d: "npm:^1.0.2"
    ext: "npm:^1.7.0"
  checksum: 10c0/777bf3388db5d7919e09a0fd175aa5b8a62385b17cb2227b7a137680cba62b4d9f6193319a102642aa23d5840d38a62e4784f19cfa5be4a2210a3f0e9b23d15d
  languageName: node
  linkType: hard

"es6-weak-map@npm:^2.0.1":
  version: 2.0.3
  resolution: "es6-weak-map@npm:2.0.3"
  dependencies:
    d: "npm:1"
    es5-ext: "npm:^0.10.46"
    es6-iterator: "npm:^2.0.3"
    es6-symbol: "npm:^3.1.1"
  checksum: 10c0/460932be9542473dbbddd183e21c15a66cfec1b2c17dae2b514e190d6fb2896b7eb683783d4b36da036609d2e1c93d2815f21b374dfccaf02a8978694c2f7b67
  languageName: node
  linkType: hard

"esbuild@npm:^0.25.0, esbuild@npm:~0.25.0":
  version: 0.25.8
  resolution: "esbuild@npm:0.25.8"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.25.8"
    "@esbuild/android-arm": "npm:0.25.8"
    "@esbuild/android-arm64": "npm:0.25.8"
    "@esbuild/android-x64": "npm:0.25.8"
    "@esbuild/darwin-arm64": "npm:0.25.8"
    "@esbuild/darwin-x64": "npm:0.25.8"
    "@esbuild/freebsd-arm64": "npm:0.25.8"
    "@esbuild/freebsd-x64": "npm:0.25.8"
    "@esbuild/linux-arm": "npm:0.25.8"
    "@esbuild/linux-arm64": "npm:0.25.8"
    "@esbuild/linux-ia32": "npm:0.25.8"
    "@esbuild/linux-loong64": "npm:0.25.8"
    "@esbuild/linux-mips64el": "npm:0.25.8"
    "@esbuild/linux-ppc64": "npm:0.25.8"
    "@esbuild/linux-riscv64": "npm:0.25.8"
    "@esbuild/linux-s390x": "npm:0.25.8"
    "@esbuild/linux-x64": "npm:0.25.8"
    "@esbuild/netbsd-arm64": "npm:0.25.8"
    "@esbuild/netbsd-x64": "npm:0.25.8"
    "@esbuild/openbsd-arm64": "npm:0.25.8"
    "@esbuild/openbsd-x64": "npm:0.25.8"
    "@esbuild/openharmony-arm64": "npm:0.25.8"
    "@esbuild/sunos-x64": "npm:0.25.8"
    "@esbuild/win32-arm64": "npm:0.25.8"
    "@esbuild/win32-ia32": "npm:0.25.8"
    "@esbuild/win32-x64": "npm:0.25.8"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/openharmony-arm64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10c0/43747a25e120d5dd9ce75c82f57306580d715647c8db4f4a0a84e73b04cf16c27572d3937d3cfb95d5ac3266a4d1bbd3913e3d76ae719693516289fc86f8a5fd
  languageName: node
  linkType: hard

"escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10c0/ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10c0/524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10c0/9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"escodegen@npm:^2.0.0":
  version: 2.1.0
  resolution: "escodegen@npm:2.1.0"
  dependencies:
    esprima: "npm:^4.0.1"
    estraverse: "npm:^5.2.0"
    esutils: "npm:^2.0.2"
    source-map: "npm:~0.6.1"
  dependenciesMeta:
    source-map:
      optional: true
  bin:
    escodegen: bin/escodegen.js
    esgenerate: bin/esgenerate.js
  checksum: 10c0/e1450a1f75f67d35c061bf0d60888b15f62ab63aef9df1901cffc81cffbbb9e8b3de237c5502cf8613a017c1df3a3003881307c78835a1ab54d8c8d2206e01d3
  languageName: node
  linkType: hard

"escope@npm:^3.6.0":
  version: 3.6.0
  resolution: "escope@npm:3.6.0"
  dependencies:
    es6-map: "npm:^0.1.3"
    es6-weak-map: "npm:^2.0.1"
    esrecurse: "npm:^4.1.0"
    estraverse: "npm:^4.1.1"
  checksum: 10c0/79cbb30bf126628eb7e586575b2f5fd31b14ea108cd2379c571fe4dc09ea5208f42dacf1cd43a702b59efccb128e9c9e7762d5cd670d2d184a889947aea509e5
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^10.1.5":
  version: 10.1.8
  resolution: "eslint-config-prettier@npm:10.1.8"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 10c0/e1bcfadc9eccd526c240056b1e59c5cd26544fe59feb85f38f4f1f116caed96aea0b3b87868e68b3099e55caaac3f2e5b9f58110f85db893e83a332751192682
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^5.2.0":
  version: 5.2.0
  resolution: "eslint-plugin-react-hooks@npm:5.2.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0
  checksum: 10c0/1c8d50fa5984c6dea32470651807d2922cc3934cf3425e78f84a24c2dfd972e7f019bee84aefb27e0cf2c13fea0ac1d4473267727408feeb1c56333ca1489385
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:^7.37.5":
  version: 7.37.5
  resolution: "eslint-plugin-react@npm:7.37.5"
  dependencies:
    array-includes: "npm:^3.1.8"
    array.prototype.findlast: "npm:^1.2.5"
    array.prototype.flatmap: "npm:^1.3.3"
    array.prototype.tosorted: "npm:^1.1.4"
    doctrine: "npm:^2.1.0"
    es-iterator-helpers: "npm:^1.2.1"
    estraverse: "npm:^5.3.0"
    hasown: "npm:^2.0.2"
    jsx-ast-utils: "npm:^2.4.1 || ^3.0.0"
    minimatch: "npm:^3.1.2"
    object.entries: "npm:^1.1.9"
    object.fromentries: "npm:^2.0.8"
    object.values: "npm:^1.2.1"
    prop-types: "npm:^15.8.1"
    resolve: "npm:^2.0.0-next.5"
    semver: "npm:^6.3.1"
    string.prototype.matchall: "npm:^4.0.12"
    string.prototype.repeat: "npm:^1.0.0"
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7
  checksum: 10c0/c850bfd556291d4d9234f5ca38db1436924a1013627c8ab1853f77cac73ec19b020e861e6c7b783436a48b6ffcdfba4547598235a37ad4611b6739f65fd8ad57
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.4.0":
  version: 8.4.0
  resolution: "eslint-scope@npm:8.4.0"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10c0/407f6c600204d0f3705bd557f81bd0189e69cd7996f408f8971ab5779c0af733d1af2f1412066b40ee1588b085874fc37a2333986c6521669cdbdd36ca5058e0
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10c0/92708e882c0a5ffd88c23c0b404ac1628cf20104a108c745f240a13c332a11aac54f49a22d5762efbffc18ecbc9a580d1b7ad034bf5f3cc3307e5cbff2ec9820
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.1":
  version: 4.2.1
  resolution: "eslint-visitor-keys@npm:4.2.1"
  checksum: 10c0/fcd43999199d6740db26c58dbe0c2594623e31ca307e616ac05153c9272f12f1364f5a0b1917a8e962268fdecc6f3622c1c2908b4fcc2e047a106fe6de69dc43
  languageName: node
  linkType: hard

"eslint@npm:^9.30.1":
  version: 9.32.0
  resolution: "eslint@npm:9.32.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.12.1"
    "@eslint/config-array": "npm:^0.21.0"
    "@eslint/config-helpers": "npm:^0.3.0"
    "@eslint/core": "npm:^0.15.0"
    "@eslint/eslintrc": "npm:^3.3.1"
    "@eslint/js": "npm:9.32.0"
    "@eslint/plugin-kit": "npm:^0.3.4"
    "@humanfs/node": "npm:^0.16.6"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@humanwhocodes/retry": "npm:^0.4.2"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.6"
    debug: "npm:^4.3.2"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^8.4.0"
    eslint-visitor-keys: "npm:^4.2.1"
    espree: "npm:^10.4.0"
    esquery: "npm:^1.5.0"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^8.0.0"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 10c0/e8a23924ec5f8b62e95483002ca25db74e25c23bd9c6d98a9f656ee32f820169bee3bfdf548ec728b16694f198b3db857d85a49210ee4a035242711d08fdc602
  languageName: node
  linkType: hard

"esniff@npm:^2.0.1":
  version: 2.0.1
  resolution: "esniff@npm:2.0.1"
  dependencies:
    d: "npm:^1.0.1"
    es5-ext: "npm:^0.10.62"
    event-emitter: "npm:^0.3.5"
    type: "npm:^2.7.2"
  checksum: 10c0/7efd8d44ac20e5db8cb0ca77eb65eca60628b2d0f3a1030bcb05e71cc40e6e2935c47b87dba3c733db12925aa5b897f8e0e7a567a2c274206f184da676ea2e65
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.4.0":
  version: 10.4.0
  resolution: "espree@npm:10.4.0"
  dependencies:
    acorn: "npm:^8.15.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^4.2.1"
  checksum: 10c0/c63fe06131c26c8157b4083313cb02a9a54720a08e21543300e55288c40e06c3fc284bdecf108d3a1372c5934a0a88644c98714f38b6ae8ed272b40d9ea08d6b
  languageName: node
  linkType: hard

"esprima@npm:^4.0.1, esprima@npm:~4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10c0/ad4bab9ead0808cf56501750fd9d3fb276f6b105f987707d059005d57e182d18a7c9ec7f3a01794ebddcca676773e42ca48a32d67a250c9d35e009ca613caba3
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10c0/cb9065ec605f9da7a76ca6dadb0619dfb611e37a81e318732977d90fab50a256b95fee2d925fba7c2f3f0523aa16f91587246693bc09bc34d5a59575fe6e93d2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.1.0, esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10c0/81a37116d1408ded88ada45b9fb16dbd26fba3aadc369ce50fcaf82a0bac12772ebd7b24cd7b91fc66786bf2c1ac7b5f196bc990a473efff972f5cb338877cf5
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: 10c0/9cb46463ef8a8a4905d3708a652d60122a0c20bb58dec7e0e12ab0e7235123d74214fc0141d743c381813e1b992767e2708194f6f6e0f9fd00c1b4e0887b8b6d
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10c0/1ff9447b96263dec95d6d67431c5e0771eb9776427421260a3e2f0fdd5d6bd4f8e37a7338f5ad2880c9f143450c9b1e4fc2069060724570a49cf9cf0312bd107
  languageName: node
  linkType: hard

"estree-walker@npm:^2.0.1":
  version: 2.0.2
  resolution: "estree-walker@npm:2.0.2"
  checksum: 10c0/53a6c54e2019b8c914dc395890153ffdc2322781acf4bd7d1a32d7aedc1710807bdcd866ac133903d5629ec601fbb50abe8c2e5553c7f5a0afdd9b6af6c945af
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10c0/9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10c0/12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"event-emitter@npm:^0.3.5, event-emitter@npm:~0.3.5":
  version: 0.3.5
  resolution: "event-emitter@npm:0.3.5"
  dependencies:
    d: "npm:1"
    es5-ext: "npm:~0.10.14"
  checksum: 10c0/75082fa8ffb3929766d0f0a063bfd6046bd2a80bea2666ebaa0cfd6f4a9116be6647c15667bea77222afc12f5b4071b68d393cf39fdaa0e8e81eda006160aff0
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.0":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 10c0/5f6d97cbcbac47be798e6355e3a7639a84ee1f7d9b199a07017f1d2f1e2fe236004d14fa5dfaeba661f94ea57805385e326236a6debbc7145c8877fbc0297c6b
  languageName: node
  linkType: hard

"events@npm:^3.0.0, events@npm:^3.3.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: 10c0/d6b6f2adbccbcda74ddbab52ed07db727ef52e31a61ed26db9feb7dc62af7fc8e060defa65e5f8af9449b86b52cc1a1f6a79f2eafcf4e62add2b7a1fa4a432f6
  languageName: node
  linkType: hard

"eventsource@npm:0.1.6":
  version: 0.1.6
  resolution: "eventsource@npm:0.1.6"
  dependencies:
    original: "npm:>=0.0.5"
  checksum: 10c0/93459f36dd83238ab8d79311eaaff935411733541f6fc61cd6e0f4327c97ab7c3274f5cb2b89caa5cf40b95f36627b974576b493f6a38e6a1abe2f6de728b915
  languageName: node
  linkType: hard

"evp_bytestokey@npm:^1.0.0, evp_bytestokey@npm:^1.0.3":
  version: 1.0.3
  resolution: "evp_bytestokey@npm:1.0.3"
  dependencies:
    md5.js: "npm:^1.3.4"
    node-gyp: "npm:latest"
    safe-buffer: "npm:^5.1.1"
  checksum: 10c0/77fbe2d94a902a80e9b8f5a73dcd695d9c14899c5e82967a61b1fc6cbbb28c46552d9b127cff47c45fcf684748bdbcfa0a50410349109de87ceb4b199ef6ee99
  languageName: node
  linkType: hard

"exec-sh@npm:^0.2.0":
  version: 0.2.2
  resolution: "exec-sh@npm:0.2.2"
  dependencies:
    merge: "npm:^1.2.0"
  checksum: 10c0/2c2ea327775f839b872cd965dc3f48f2f600a86f795bf482f8de9db3737deca4119bbafae0d47d0093f36ce6031223174c39fb5b2850e58f9d935b5d8d6bc673
  languageName: node
  linkType: hard

"execa@npm:^0.7.0":
  version: 0.7.0
  resolution: "execa@npm:0.7.0"
  dependencies:
    cross-spawn: "npm:^5.0.1"
    get-stream: "npm:^3.0.0"
    is-stream: "npm:^1.1.0"
    npm-run-path: "npm:^2.0.0"
    p-finally: "npm:^1.0.0"
    signal-exit: "npm:^3.0.0"
    strip-eof: "npm:^1.0.0"
  checksum: 10c0/812f1776e2a6b2226532e43c1af87d8a12e26de03a06e7e043f653acf5565e0656f5f6c64d66726fefa17178ac129caaa419a50905934e7c4a846417abb25d4a
  languageName: node
  linkType: hard

"expand-brackets@npm:^2.1.4":
  version: 2.1.4
  resolution: "expand-brackets@npm:2.1.4"
  dependencies:
    debug: "npm:^2.3.3"
    define-property: "npm:^0.2.5"
    extend-shallow: "npm:^2.0.1"
    posix-character-classes: "npm:^0.1.0"
    regex-not: "npm:^1.0.0"
    snapdragon: "npm:^0.8.1"
    to-regex: "npm:^3.0.1"
  checksum: 10c0/3e2fb95d2d7d7231486493fd65db913927b656b6fcdfcce41e139c0991a72204af619ad4acb1be75ed994ca49edb7995ef241dbf8cf44dc3c03d211328428a87
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"express@npm:^4.16.2":
  version: 4.21.2
  resolution: "express@npm:4.21.2"
  dependencies:
    accepts: "npm:~1.3.8"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.20.3"
    content-disposition: "npm:0.5.4"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.7.1"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:1.3.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    merge-descriptors: "npm:1.0.3"
    methods: "npm:~1.1.2"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.12"
    proxy-addr: "npm:~2.0.7"
    qs: "npm:6.13.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.2.1"
    send: "npm:0.19.0"
    serve-static: "npm:1.16.2"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/38168fd0a32756600b56e6214afecf4fc79ec28eca7f7a91c2ab8d50df4f47562ca3f9dee412da7f5cea6b1a1544b33b40f9f8586dbacfbdada0fe90dbb10a1f
  languageName: node
  linkType: hard

"ext@npm:^1.7.0":
  version: 1.7.0
  resolution: "ext@npm:1.7.0"
  dependencies:
    type: "npm:^2.7.2"
  checksum: 10c0/a8e5f34e12214e9eee3a4af3b5c9d05ba048f28996450975b369fc86e5d0ef13b6df0615f892f5396a9c65d616213c25ec5b0ad17ef42eac4a500512a19da6c7
  languageName: node
  linkType: hard

"extend-shallow@npm:^2.0.1":
  version: 2.0.1
  resolution: "extend-shallow@npm:2.0.1"
  dependencies:
    is-extendable: "npm:^0.1.0"
  checksum: 10c0/ee1cb0a18c9faddb42d791b2d64867bd6cfd0f3affb711782eb6e894dd193e2934a7f529426aac7c8ddb31ac5d38000a00aa2caf08aa3dfc3e1c8ff6ba340bd9
  languageName: node
  linkType: hard

"extend-shallow@npm:^3.0.0, extend-shallow@npm:^3.0.2":
  version: 3.0.2
  resolution: "extend-shallow@npm:3.0.2"
  dependencies:
    assign-symbols: "npm:^1.0.0"
    is-extendable: "npm:^1.0.1"
  checksum: 10c0/f39581b8f98e3ad94995e33214fff725b0297cf09f2725b6f624551cfb71e0764accfd0af80becc0182af5014d2a57b31b85ec999f9eb8a6c45af81752feac9a
  languageName: node
  linkType: hard

"extglob@npm:^2.0.4":
  version: 2.0.4
  resolution: "extglob@npm:2.0.4"
  dependencies:
    array-unique: "npm:^0.3.2"
    define-property: "npm:^1.0.0"
    expand-brackets: "npm:^2.1.4"
    extend-shallow: "npm:^2.0.1"
    fragment-cache: "npm:^0.2.1"
    regex-not: "npm:^1.0.0"
    snapdragon: "npm:^0.8.1"
    to-regex: "npm:^3.0.1"
  checksum: 10c0/e1a891342e2010d046143016c6c03d58455c2c96c30bf5570ea07929984ee7d48fad86b363aee08f7a8a638f5c3a66906429b21ecb19bc8e90df56a001cd282c
  languageName: node
  linkType: hard

"fabric@npm:^6.7.0":
  version: 6.7.0
  resolution: "fabric@npm:6.7.0"
  dependencies:
    canvas: "npm:^2.11.2"
    jsdom: "npm:^20.0.1"
  dependenciesMeta:
    canvas:
      optional: true
    jsdom:
      optional: true
  checksum: 10c0/2f85d2b1f72e873e8a4443c53114c042ed69ea63cb5e89e235f8aecddec408e4a61e25f262b772b46781ea448f7c803b7a10480971043a2cd0f54fa12741312c
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.11, fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10c0/f6aaa141d0d3384cf73cbcdfc52f475ed293f6d5b65bfc5def368b09163a9f7e5ec2b3014d80f733c405f58e470ee0cc451c2937685045cddcdeaa24199c43fe
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10c0/7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10c0/111972b37338bcb88f7d9e2c5907862c280ebf4234433b95bc611e518d192ccb2d38119c4ac86e26b668d75f7f3894f4ff5c4982899afced7ca78633b08287c4
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/ebc6e50ac7048daaeb8e64522a1ea7a26e92b3cee5cd1c7f2316cdca81ba543aa40a136b53891446ea5c3a67ec215fbaca87ad405f102dd97012f62916905630
  languageName: node
  linkType: hard

"faye-websocket@npm:^0.10.0":
  version: 0.10.0
  resolution: "faye-websocket@npm:0.10.0"
  dependencies:
    websocket-driver: "npm:>=0.5.1"
  checksum: 10c0/aa82ed4a5895ae2019658858c7179921179ce546dc227594fae55896bfe2deddf080d44eaaab882afb878987f35fce57a77b6bae66524fb584fbba73d17d63af
  languageName: node
  linkType: hard

"faye-websocket@npm:~0.11.0":
  version: 0.11.4
  resolution: "faye-websocket@npm:0.11.4"
  dependencies:
    websocket-driver: "npm:>=0.5.1"
  checksum: 10c0/c6052a0bb322778ce9f89af92890f6f4ce00d5ec92418a35e5f4c6864a4fe736fec0bcebd47eac7c0f0e979b01530746b1c85c83cb04bae789271abf19737420
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4, fdir@npm:^6.4.6":
  version: 6.4.6
  resolution: "fdir@npm:6.4.6"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/45b559cff889934ebb8bc498351e5acba40750ada7e7d6bde197768d2fa67c149be8ae7f8ff34d03f4e1eb20f2764116e56440aaa2f6689e9a4aa7ef06acafe9
  languageName: node
  linkType: hard

"fflate@npm:^0.8.1":
  version: 0.8.2
  resolution: "fflate@npm:0.8.2"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: "npm:^4.0.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-saver@npm:^2.0.5":
  version: 2.0.5
  resolution: "file-saver@npm:2.0.5"
  checksum: 10c0/0a361f683786c34b2574aea53744cb70d0a6feb0fa5e3af00f2fcb6c9d40d3049cc1470e38c6c75df24219f247f6fb3076f86943958f580e62ee2ffe897af8b1
  languageName: node
  linkType: hard

"file-uri-to-path@npm:1.0.0":
  version: 1.0.0
  resolution: "file-uri-to-path@npm:1.0.0"
  checksum: 10c0/3b545e3a341d322d368e880e1c204ef55f1d45cdea65f7efc6c6ce9e0c4d22d802d5629320eb779d006fe59624ac17b0e848d83cc5af7cd101f206cb704f5519
  languageName: node
  linkType: hard

"fill-range@npm:^4.0.0":
  version: 4.0.0
  resolution: "fill-range@npm:4.0.0"
  dependencies:
    extend-shallow: "npm:^2.0.1"
    is-number: "npm:^3.0.0"
    repeat-string: "npm:^1.6.1"
    to-regex-range: "npm:^2.1.0"
  checksum: 10c0/ccd57b7c43d7e28a1f8a60adfa3c401629c08e2f121565eece95e2386ebc64dedc7128d8c3448342aabf19db0c55a34f425f148400c7a7be9a606ba48749e089
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"finalhandler@npm:1.3.1":
  version: 1.3.1
  resolution: "finalhandler@npm:1.3.1"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    statuses: "npm:2.0.1"
    unpipe: "npm:~1.0.0"
  checksum: 10c0/d38035831865a49b5610206a3a9a9aae4e8523cbbcd01175d0480ffbf1278c47f11d89be3ca7f617ae6d94f29cf797546a4619cd84dd109009ef33f12f69019f
  languageName: node
  linkType: hard

"find-root@npm:^1.1.0":
  version: 1.1.0
  resolution: "find-root@npm:1.1.0"
  checksum: 10c0/1abc7f3bf2f8d78ff26d9e00ce9d0f7b32e5ff6d1da2857bcdf4746134c422282b091c672cde0572cac3840713487e0a7a636af9aa1b74cb11894b447a521efa
  languageName: node
  linkType: hard

"find-up@npm:^1.0.0":
  version: 1.1.2
  resolution: "find-up@npm:1.1.2"
  dependencies:
    path-exists: "npm:^2.0.0"
    pinkie-promise: "npm:^2.0.0"
  checksum: 10c0/51e35c62d9b7efe82d7d5cce966bfe10c2eaa78c769333f8114627e3a8a4a4f50747f5f50bff50b1094cbc6527776f0d3b9ff74d3561ef714a5290a17c80c2bc
  languageName: node
  linkType: hard

"find-up@npm:^2.0.0, find-up@npm:^2.1.0":
  version: 2.1.0
  resolution: "find-up@npm:2.1.0"
  dependencies:
    locate-path: "npm:^2.0.0"
  checksum: 10c0/c080875c9fe28eb1962f35cbe83c683796a0321899f1eed31a37577800055539815de13d53495049697d3ba313013344f843bb9401dd337a1b832be5edfc6840
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/062c5a83a9c02f53cdd6d175a37ecf8f87ea5bbff1fdfb828f04bfa021441bc7583e8ebc0872a4c1baab96221fb8a8a275a19809fb93fbc40bd69ec35634069a
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.4"
  checksum: 10c0/2c59d93e9faa2523e4fda6b4ada749bed432cfa28c8e251f33b25795e426a1c6dbada777afb1f74fcfff33934fdbdea921ee738fcc33e71adc9d6eca984a1cfc
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.0.0, follow-redirects@npm:^1.15.6":
  version: 1.15.11
  resolution: "follow-redirects@npm:1.15.11"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/d301f430542520a54058d4aeeb453233c564aaccac835d29d15e050beb33f339ad67d9bddbce01739c5dc46a6716dbe3d9d0d5134b1ca203effa11a7ef092343
  languageName: node
  linkType: hard

"fontkit@npm:^2.0.2":
  version: 2.0.4
  resolution: "fontkit@npm:2.0.4"
  dependencies:
    "@swc/helpers": "npm:^0.5.12"
    brotli: "npm:^1.3.2"
    clone: "npm:^2.1.2"
    dfa: "npm:^1.2.0"
    fast-deep-equal: "npm:^3.1.3"
    restructure: "npm:^3.0.0"
    tiny-inflate: "npm:^1.0.3"
    unicode-properties: "npm:^1.4.0"
    unicode-trie: "npm:^2.0.0"
  checksum: 10c0/e68940a0801daa53a4bd160fc49814eeea5eab4dc67225b43064548d35939be9f14de17213bc1a88064adf81b6dfbdb53bda7189df1d07a3ad044482e7fd55e4
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 10c0/0e0b50f6a843a282637d43674d1fb278dda1dd85f4f99b640024cfb10b85058aac0cc781bf689d5fe50b4b7f638e91e548560723a4e76e04fe96ae35ef039cee
  languageName: node
  linkType: hard

"for-in@npm:^1.0.2":
  version: 1.0.2
  resolution: "for-in@npm:1.0.2"
  checksum: 10c0/42bb609d564b1dc340e1996868b67961257fd03a48d7fdafd4f5119530b87f962be6b4d5b7e3a3fc84c9854d149494b1d358e0b0ce9837e64c4c6603a49451d6
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0, form-data@npm:^4.0.4":
  version: 4.0.4
  resolution: "form-data@npm:4.0.4"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    hasown: "npm:^2.0.2"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/373525a9a034b9d57073e55eab79e501a714ffac02e7a9b01be1c820780652b16e4101819785e1e18f8d98f0aee866cc654d660a435c378e16a72f2e7cac9695
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 10c0/9b67c3fac86acdbc9ae47ba1ddd5f2f81526fa4c8226863ede5600a3f7c7416ef451f6f1e240a3cc32d0fd79fcfe6beb08fd0da454f360032bde70bf80afbb33
  languageName: node
  linkType: hard

"fragment-cache@npm:^0.2.1":
  version: 0.2.1
  resolution: "fragment-cache@npm:0.2.1"
  dependencies:
    map-cache: "npm:^0.2.2"
  checksum: 10c0/5891d1c1d1d5e1a7fb3ccf28515c06731476fa88f7a50f4ede8a0d8d239a338448e7f7cc8b73db48da19c229fa30066104fe6489862065a4f1ed591c42fbeabf
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10c0/c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.1":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/5f579466e7109719d162a9249abbeffe7f426eb133ea486e020b89bc6d67a741134076bf439983f2eb79276ceaf6bd7b7c1e43c3fd67fe889863e69072fb0a5e
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/703d16522b8282d7299337539c3ed6edddd1afe82435e4f5b76e34a79cd74e488a8a0e26a636afc2440e1a23b03878e2122e3a2cfe375a5cf63c37d92b86a004
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10c0/444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:^1.2.7":
  version: 1.2.13
  resolution: "fsevents@npm:1.2.13"
  dependencies:
    bindings: "npm:^1.5.0"
    nan: "npm:^2.12.1"
  checksum: 10c0/4427ff08db9ee7327f2c3ad58ec56f9096a917eed861bfffaa2e2be419479cdf37d00750869ab9ecbf5f59f32ad999bd59577d73fc639193e6c0ce52bb253e02
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A^1.2.7#optional!builtin<compat/fsevents>":
  version: 1.2.13
  resolution: "fsevents@patch:fsevents@npm%3A1.2.13#optional!builtin<compat/fsevents>::version=1.2.13&hash=d11327"
  dependencies:
    bindings: "npm:^1.5.0"
    nan: "npm:^2.12.1"
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    functions-have-names: "npm:^1.2.3"
    hasown: "npm:^2.0.2"
    is-callable: "npm:^1.2.7"
  checksum: 10c0/e920a2ab52663005f3cbe7ee3373e3c71c1fb5558b0b0548648cdf3e51961085032458e26c71ff1a8c8c20e7ee7caeb03d43a5d1fa8610c459333323a2e71253
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 10c0/33e77fd29bddc2d9bb78ab3eb854c165909201f88c75faa8272e35899e2d35a8a642a15e7420ef945e1f64a9670d6aa3ec744106b2aa42be68ca5114025954ca
  languageName: node
  linkType: hard

"gauge@npm:^3.0.0":
  version: 3.0.2
  resolution: "gauge@npm:3.0.2"
  dependencies:
    aproba: "npm:^1.0.3 || ^2.0.0"
    color-support: "npm:^1.1.2"
    console-control-strings: "npm:^1.0.0"
    has-unicode: "npm:^2.0.1"
    object-assign: "npm:^4.1.1"
    signal-exit: "npm:^3.0.0"
    string-width: "npm:^4.2.3"
    strip-ansi: "npm:^6.0.1"
    wide-align: "npm:^1.1.2"
  checksum: 10c0/75230ccaf216471e31025c7d5fcea1629596ca20792de50c596eb18ffb14d8404f927cd55535aab2eeecd18d1e11bd6f23ec3c2e9878d2dda1dc74bccc34b913
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10c0/782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-caller-file@npm:^1.0.1":
  version: 1.0.3
  resolution: "get-caller-file@npm:1.0.3"
  checksum: 10c0/763dcee2de8ff60ae7e13a4bad8306205a2fbe108e555686344ddd9ef211b8bebfe459d3a739669257014c59e7cc1e7a44003c21af805c1214673e6a45f06c51
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-stdin@npm:^4.0.1":
  version: 4.0.1
  resolution: "get-stdin@npm:4.0.1"
  checksum: 10c0/68fc39a0af6050bcad791fb3df72999e7636401f11f574bf24af07b1c640d30c01cf38aa39ee55665a93ee7a7753eeb6d1fce6c434dd1f458ee0f8fd02775809
  languageName: node
  linkType: hard

"get-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "get-stream@npm:3.0.0"
  checksum: 10c0/003f5f3b8870da59c6aafdf6ed7e7b07b48c2f8629cd461bd3900726548b6b8cfa2e14d6b7814fbb08f07a42f4f738407fa70b989928b2783a76b278505bba22
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/d6a7d6afca375779a4b307738c9e80dbf7afc0bdbe5948768d54ab9653c865523d8920e670991a925936eb524b7cb6a6361d199a760b21d0ca7620194455aa4b
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.7.5":
  version: 4.10.1
  resolution: "get-tsconfig@npm:4.10.1"
  dependencies:
    resolve-pkg-maps: "npm:^1.0.0"
  checksum: 10c0/7f8e3dabc6a49b747920a800fb88e1952fef871cdf51b79e98db48275a5de6cdaf499c55ee67df5fa6fe7ce65f0063e26de0f2e53049b408c585aa74d39ffa21
  languageName: node
  linkType: hard

"get-value@npm:^2.0.3, get-value@npm:^2.0.6":
  version: 2.0.6
  resolution: "get-value@npm:2.0.6"
  checksum: 10c0/f069c132791b357c8fc4adfe9e2929b0a2c6e95f98ca7bc6fcbc27f8a302e552f86b4ae61ec56d9e9ac2544b93b6a39743d479866a37b43fcc104088ba74f0d9
  languageName: node
  linkType: hard

"glob-parent@npm:^3.1.0":
  version: 3.1.0
  resolution: "glob-parent@npm:3.1.0"
  dependencies:
    is-glob: "npm:^3.1.0"
    path-dirname: "npm:^1.0.0"
  checksum: 10c0/bfa89ce5ae1dfea4c2ece7b61d2ea230d87fcbec7472915cfdb3f4caf688a91ecb0dc86ae39b1e17505adce7e64cae3b971d64dc66091f3a0131169fd631b00d
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10c0/317034d88654730230b3f43bb7ad4f7c90257a426e872ea0bf157473ac61c99bf5d205fad8f0185f989be8d2fa6d3c7dce1645d99d545b6ea9089c39f838e7f8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"glob@npm:^7.0.3, glob@npm:^7.1.3":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 10c0/b96ff42620c9231ad468d4c58ff42afee7777ee1c963013ff8aabe095a451d0ceeb8dcd8ef4cbd64d2538cef45f787a78ba3a9574f4a634438963e334471302d
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
  checksum: 10c0/9d156f313af79d80b1566b93e19285f481c591ad6d0d319b4be5e03750d004dde40a39a0f26f7e635f9007a3600802f53ecd85a759b86f109e80a5f705e01846
  languageName: node
  linkType: hard

"globby@npm:^6.1.0":
  version: 6.1.0
  resolution: "globby@npm:6.1.0"
  dependencies:
    array-union: "npm:^1.0.1"
    glob: "npm:^7.0.3"
    object-assign: "npm:^4.0.1"
    pify: "npm:^2.0.0"
    pinkie-promise: "npm:^2.0.0"
  checksum: 10c0/656ad1f0d02c6ef378c07589519ed3ec27fe988ea177195c05b8aff280320f3d67b91fa0baa6f7e49288f9bf1f92fc84f783a79ac3ed66278f3fa082e627ed84
  languageName: node
  linkType: hard

"goober@npm:^2.0.33":
  version: 2.1.16
  resolution: "goober@npm:2.1.16"
  peerDependencies:
    csstype: ^3.0.10
  checksum: 10c0/f4c8256bf9c27873d47c1443f348779ac7f322516cb80a5dc647a6ebe790ce6bb9d3f487a0fb8be0b583fb96b9b2f6b7463f7fea3cd680306f95fa6fc9db1f6a
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.11, graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10c0/e951259d8cd2e0d196c72ec711add7115d42eb9a8146c8eeda5b8d3ac91e5dd816b9cd68920726d9fd4490368e7ed86e9c423f40db87e2d8dfafa00fa17c3a31
  languageName: node
  linkType: hard

"handle-thing@npm:^2.0.0":
  version: 2.0.1
  resolution: "handle-thing@npm:2.0.1"
  checksum: 10c0/7ae34ba286a3434f1993ebd1cc9c9e6b6d8ea672182db28b1afc0a7119229552fa7031e3e5f3cd32a76430ece4e94b7da6f12af2eb39d6239a7693e4bd63a998
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 10c0/2de0cdc4a1ccf7a1e75ffede1876994525ac03cc6f5ae7392d3415dd475cd9eee5bceec63669ab61aa997ff6cceebb50ef75561c7002bed8988de2b9d1b40788
  languageName: node
  linkType: hard

"has-flag@npm:^2.0.0":
  version: 2.0.0
  resolution: "has-flag@npm:2.0.0"
  checksum: 10c0/5e1f136c7f801c2719048bedfabcf834a1ed46276cd4c98c6fcddb89a482f5d6a16df0771a38805cfc2d9010b4de157909e1a71b708e1d339b6e311041bde9b4
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 10c0/1c6c83b14b8b1b3c25b0727b8ba3e3b647f99e9e6e13eb7322107261de07a4c1be56fc0d45678fc376e09772a3a1642ccdaf8fc69bdf123b6c086598397ce473
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10c0/253c1f59e80bb476cf0dde8ff5284505d90c3bdb762983c3514d36414290475fe3fd6f574929d84de2a8eec00d35cf07cb6776205ff32efd7c50719125f00236
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: "npm:^1.0.0"
  checksum: 10c0/46538dddab297ec2f43923c3d35237df45d8c55a6fc1067031e04c13ed8a9a8f94954460632fd4da84c31a1721eefee16d901cbb1ae9602bab93bb6e08f93b95
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: 10c0/ebdb2f4895c26bb08a8a100b62d362e49b2190bcfd84b76bc4be1a3bd4d254ec52d0dd9f2fbcc093fc5eb878b20c52146f9dfd33e2686ed28982187be593b47c
  languageName: node
  linkType: hard

"has-value@npm:^0.3.1":
  version: 0.3.1
  resolution: "has-value@npm:0.3.1"
  dependencies:
    get-value: "npm:^2.0.3"
    has-values: "npm:^0.1.4"
    isobject: "npm:^2.0.0"
  checksum: 10c0/7a7c2e9d07bc9742c81806150adb154d149bc6155267248c459cd1ce2a64b0759980d26213260e4b7599c8a3754551179f155ded88d0533a0d2bc7bc29028432
  languageName: node
  linkType: hard

"has-value@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-value@npm:1.0.0"
  dependencies:
    get-value: "npm:^2.0.6"
    has-values: "npm:^1.0.0"
    isobject: "npm:^3.0.0"
  checksum: 10c0/17cdccaf50f8aac80a109dba2e2ee5e800aec9a9d382ef9deab66c56b34269e4c9ac720276d5ffa722764304a1180ae436df077da0dd05548cfae0209708ba4d
  languageName: node
  linkType: hard

"has-values@npm:^0.1.4":
  version: 0.1.4
  resolution: "has-values@npm:0.1.4"
  checksum: 10c0/a8f00ad862c20289798c35243d5bd0b0a97dd44b668c2204afe082e0265f2d0bf3b89fc8cc0ef01a52b49f10aa35cf85c336ee3a5f1cac96ed490f5e901cdbf2
  languageName: node
  linkType: hard

"has-values@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-values@npm:1.0.0"
  dependencies:
    is-number: "npm:^3.0.0"
    kind-of: "npm:^4.0.0"
  checksum: 10c0/a6f2a1cc6b2e43eacc68e62e71ad6890def7f4b13d2ef06b4ad3ee156c23e470e6df144b9b467701908e17633411f1075fdff0cab45fb66c5e0584d89b25f35e
  languageName: node
  linkType: hard

"hash-base@npm:^2.0.0":
  version: 2.0.2
  resolution: "hash-base@npm:2.0.2"
  dependencies:
    inherits: "npm:^2.0.1"
  checksum: 10c0/283f6060277b52e627a734c4d19d4315ba82326cab5a2f4f2f00b924d747dc7cc902a8cedb1904c7a3501075fcbb24c08de1152bae296698fdc5ad75b33986af
  languageName: node
  linkType: hard

"hash-base@npm:^3.0.0, hash-base@npm:~3.0, hash-base@npm:~3.0.4":
  version: 3.0.5
  resolution: "hash-base@npm:3.0.5"
  dependencies:
    inherits: "npm:^2.0.4"
    safe-buffer: "npm:^5.2.1"
  checksum: 10c0/6dc185b79bad9b6d525cd132a588e4215380fdc36fec6f7a8a58c5db8e3b642557d02ad9c367f5e476c7c3ad3ccffa3607f308b124e1ed80e3b80a1b254db61e
  languageName: node
  linkType: hard

"hash.js@npm:^1.0.0, hash.js@npm:^1.0.3":
  version: 1.1.7
  resolution: "hash.js@npm:1.1.7"
  dependencies:
    inherits: "npm:^2.0.3"
    minimalistic-assert: "npm:^1.0.1"
  checksum: 10c0/41ada59494eac5332cfc1ce6b7ebdd7b88a3864a6d6b08a3ea8ef261332ed60f37f10877e0c825aaa4bddebf164fbffa618286aeeec5296675e2671cbfa746c4
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0, hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"hmac-drbg@npm:^1.0.1":
  version: 1.0.1
  resolution: "hmac-drbg@npm:1.0.1"
  dependencies:
    hash.js: "npm:^1.0.3"
    minimalistic-assert: "npm:^1.0.0"
    minimalistic-crypto-utils: "npm:^1.0.1"
  checksum: 10c0/f3d9ba31b40257a573f162176ac5930109816036c59a09f901eb2ffd7e5e705c6832bedfff507957125f2086a0ab8f853c0df225642a88bf1fcaea945f20600d
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.0.0, hoist-non-react-statics@npm:^3.3.1":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: "npm:^16.7.0"
  checksum: 10c0/fe0889169e845d738b59b64badf5e55fa3cf20454f9203d1eb088df322d49d4318df774828e789898dcb280e8a5521bb59b3203385662ca5e9218a6ca5820e74
  languageName: node
  linkType: hard

"hosted-git-info@npm:^2.1.4":
  version: 2.8.9
  resolution: "hosted-git-info@npm:2.8.9"
  checksum: 10c0/317cbc6b1bbbe23c2a40ae23f3dafe9fa349ce42a89a36f930e3f9c0530c179a3882d2ef1e4141a4c3674d6faaea862138ec55b43ad6f75e387fda2483a13c70
  languageName: node
  linkType: hard

"hpack.js@npm:^2.1.6":
  version: 2.1.6
  resolution: "hpack.js@npm:2.1.6"
  dependencies:
    inherits: "npm:^2.0.1"
    obuf: "npm:^1.0.0"
    readable-stream: "npm:^2.0.1"
    wbuf: "npm:^1.1.0"
  checksum: 10c0/55b9e824430bab82a19d079cb6e33042d7d0640325678c9917fcc020c61d8a08ca671b6c942c7f0aae9bb6e4b67ffb50734a72f9e21d66407c3138c1983b70f0
  languageName: node
  linkType: hard

"hsl-to-hex@npm:^1.0.0":
  version: 1.0.0
  resolution: "hsl-to-hex@npm:1.0.0"
  dependencies:
    hsl-to-rgb-for-reals: "npm:^1.1.0"
  checksum: 10c0/f8f88e4afae38a46b9ec25edc49a9f79340d951bffd97b15038108e287f9e70768d102565b2df59f785e00895b8c4edcd2fc040e29c7ba2f1f3623f990bf5378
  languageName: node
  linkType: hard

"hsl-to-rgb-for-reals@npm:^1.1.0":
  version: 1.1.1
  resolution: "hsl-to-rgb-for-reals@npm:1.1.1"
  checksum: 10c0/330f63b3ae9dbc6ac5974fc6428fe7a443ad62858eb517d0c99fccc12c51475f597a728ddd5c306ae17b1f22a129b3f1eab6439870b751df7959175fb071aa3e
  languageName: node
  linkType: hard

"html-dom-parser@npm:5.1.1":
  version: 5.1.1
  resolution: "html-dom-parser@npm:5.1.1"
  dependencies:
    domhandler: "npm:5.0.3"
    htmlparser2: "npm:10.0.0"
  checksum: 10c0/ae5c4b4570e4225d32c279a1cc2b5cda56ff881da4164248d1e24e77fbf4fdf75da02a68003ccaf94f9394cacd67269cbdc0a2ad2537676d8aa24f67e13b4628
  languageName: node
  linkType: hard

"html-encoding-sniffer@npm:^3.0.0":
  version: 3.0.0
  resolution: "html-encoding-sniffer@npm:3.0.0"
  dependencies:
    whatwg-encoding: "npm:^2.0.0"
  checksum: 10c0/b17b3b0fb5d061d8eb15121c3b0b536376c3e295ecaf09ba48dd69c6b6c957839db124fe1e2b3f11329753a4ee01aa7dedf63b7677999e86da17fbbdd82c5386
  languageName: node
  linkType: hard

"html-entities@npm:^1.2.0":
  version: 1.4.0
  resolution: "html-entities@npm:1.4.0"
  checksum: 10c0/eb2de616fb5948e681157805687672ea90e67c8a4f21a3215888ab422a984cab61fec96860708dca3bde0ae52577515683c8e28157ac8637220bb6a57a031b85
  languageName: node
  linkType: hard

"html-react-parser@npm:^5.2.6":
  version: 5.2.6
  resolution: "html-react-parser@npm:5.2.6"
  dependencies:
    domhandler: "npm:5.0.3"
    html-dom-parser: "npm:5.1.1"
    react-property: "npm:2.0.2"
    style-to-js: "npm:1.1.17"
  peerDependencies:
    "@types/react": 0.14 || 15 || 16 || 17 || 18 || 19
    react: 0.14 || 15 || 16 || 17 || 18 || 19
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/d51d33fde8f37475c80069f4c475e3da4ca0cf568476e236865d7f3961eb57969761678a379ad8c6b102ccecab7adc5238dfb94418069122b2bf38cf88a0240f
  languageName: node
  linkType: hard

"html2canvas@npm:^1.0.0-rc.5, html2canvas@npm:^1.4.1":
  version: 1.4.1
  resolution: "html2canvas@npm:1.4.1"
  dependencies:
    css-line-break: "npm:^2.1.0"
    text-segmentation: "npm:^1.0.3"
  checksum: 10c0/6de86f75762b00948edf2ea559f16da0a1ec3facc4a8a7d3f35fcec59bb0c5970463478988ae3d9082152e0173690d46ebf4082e7ac803dd4817bae1d355c0db
  languageName: node
  linkType: hard

"htmlparser2@npm:10.0.0, htmlparser2@npm:^10.0.0":
  version: 10.0.0
  resolution: "htmlparser2@npm:10.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.2.1"
    entities: "npm:^6.0.0"
  checksum: 10c0/47cfa37e529c86a7ba9a1e0e6f951ad26ef8ca5af898ab6e8916fa02c0264c1453b4a65f28b7b8a7f9d0d29b5a70abead8203bf8b3f07bc69407e85e7d9a68e4
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10c0/45b66a945cf13ec2d1f29432277201313babf4a01d9e52f44b31ca923434083afeca03f18417f599c9ab3d0e7b618ceb21257542338b57c54b710463b4a53e37
  languageName: node
  linkType: hard

"http-deceiver@npm:^1.2.7":
  version: 1.2.7
  resolution: "http-deceiver@npm:1.2.7"
  checksum: 10c0/8bb9b716f5fc55f54a451da7f49b9c695c3e45498a789634daec26b61e4add7c85613a4a9e53726c39d09de7a163891ecd6eb5809adb64500a840fd86fe81d03
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10c0/fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-errors@npm:~1.6.2":
  version: 1.6.3
  resolution: "http-errors@npm:1.6.3"
  dependencies:
    depd: "npm:~1.1.2"
    inherits: "npm:2.0.3"
    setprototypeof: "npm:1.1.0"
    statuses: "npm:>= 1.4.0 < 2"
  checksum: 10c0/17ec4046ee974477778bfdd525936c254b872054703ec2caa4d6f099566b8adade636ae6aeeacb39302c5cd6e28fb407ebd937f500f5010d0b6850750414ff78
  languageName: node
  linkType: hard

"http-parser-js@npm:>=0.5.1":
  version: 0.5.10
  resolution: "http-parser-js@npm:0.5.10"
  checksum: 10c0/8bbcf1832a8d70b2bd515270112116333add88738a2cc05bfb94ba6bde3be4b33efee5611584113818d2bcf654fdc335b652503be5a6b4c0b95e46f214187d93
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "http-proxy-agent@npm:5.0.0"
  dependencies:
    "@tootallnate/once": "npm:2"
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/32a05e413430b2c1e542e5c74b38a9f14865301dd69dff2e53ddb684989440e3d2ce0c4b64d25eb63cf6283e6265ff979a61cf93e3ca3d23047ddfdc8df34a32
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"http-proxy-middleware@npm:^0.19.1":
  version: 0.19.2
  resolution: "http-proxy-middleware@npm:0.19.2"
  dependencies:
    http-proxy: "npm:^1.18.1"
    is-glob: "npm:^4.0.0"
    lodash: "npm:^4.17.11"
    micromatch: "npm:^3.1.10"
  checksum: 10c0/b7d7df229d5dfe0f043187602e4a04728d22dda7a8fd7aed9f5d83091cb78051391fcc5e5b7cab0b10c5b93c136bac78e8e6567da6728c0cb7a8277c7a0e16c2
  languageName: node
  linkType: hard

"http-proxy@npm:^1.18.1":
  version: 1.18.1
  resolution: "http-proxy@npm:1.18.1"
  dependencies:
    eventemitter3: "npm:^4.0.0"
    follow-redirects: "npm:^1.0.0"
    requires-port: "npm:^1.0.0"
  checksum: 10c0/148dfa700a03fb421e383aaaf88ac1d94521dfc34072f6c59770528c65250983c2e4ec996f2f03aa9f3fe46cd1270a593126068319311e3e8d9e610a37533e94
  languageName: node
  linkType: hard

"https-browserify@npm:^1.0.0":
  version: 1.0.0
  resolution: "https-browserify@npm:1.0.0"
  checksum: 10c0/e17b6943bc24ea9b9a7da5714645d808670af75a425f29baffc3284962626efdc1eb3aa9bbffaa6e64028a6ad98af5b09fabcb454a8f918fb686abfdc9e9b8ae
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0, https-proxy-agent@npm:^5.0.1":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/6dd639f03434003577c62b27cafdb864784ef19b2de430d8ae2a1d45e31c4fd60719e5637b44db1a88a046934307da7089e03d6089ec3ddacc1189d8de8897d1
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"hyphen@npm:^1.6.4":
  version: 1.10.6
  resolution: "hyphen@npm:1.10.6"
  checksum: 10c0/a6f564160a32d5d1856bef0a97615e6078d91b0836903432114ab648b7305e9bc5c8a7358dfc087b268616b22221bd61c5c3e5c9a57243c857866aec789b9116
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10c0/c6886a24cc00f2a059767440ec1bc00d334a89f250db8e0f7feb4961c8727118457e27c495ba94d082e51d3baca378726cd110aaf7ded8b9bbfd6a44760cf1d4
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2, iconv-lite@npm:^0.6.3":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.4":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10c0/f9f652c957983634ded1e7f02da3b559a0d4cc210fca3792cb67f1b153623c9c42efdc1c4121af171e295444459fc4a9201101fb041b1104a3c000bccb188337
  languageName: node
  linkType: hard

"ignore@npm:^7.0.0":
  version: 7.0.5
  resolution: "ignore@npm:7.0.5"
  checksum: 10c0/ae00db89fe873064a093b8999fe4cc284b13ef2a178636211842cceb650b9c3e390d3339191acb145d81ed5379d2074840cf0c33a20bdbd6f32821f79eb4ad5d
  languageName: node
  linkType: hard

"immediate@npm:~3.0.5":
  version: 3.0.6
  resolution: "immediate@npm:3.0.6"
  checksum: 10c0/f8ba7ede69bee9260241ad078d2d535848745ff5f6995c7c7cb41cfdc9ccc213f66e10fa5afb881f90298b24a3f7344b637b592beb4f54e582770cdce3f1f039
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10c0/bf8cc494872fef783249709385ae883b447e3eb09db0ebd15dcead7d9afe7224dad7bd7591c6b73b0b19b3c0f9640eb8ee884f01cfaf2887ab995b0b36a0cbec
  languageName: node
  linkType: hard

"import-local@npm:^1.0.0":
  version: 1.0.0
  resolution: "import-local@npm:1.0.0"
  dependencies:
    pkg-dir: "npm:^2.0.0"
    resolve-cwd: "npm:^2.0.0"
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: 10c0/7d70eca56c77158a53ffe41f17a662d03ea3c1269479f35036e8375072f4fb7ca6b1b0fea1d9fdbb56c5d5bc0514efe1ed1429ab654fee1a9319aba1fcd3a7f5
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"indent-string@npm:^2.1.0":
  version: 2.1.0
  resolution: "indent-string@npm:2.1.0"
  dependencies:
    repeating: "npm:^2.0.0"
  checksum: 10c0/d38e04bbd9b0e1843164d06e9ac1e106ead5a6f7b5714c94ecebc2555b2d3af075b3ddc4d6f92ac87d5319c0935df60d571d3f45f17a6f0ec707be65f26ae924
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10c0/7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.1, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"inherits@npm:2.0.3":
  version: 2.0.3
  resolution: "inherits@npm:2.0.3"
  checksum: 10c0/6e56402373149ea076a434072671f9982f5fad030c7662be0332122fe6c0fa490acb3cc1010d90b6eff8d640b1167d77674add52dfd1bb85d545cf29e80e73e7
  languageName: node
  linkType: hard

"inline-style-parser@npm:0.2.4":
  version: 0.2.4
  resolution: "inline-style-parser@npm:0.2.4"
  checksum: 10c0/ddc0b210eaa03e0f98d677b9836242c583c7c6051e84ce0e704ae4626e7871c5b78f8e30853480218b446355745775df318d4f82d33087ff7e393245efa9a881
  languageName: node
  linkType: hard

"internal-ip@npm:1.2.0":
  version: 1.2.0
  resolution: "internal-ip@npm:1.2.0"
  dependencies:
    meow: "npm:^3.3.0"
  bin:
    internal-ip: cli.js
  checksum: 10c0/47a09e975731681dedba2d21cd0b40c07e111e861d41ee29f5735b0cd17d1ac35b1c7c04704315236de89d6a90d5ba0cb8cf958234a5c9f350046a64d87d5216
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    hasown: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10c0/03966f5e259b009a9bf1a78d60da920df198af4318ec004f57b8aef1dd3fe377fbc8cce63a96e8c810010302654de89f9e19de1cd8ad0061d15be28a695465c7
  languageName: node
  linkType: hard

"interpret@npm:^1.0.0":
  version: 1.4.0
  resolution: "interpret@npm:1.4.0"
  checksum: 10c0/08c5ad30032edeec638485bc3f6db7d0094d9b3e85e0f950866600af3c52e9fd69715416d29564731c479d9f4d43ff3e4d302a178196bdc0e6837ec147640450
  languageName: node
  linkType: hard

"invert-kv@npm:^1.0.0":
  version: 1.0.0
  resolution: "invert-kv@npm:1.0.0"
  checksum: 10c0/9ccef12ada8494c56175cc0380b4cea18b6c0a368436f324a30e43a332db90bdfb83cd3a7987b71df359cdf931ce45b7daf35b677da56658565d61068e4bc20b
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"ip@npm:^1.1.0, ip@npm:^1.1.5":
  version: 1.1.9
  resolution: "ip@npm:1.1.9"
  checksum: 10c0/5af58bfe2110c9978acfd77a2ffcdf9d33a6ce1c72f49edbaf16958f7a8eb979b5163e43bb18938caf3aaa55cdacde4e470874c58ca3b4b112ea7a30461a0c27
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 10c0/0486e775047971d3fdb5fb4f063829bac45af299ae0b82dcf3afa2145338e08290563a2a70f34b732d795ecc8311902e541a8530eeb30d75860a78ff4e94ce2a
  languageName: node
  linkType: hard

"is-accessor-descriptor@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-accessor-descriptor@npm:1.0.1"
  dependencies:
    hasown: "npm:^2.0.0"
  checksum: 10c0/d034034074c5ffeb6c868e091083182279db1a956f49f8d1494cecaa0f8b99d706556ded2a9b20d9aa290549106eef8204d67d8572902e06dcb1add6db6b524d
  languageName: node
  linkType: hard

"is-arguments@npm:^1.1.1":
  version: 1.2.0
  resolution: "is-arguments@npm:1.2.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/6377344b31e9fcb707c6751ee89b11f132f32338e6a782ec2eac9393b0cbd32235dad93052998cda778ee058754860738341d8114910d50ada5615912bb929fc
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/c5c9f25606e86dbb12e756694afbbff64bc8b348d1bc989324c037e1068695131930199d6ad381952715dad3a9569333817f0b1a72ce5af7f883ce802e49c83d
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10c0/e7fb686a739068bb70f860b39b67afc62acc62e36bb61c5f965768abce1873b379c563e61dd2adad96ebb7edf6651111b385e490cf508378959b0ed4cac4e729
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 10c0/f59b43dc1d129edb6f0e282595e56477f98c40278a2acdc8b0a5c57097c9eff8fe55470493df5775478cf32a4dc8eaf6d3a749f07ceee5bc263a78b2434f6a54
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: "npm:^1.0.0"
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/d70c236a5e82de6fc4d44368ffd0c2fee2b088b893511ce21e679da275a5ecc6015ff59a7d7e1bdd7ca39f71a8dbdd253cf8cce5c6b3c91cdd5b42b5ce677298
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: "npm:^1.0.2"
  checksum: 10c0/f4f4b905ceb195be90a6ea7f34323bf1c18e3793f18922e3e9a73c684c29eeeeff5175605c3a3a74cc38185fe27758f07efba3dbae812e5c5afbc0d2316b40e4
  languageName: node
  linkType: hard

"is-binary-path@npm:^1.0.0":
  version: 1.0.1
  resolution: "is-binary-path@npm:1.0.1"
  dependencies:
    binary-extensions: "npm:^1.0.0"
  checksum: 10c0/16e456fa3782eaf3d8e28d382b750507e3d54ff6694df8a1b2c6498da321e2ead311de9c42e653d8fb3213de72bac204b5f97e4a110cda8a72f17b1c1b4eb643
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10c0/a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/36ff6baf6bd18b3130186990026f5a95c709345c39cd368468e6c1b6ab52201e9fd26d8e1f4c066357b4938b0f0401e1a5000e08257787c1a02f3a719457001e
  languageName: node
  linkType: hard

"is-buffer@npm:^1.1.5":
  version: 1.1.6
  resolution: "is-buffer@npm:1.1.6"
  checksum: 10c0/ae18aa0b6e113d6c490ad1db5e8df9bdb57758382b313f5a22c9c61084875c6396d50bbf49315f5b1926d142d74dfb8d31b40d993a383e0a158b15fea7a82234
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 10c0/ceebaeb9d92e8adee604076971dd6000d38d6afc40bb843ea8e45c5579b57671c3f3b50d7f04869618242c6cee08d1b67806a8cb8edaaaf7c0748b3720d6066f
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0, is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/898443c14780a577e807618aaae2b6f745c8538eca5c7bc11388a3f2dc6de82b9902bcc7eb74f07be672b11bbe82dd6a6edded44a00cb3d8f933d0459905eedd
  languageName: node
  linkType: hard

"is-data-descriptor@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-data-descriptor@npm:1.0.1"
  dependencies:
    hasown: "npm:^2.0.0"
  checksum: 10c0/ad3acc372e3227f87eb8cdba112c343ca2a67f1885aecf64f02f901cb0858a1fc9488ad42135ab102e9d9e71a62b3594740790bb103a9ba5da830a131a89e3e8
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    is-typed-array: "npm:^1.1.13"
  checksum: 10c0/ef3548a99d7e7f1370ce21006baca6d40c73e9f15c941f89f0049c79714c873d03b02dae1c64b3f861f55163ecc16da06506c5b8a1d4f16650b3d9351c380153
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/1a4d199c8e9e9cac5128d32e6626fa7805175af9df015620ac0d5d45854ccf348ba494679d872d37301032e35a54fc7978fba1687e8721b2139aea7870cafa2f
  languageName: node
  linkType: hard

"is-descriptor@npm:^0.1.0":
  version: 0.1.7
  resolution: "is-descriptor@npm:0.1.7"
  dependencies:
    is-accessor-descriptor: "npm:^1.0.1"
    is-data-descriptor: "npm:^1.0.1"
  checksum: 10c0/f5960b9783f508aec570465288cb673d4b3cc4aae4e6de970c3afd9a8fc1351edcb85d78b2cce2ec5251893a423f73263cab3bb94cf365a8d71b5d510a116392
  languageName: node
  linkType: hard

"is-descriptor@npm:^1.0.0, is-descriptor@npm:^1.0.2":
  version: 1.0.3
  resolution: "is-descriptor@npm:1.0.3"
  dependencies:
    is-accessor-descriptor: "npm:^1.0.1"
    is-data-descriptor: "npm:^1.0.1"
  checksum: 10c0/b4ee667ea787d3a0be4e58536087fd0587de2b0b6672fbfe288f5b8d831ac4b79fd987f31d6c2d4e5543a42c97a87428bc5215ce292a1a47070147793878226f
  languageName: node
  linkType: hard

"is-extendable@npm:^0.1.0, is-extendable@npm:^0.1.1":
  version: 0.1.1
  resolution: "is-extendable@npm:0.1.1"
  checksum: 10c0/dd5ca3994a28e1740d1e25192e66eed128e0b2ff161a7ea348e87ae4f616554b486854de423877a2a2c171d5f7cd6e8093b91f54533bc88a59ee1c9838c43879
  languageName: node
  linkType: hard

"is-extendable@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-extendable@npm:1.0.1"
  dependencies:
    is-plain-object: "npm:^2.0.4"
  checksum: 10c0/1d6678a5be1563db6ecb121331c819c38059703f0179f52aa80c242c223ee9c6b66470286636c0e63d7163e4d905c0a7d82a096e0b5eaeabb51b9f8d0af0d73f
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.0, is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/818dff679b64f19e228a8205a1e2d09989a98e98def3a817f889208cfcbf918d321b251aadf2c05918194803ebd2eb01b14fc9d0b2bea53d984f4137bfca5e97
  languageName: node
  linkType: hard

"is-finite@npm:^1.0.0":
  version: 1.1.0
  resolution: "is-finite@npm:1.1.0"
  checksum: 10c0/ca6bc7a0321b339f098e657bd4cbf4bb2410f5a11f1b9adb1a1a9ab72288b64368e8251326cb1f74e985f2779299cec3e1f1e558b68ce7e1e2c9be17b7cfd626
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-fullwidth-code-point@npm:1.0.0"
  dependencies:
    number-is-nan: "npm:^1.0.0"
  checksum: 10c0/12acfcf16142f2d431bf6af25d68569d3198e81b9799b4ae41058247aafcc666b0127d64384ea28e67a746372611fcbe9b802f69175287aba466da3eddd5ba0f
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-fullwidth-code-point@npm:2.0.0"
  checksum: 10c0/e58f3e4a601fc0500d8b2677e26e9fe0cd450980e66adb29d85b6addf7969731e38f8e43ed2ec868a09c101a55ac3d8b78902209269f38c5286bc98f5bc1b4d9
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.0"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/fdfa96c8087bf36fc4cd514b474ba2ff404219a4dd4cfa6cf5426404a1eed259bdcdb98f082a71029a48d01f27733e3436ecc6690129a7ec09cb0434bee03a2a
  languageName: node
  linkType: hard

"is-glob@npm:^3.1.0":
  version: 3.1.0
  resolution: "is-glob@npm:3.1.0"
  dependencies:
    is-extglob: "npm:^2.1.0"
  checksum: 10c0/ba816a35dcf5285de924a8a4654df7b183a86381d73ea3bbf3df3cc61b3ba61fdddf90ee205709a2235b210ee600ee86e5e8600093cf291a662607fd032e2ff4
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: 10c0/2c4d431b74e00fdda7162cd8e4b763d6f6f217edf97d4f8538b94b8702b150610e2c64961340015fe8df5b1fcee33ccd2e9b62619c4a8a3a155f8de6d6d355fc
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-negative-zero@npm:2.0.3"
  checksum: 10c0/bcdcf6b8b9714063ffcfa9929c575ac69bfdabb8f4574ff557dfc086df2836cf07e3906f5bbc4f2a5c12f8f3ba56af640c843cdfc74da8caed86c7c7d66fd08e
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/97b451b41f25135ff021d85c436ff0100d84a039bb87ffd799cbcdbea81ef30c464ced38258cdd34f080be08fc3b076ca1f472086286d2aa43521d6ec6a79f53
  languageName: node
  linkType: hard

"is-number@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-number@npm:3.0.0"
  dependencies:
    kind-of: "npm:^3.0.2"
  checksum: 10c0/e639c54640b7f029623df24d3d103901e322c0c25ea5bde97cd723c2d0d4c05857a8364ab5c58d963089dbed6bf1d0ffe975cb6aef917e2ad0ccbca653d31b4f
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-path-cwd@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-path-cwd@npm:1.0.0"
  checksum: 10c0/8cc3acd4cbe5dd1c2932651ffa37a3d64765405bde2d4b61f76f9fa77d195fedae42d8b8aed2027ea6f7e2cc4ce7d7f8cb875e036c6113174193f6f7467393fc
  languageName: node
  linkType: hard

"is-path-in-cwd@npm:^1.0.0":
  version: 1.0.1
  resolution: "is-path-in-cwd@npm:1.0.1"
  dependencies:
    is-path-inside: "npm:^1.0.0"
  checksum: 10c0/d459e591f71ff1006791a1639c8bf7d2d668b60a489ec6ab84500182d265fde8058ddff1ca795d2c30ca044c522954b1293171760528f720046aa3dd20cf08bc
  languageName: node
  linkType: hard

"is-path-inside@npm:^1.0.0":
  version: 1.0.1
  resolution: "is-path-inside@npm:1.0.1"
  dependencies:
    path-is-inside: "npm:^1.0.1"
  checksum: 10c0/093ab1324e33a95c2d057e1450e1936ee7a3ed25b78c8dc42f576f3dc3489dd8788d431ea2969bb0e081f005de1571792ea99cf7b1b69ab2dd4ca477ae7a8e51
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.3, is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: 10c0/f050fdd5203d9c81e8c4df1b3ff461c4bc64e8b5ca383bcdde46131361d0a678e80bcf00b5257646f6c636197629644d53bd8e2375aea633de09a82d57e942f4
  languageName: node
  linkType: hard

"is-potential-custom-element-name@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-potential-custom-element-name@npm:1.0.1"
  checksum: 10c0/b73e2f22bc863b0939941d369486d308b43d7aef1f9439705e3582bfccaa4516406865e32c968a35f97a99396dac84e2624e67b0a16b0a15086a785e16ce7db9
  languageName: node
  linkType: hard

"is-regex@npm:^1.1.4, is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/1d3715d2b7889932349241680032e85d0b492cfcb045acb75ffc2c3085e8d561184f1f7e84b6f8321935b4aea39bc9c6ba74ed595b57ce4881a51dfdbc214e04
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 10c0/f73732e13f099b2dc879c2a12341cfc22ccaca8dd504e6edae26484bd5707a35d503fba5b4daad530a9b088ced1ae6c9d8200fd92e09b428fe14ea79ce8080b7
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/65158c2feb41ff1edd6bbd6fd8403a69861cf273ff36077982b5d4d68e1d59278c71691216a4a64632bd76d4792d4d1d2553901b6666d84ade13bba5ea7bc7db
  languageName: node
  linkType: hard

"is-stream@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-stream@npm:1.1.0"
  checksum: 10c0/b8ae7971e78d2e8488d15f804229c6eed7ed36a28f8807a1815938771f4adff0e705218b7dab968270433f67103e4fef98062a0beea55d64835f705ee72c7002
  languageName: node
  linkType: hard

"is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/2f518b4e47886bb81567faba6ffd0d8a8333cf84336e2e78bf160693972e32ad00fe84b0926491cc598dee576fdc55642c92e62d0cbe96bf36f643b6f956f94d
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10c0/f08f3e255c12442e833f75a9e2b84b2d4882fdfd920513cf2a4a2324f0a5b076c8fd913778e3ea5d258d5183e9d92c0cd20e04b03ab3df05316b049b2670af1e
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/415511da3669e36e002820584e264997ffe277ff136643a3126cc949197e6ca3334d0f12d084e83b1994af2e9c8141275c741cf2b7da5a2ff62dd0cac26f76c4
  languageName: node
  linkType: hard

"is-url@npm:^1.2.4":
  version: 1.2.4
  resolution: "is-url@npm:1.2.4"
  checksum: 10c0/0157a79874f8f95fdd63540e3f38c8583c2ef572661cd0693cda80ae3e42dfe8e9a4a972ec1b827f861d9a9acf75b37f7d58a37f94a8a053259642912c252bc3
  languageName: node
  linkType: hard

"is-utf8@npm:^0.2.0":
  version: 0.2.1
  resolution: "is-utf8@npm:0.2.1"
  checksum: 10c0/3ed45e5b4ddfa04ed7e32c63d29c61b980ecd6df74698f45978b8c17a54034943bcbffb6ae243202e799682a66f90fef526f465dd39438745e9fe70794c1ef09
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: 10c0/443c35bb86d5e6cc5929cd9c75a4024bb0fff9586ed50b092f94e700b89c43a33b186b76dbc6d54f3d3d09ece689ab38dcdc1af6a482cbe79c0f2da0a17f1299
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10c0/8e0a9c07b0c780949a100e2cab2b5560a48ecd4c61726923c1a9b77b6ab0aa0046c9e7fb2206042296817045376dee2c8ab1dabe08c7c3dfbf195b01275a085b
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/6491eba08acb8dc9532da23cb226b7d0192ede0b88f16199e592e4769db0a077119c1f5d2283d1e0d16d739115f70046e887e477eb0e66cd90e1bb29f28ba647
  languageName: node
  linkType: hard

"is-windows@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-windows@npm:1.0.2"
  checksum: 10c0/b32f418ab3385604a66f1b7a3ce39d25e8881dee0bd30816dc8344ef6ff9df473a732bcc1ec4e84fe99b2f229ae474f7133e8e93f9241686cfcf7eebe53ba7a5
  languageName: node
  linkType: hard

"is-wsl@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-wsl@npm:1.1.0"
  checksum: 10c0/7ad0012f21092d6f586c7faad84755a8ef0da9b9ec295e4dc82313cce4e1a93a3da3c217265016461f9b141503fe55fa6eb1fd5457d3f05e8d1bdbb48e50c13a
  languageName: node
  linkType: hard

"isarray@npm:1.0.0, isarray@npm:^1.0.0, isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10c0/18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 10c0/4199f14a7a13da2177c66c31080008b7124331956f47bca57dd0b6ea9f11687aa25e565a2c7a2b519bc86988d10398e3049a1f5df13c9f6b7664154690ae79fd
  languageName: node
  linkType: hard

"isbot@npm:^5.1.22":
  version: 5.1.29
  resolution: "isbot@npm:5.1.29"
  checksum: 10c0/a230429cf6de4b551c129aee8a52b65a6f298a440301537072ddfe94e1eeb481d55bbab1a743084d079c23c71e72ebd89a421d6f7e897f9807493a2f4900319d
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"isobject@npm:^2.0.0":
  version: 2.1.0
  resolution: "isobject@npm:2.1.0"
  dependencies:
    isarray: "npm:1.0.0"
  checksum: 10c0/c4cafec73b3b2ee11be75dff8dafd283b5728235ac099b07d7873d5182553a707768e208327bbc12931b9422d8822280bf88d894a0024ff5857b3efefb480e7b
  languageName: node
  linkType: hard

"isobject@npm:^3.0.0, isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: 10c0/03344f5064a82f099a0cd1a8a407f4c0d20b7b8485e8e816c39f249e9416b06c322e8dec5b842b6bb8a06de0af9cb48e7bc1b5352f0fadc2f0abac033db3d4db
  languageName: node
  linkType: hard

"iterator.prototype@npm:^1.1.4":
  version: 1.1.5
  resolution: "iterator.prototype@npm:1.1.5"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.6"
    get-proto: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10c0/f7a262808e1b41049ab55f1e9c29af7ec1025a000d243b83edf34ce2416eedd56079b117fa59376bb4a724110690f13aa8427f2ee29a09eec63a7e72367626d0
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"javascript-natural-sort@npm:^0.7.1":
  version: 0.7.1
  resolution: "javascript-natural-sort@npm:0.7.1"
  checksum: 10c0/340f8ffc5d30fb516e06dc540e8fa9e0b93c865cf49d791fed3eac3bdc5fc71f0066fc81d44ec1433edc87caecaf9f13eec4a1fce8c5beafc709a71eaedae6fe
  languageName: node
  linkType: hard

"jay-peg@npm:^1.1.1":
  version: 1.1.1
  resolution: "jay-peg@npm:1.1.1"
  dependencies:
    restructure: "npm:^3.0.0"
  checksum: 10c0/654ea1e1938dac5af24d4bf8fc9a2ac91faf39a18295b40e02b1c8bd08e9f17df78d383a538891e29ed5f7097e098bbae256d5dab39854314f5771eceeea2088
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsdom@npm:^20.0.1":
  version: 20.0.3
  resolution: "jsdom@npm:20.0.3"
  dependencies:
    abab: "npm:^2.0.6"
    acorn: "npm:^8.8.1"
    acorn-globals: "npm:^7.0.0"
    cssom: "npm:^0.5.0"
    cssstyle: "npm:^2.3.0"
    data-urls: "npm:^3.0.2"
    decimal.js: "npm:^10.4.2"
    domexception: "npm:^4.0.0"
    escodegen: "npm:^2.0.0"
    form-data: "npm:^4.0.0"
    html-encoding-sniffer: "npm:^3.0.0"
    http-proxy-agent: "npm:^5.0.0"
    https-proxy-agent: "npm:^5.0.1"
    is-potential-custom-element-name: "npm:^1.0.1"
    nwsapi: "npm:^2.2.2"
    parse5: "npm:^7.1.1"
    saxes: "npm:^6.0.0"
    symbol-tree: "npm:^3.2.4"
    tough-cookie: "npm:^4.1.2"
    w3c-xmlserializer: "npm:^4.0.0"
    webidl-conversions: "npm:^7.0.0"
    whatwg-encoding: "npm:^2.0.0"
    whatwg-mimetype: "npm:^3.0.0"
    whatwg-url: "npm:^11.0.0"
    ws: "npm:^8.11.0"
    xml-name-validator: "npm:^4.0.0"
  peerDependencies:
    canvas: ^2.5.0
  peerDependenciesMeta:
    canvas:
      optional: true
  checksum: 10c0/b109073bb826a966db7828f46cb1d7371abecd30f182b143c52be5fe1ed84513bbbe995eb3d157241681fcd18331381e61e3dc004d4949f3a63bca02f6214902
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10c0/0d1c91569d9588e7eef2b49b59851f297f3ab93c7b35c7c221e288099322be6b562767d11e4821da500f3219542b9afd2e54c5dc573107c1126ed1080f8e96d7
  languageName: node
  linkType: hard

"json-loader@npm:^0.5.4":
  version: 0.5.7
  resolution: "json-loader@npm:0.5.7"
  checksum: 10c0/b155b81f644693b5418e595c127c552c34373f749d2d125df24cfe753de6b3b0af88dda5d58e7b65940ed03ec46c19fc3d09f53b932b45c9ae3ca1fb55e44a15
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10c0/140932564c8f0b88455432e0f33c4cb4086b8868e37524e07e723f4eaedb9425bdc2bafd71bd1d9765bd15fd1e2d126972bc83990f55c467168c228c24d665f3
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10c0/108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10c0/cb168b61fd4de83e58d09aaa6425ef71001bae30d260e2c57e7d09a5fd82223e2f22a042dedaab8db23b7d9ae46854b08bb1f91675a8be11c5cffebef5fb66a5
  languageName: node
  linkType: hard

"json3@npm:^3.3.2":
  version: 3.3.3
  resolution: "json3@npm:3.3.3"
  checksum: 10c0/ca6942dbcef852c8fda5a87261b82dfa9135b43777cd05bdfc08d071592bd328e289c78eb15d0c45884d9cfe71a2a20601aaa9be16e98d488b5a2f51ee91f259
  languageName: node
  linkType: hard

"json5@npm:^0.5.1":
  version: 0.5.1
  resolution: "json5@npm:0.5.1"
  bin:
    json5: lib/cli.js
  checksum: 10c0/aca0ab7ccf1883d3fc2ecc16219bc389716a773f774552817deaadb549acc0bb502e317a81946fc0a48f9eb6e0822cf1dc5a097009203f2c94de84c8db02a1f3
  languageName: node
  linkType: hard

"json5@npm:^1.0.1":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: "npm:^1.2.0"
  bin:
    json5: lib/cli.js
  checksum: 10c0/9ee316bf21f000b00752e6c2a3b79ecf5324515a5c60ee88983a1910a45426b643a4f3461657586e8aeca87aaf96f0a519b0516d2ae527a6c3e7eed80f68717f
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"jspdf@npm:^3.0.1":
  version: 3.0.1
  resolution: "jspdf@npm:3.0.1"
  dependencies:
    "@babel/runtime": "npm:^7.26.7"
    atob: "npm:^2.1.2"
    btoa: "npm:^1.2.1"
    canvg: "npm:^3.0.11"
    core-js: "npm:^3.6.0"
    dompurify: "npm:^3.2.4"
    fflate: "npm:^0.8.1"
    html2canvas: "npm:^1.0.0-rc.5"
  dependenciesMeta:
    canvg:
      optional: true
    core-js:
      optional: true
    dompurify:
      optional: true
    html2canvas:
      optional: true
  checksum: 10c0/db900f44d9cdf2c51bd5a7ffe9c55711aff68e7751a61291cc2451f06cfa1e268ecd5947c37e0f9097c999d751e3cdd1560f5a4a242376530100654c70970eda
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^2.4.1 || ^3.0.0":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: "npm:^3.1.6"
    array.prototype.flat: "npm:^1.3.1"
    object.assign: "npm:^4.1.4"
    object.values: "npm:^1.1.6"
  checksum: 10c0/a32679e9cb55469cb6d8bbc863f7d631b2c98b7fc7bf172629261751a6e7bc8da6ae374ddb74d5fbd8b06cf0eb4572287b259813d92b36e384024ed35e4c13e1
  languageName: node
  linkType: hard

"jszip@npm:>=3.0.0, jszip@npm:^3.10.1, jszip@npm:^3.7.1":
  version: 3.10.1
  resolution: "jszip@npm:3.10.1"
  dependencies:
    lie: "npm:~3.3.0"
    pako: "npm:~1.0.2"
    readable-stream: "npm:~2.3.6"
    setimmediate: "npm:^1.0.5"
  checksum: 10c0/58e01ec9c4960383fb8b38dd5f67b83ccc1ec215bf74c8a5b32f42b6e5fb79fada5176842a11409c4051b5b94275044851814a31076bf49e1be218d3ef57c863
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10c0/aa52f3c5e18e16bb6324876bb8b59dd02acf782a4b789c7b2ae21107fab95fab3890ed448d4f8dba80ce05391eeac4bfabb4f02a20221342982f806fa2cf271e
  languageName: node
  linkType: hard

"killable@npm:^1.0.0":
  version: 1.0.1
  resolution: "killable@npm:1.0.1"
  checksum: 10c0/1de0ffe2dd603920685d1b2027136051f095ab42be03e354a43713664e99617cf32cbdb61fc03742c329386d7cf9450edbf4593e50daeaae381e20627b477cd6
  languageName: node
  linkType: hard

"kind-of@npm:^3.0.2, kind-of@npm:^3.0.3, kind-of@npm:^3.2.0":
  version: 3.2.2
  resolution: "kind-of@npm:3.2.2"
  dependencies:
    is-buffer: "npm:^1.1.5"
  checksum: 10c0/7e34bc29d4b02c997f92f080de34ebb92033a96736bbb0bb2410e033a7e5ae6571f1fa37b2d7710018f95361473b816c604234197f4f203f9cf149d8ef1574d9
  languageName: node
  linkType: hard

"kind-of@npm:^4.0.0":
  version: 4.0.0
  resolution: "kind-of@npm:4.0.0"
  dependencies:
    is-buffer: "npm:^1.1.5"
  checksum: 10c0/d6c44c75ee36898142dfc7106afbd50593216c37f96acb81a7ab33ca1a6938ce97d5692b8fc8fccd035f83811a9d97749d68771116441a48eedd0b68e2973165
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 10c0/61cdff9623dabf3568b6445e93e31376bee1cdb93f8ba7033d86022c2a9b1791a1d9510e026e6465ebd701a6dd2f7b0808483ad8838341ac52f003f512e0b4c4
  languageName: node
  linkType: hard

"lazy-cache@npm:^1.0.3":
  version: 1.0.4
  resolution: "lazy-cache@npm:1.0.4"
  checksum: 10c0/00f4868a27dc5c491ad86f46068d19bc97c0402d6c7c1449a977fade8ce667d4723beac8e12fdb1d6237156dd25ab0d3c963422bdfcbc76fd25941bfe3c6f015
  languageName: node
  linkType: hard

"lcid@npm:^1.0.0":
  version: 1.0.0
  resolution: "lcid@npm:1.0.0"
  dependencies:
    invert-kv: "npm:^1.0.0"
  checksum: 10c0/87fb32196c3c80458778f34f71c042e114f3134a3c86c0d60ee9c94f0750e467d7ca0c005a5224ffd9d49a6e449b5e5c31e1544f1827765a0ba8747298f5980e
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10c0/effb03cad7c89dfa5bd4f6989364bfc79994c2042ec5966cb9b95990e2edee5cd8969ddf42616a0373ac49fac1403437deaf6e9050fbbaa3546093a59b9ac94e
  languageName: node
  linkType: hard

"lie@npm:~3.3.0":
  version: 3.3.0
  resolution: "lie@npm:3.3.0"
  dependencies:
    immediate: "npm:~3.0.5"
  checksum: 10c0/56dd113091978f82f9dc5081769c6f3b947852ecf9feccaf83e14a123bc630c2301439ce6182521e5fbafbde88e88ac38314327a4e0493a1bea7e0699a7af808
  languageName: node
  linkType: hard

"linebreak@npm:^1.1.0":
  version: 1.1.0
  resolution: "linebreak@npm:1.1.0"
  dependencies:
    base64-js: "npm:0.0.8"
    unicode-trie: "npm:^2.0.0"
  checksum: 10c0/b350c90d7b10db30345ed56cdb869548110ce73ccdc4337100eaee50755eed78e9823490e6f2d7ed0adde14f7ed2a12d8583015e072c54f34dc70b316fde133d
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10c0/3da6ee62d4cd9f03f5dc90b4df2540fb85b352081bee77fe4bbcd12c9000ead7f35e0a38b8d09a9bb99b13223446dd8689ff3c4959807620726d788701a83d2d
  languageName: node
  linkType: hard

"load-json-file@npm:^1.0.0":
  version: 1.1.0
  resolution: "load-json-file@npm:1.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    parse-json: "npm:^2.2.0"
    pify: "npm:^2.0.0"
    pinkie-promise: "npm:^2.0.0"
    strip-bom: "npm:^2.0.0"
  checksum: 10c0/2a5344c2d88643735a938fdca8582c0504e1c290577faa74f56b9cc187fa443832709a15f36e5771f779ec0878215a03abc8faf97ec57bb86092ceb7e0caef22
  languageName: node
  linkType: hard

"load-json-file@npm:^2.0.0":
  version: 2.0.0
  resolution: "load-json-file@npm:2.0.0"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    parse-json: "npm:^2.2.0"
    pify: "npm:^2.0.0"
    strip-bom: "npm:^3.0.0"
  checksum: 10c0/15cf1259361325fadfc54cd4ecc5d6729103c8873492001ba5473fb1ef753000f680c887db6c86fec69a4ede009efeb8c0c0c77b2a31bc54d2793767e25577c9
  languageName: node
  linkType: hard

"loader-runner@npm:^2.3.0":
  version: 2.4.0
  resolution: "loader-runner@npm:2.4.0"
  checksum: 10c0/1f723bd8318453c2d073d7befbf891ba6d2a02f22622688bf7d22e7ba527a0f9476c7fdfedc6bfa2b55c0389d9f406f3a5239ed1b33c9088d77cfed085086a1e
  languageName: node
  linkType: hard

"loader-utils@npm:^1.1.0":
  version: 1.4.2
  resolution: "loader-utils@npm:1.4.2"
  dependencies:
    big.js: "npm:^5.2.2"
    emojis-list: "npm:^3.0.0"
    json5: "npm:^1.0.1"
  checksum: 10c0/2b726088b5526f7605615e3e28043ae9bbd2453f4a85898e1151f3c39dbf7a2b65d09f3996bc588d92ac7e717ded529d3e1ea3ea42c433393be84a58234a2f53
  languageName: node
  linkType: hard

"locate-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "locate-path@npm:2.0.0"
  dependencies:
    p-locate: "npm:^2.0.0"
    path-exists: "npm:^3.0.0"
  checksum: 10c0/24efa0e589be6aa3c469b502f795126b26ab97afa378846cb508174211515633b770aa0ba610cab113caedab8d2a4902b061a08aaed5297c12ab6f5be4df0133
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10c0/d3972ab70dfe58ce620e64265f90162d247e87159b6126b01314dd67be43d50e96a50b517bce2d9452a79409c7614054c277b5232377de50416564a77ac7aad3
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10c0/402fa16a1edd7538de5b5903a90228aa48eb5533986ba7fa26606a49db2572bf414ff73a2c9f5d5fd36b31c46a5d5c7e1527749c07cbcf965ccff5fbdf32c506
  languageName: node
  linkType: hard

"lodash@npm:^4.17.11, lodash@npm:^4.17.14, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"loglevel@npm:^1.4.1":
  version: 1.9.2
  resolution: "loglevel@npm:1.9.2"
  checksum: 10c0/1e317fa4648fe0b4a4cffef6de037340592cee8547b07d4ce97a487abe9153e704b98451100c799b032c72bb89c9366d71c9fb8192ada8703269263ae77acdc7
  languageName: node
  linkType: hard

"longest@npm:^1.0.1":
  version: 1.0.1
  resolution: "longest@npm:1.0.1"
  checksum: 10c0/e77bd510ea4083cc202a8985be1d422d4183e1078775bcf6c5d9aee3e401d9094b44348c720f9d349f230293865b09ee611453ac4694422ad43a9a9bdb092c82
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0, loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10c0/655d110220983c1a4b9c0c679a2e8016d4b67f6e9c7b5435ff5979ecdb20d0813f4dec0a08674fcbdd4846a3f07edbb50a36811fd37930b94aaa0d9daceb017e
  languageName: node
  linkType: hard

"lop@npm:^0.4.2":
  version: 0.4.2
  resolution: "lop@npm:0.4.2"
  dependencies:
    duck: "npm:^0.1.12"
    option: "npm:~0.2.1"
    underscore: "npm:^1.13.1"
  checksum: 10c0/f0d72f8a80fc12e5c1ac7a480dd5aa8820d8a390e1d5ea0a3a40f9a830885b149ee9c61384e40f1e5f23bc50469f87de8b78dd08549bbb4787409cf58373e04d
  languageName: node
  linkType: hard

"loud-rejection@npm:^1.0.0":
  version: 1.6.0
  resolution: "loud-rejection@npm:1.6.0"
  dependencies:
    currently-unhandled: "npm:^0.4.1"
    signal-exit: "npm:^3.0.0"
  checksum: 10c0/aa060b3fe55ad96b97890f1b0a24bf81a2d612e397d6cc0374ce1cf7e021cd0247f0ddb68134499882d0843c2776371d5221b80b0b3beeca5133a6e7f27a3845
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^4.0.1":
  version: 4.1.5
  resolution: "lru-cache@npm:4.1.5"
  dependencies:
    pseudomap: "npm:^1.0.2"
    yallist: "npm:^2.1.2"
  checksum: 10c0/1ca5306814e5add9ec63556d6fd9b24a4ecdeaef8e9cea52cbf30301e6b88c8d8ddc7cab45b59b56eb763e6c45af911585dc89925a074ab65e1502e3fe8103cf
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10c0/89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.12":
  version: 0.30.17
  resolution: "magic-string@npm:0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
  checksum: 10c0/16826e415d04b88378f200fe022b53e638e3838b9e496edda6c0e086d7753a44a6ed187adc72d19f3623810589bf139af1a315541cd6a26ae0771a0193eaf7b8
  languageName: node
  linkType: hard

"make-cancellable-promise@npm:^1.3.1":
  version: 1.3.2
  resolution: "make-cancellable-promise@npm:1.3.2"
  checksum: 10c0/10aa0450c743dcf20b55414c433ca45926b775b22eb6d25fa386fc499a8f3fc64c70eb575d99bdd16667d300068f51702822c293bc4e72da7ff4f82d0ea48184
  languageName: node
  linkType: hard

"make-cancellable-promise@npm:^2.0.0":
  version: 2.0.0
  resolution: "make-cancellable-promise@npm:2.0.0"
  checksum: 10c0/e9d76281ea0005001c3de95d26ea6cd508f44ffd8fa2e19193b92d1e8bc03cf75f87750baec6de3dd032b12f8b747ba88fef3328309fe005dbbdd56ce70617c6
  languageName: node
  linkType: hard

"make-dir@npm:^3.1.0":
  version: 3.1.0
  resolution: "make-dir@npm:3.1.0"
  dependencies:
    semver: "npm:^6.0.0"
  checksum: 10c0/56aaafefc49c2dfef02c5c95f9b196c4eb6988040cf2c712185c7fe5c99b4091591a7fc4d4eafaaefa70ff763a26f6ab8c3ff60b9e75ea19876f49b18667ecaa
  languageName: node
  linkType: hard

"make-event-props@npm:^1.6.0":
  version: 1.6.2
  resolution: "make-event-props@npm:1.6.2"
  checksum: 10c0/ecf0b742e43a392c07e2267baca2397e750d38cc14ef3cb72ef8bfe4a8c8b0fd99a03a2eeab84a26c2b204f7c231da6af31fa26321fbfd413ded43ba1825e867
  languageName: node
  linkType: hard

"make-event-props@npm:^2.0.0":
  version: 2.0.0
  resolution: "make-event-props@npm:2.0.0"
  checksum: 10c0/8c799634839498064dd73c979842af9d1d5a6156fe447edda6144318c948cfa7edbc434b14cebd5898e148e4d52aa37ffa204a82c1e2ee43e5946442f8b2466d
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"mammoth@npm:^1.9.1":
  version: 1.9.1
  resolution: "mammoth@npm:1.9.1"
  dependencies:
    "@xmldom/xmldom": "npm:^0.8.6"
    argparse: "npm:~1.0.3"
    base64-js: "npm:^1.5.1"
    bluebird: "npm:~3.4.0"
    dingbat-to-unicode: "npm:^1.0.1"
    jszip: "npm:^3.7.1"
    lop: "npm:^0.4.2"
    path-is-absolute: "npm:^1.0.0"
    underscore: "npm:^1.13.1"
    xmlbuilder: "npm:^10.0.0"
  bin:
    mammoth: bin/mammoth
  checksum: 10c0/cf42235d5f972b4e0af2e342bf16b8ac5de1627bef3677c884c7114b25141d007d83f35a5e3a1307b485655cd80ed523719e15d65b2fd7f84ebdcae6cb9b158e
  languageName: node
  linkType: hard

"map-cache@npm:^0.2.2":
  version: 0.2.2
  resolution: "map-cache@npm:0.2.2"
  checksum: 10c0/05e3eb005c1b80b9f949ca007687640e8c5d0fc88dc45c3c3ab4902a3bec79d66a58f3e3b04d6985d90cd267c629c7b46c977e9c34433e8c11ecfcbb9f0fa290
  languageName: node
  linkType: hard

"map-obj@npm:^1.0.0, map-obj@npm:^1.0.1":
  version: 1.0.1
  resolution: "map-obj@npm:1.0.1"
  checksum: 10c0/ccca88395e7d38671ed9f5652ecf471ecd546924be2fb900836b9da35e068a96687d96a5f93dcdfa94d9a27d649d2f10a84595590f89a347fb4dda47629dcc52
  languageName: node
  linkType: hard

"map-visit@npm:^1.0.0":
  version: 1.0.0
  resolution: "map-visit@npm:1.0.0"
  dependencies:
    object-visit: "npm:^1.0.0"
  checksum: 10c0/fb3475e5311939a6147e339999113db607adc11c7c3cd3103e5e9dbf502898416ecba6b1c7c649c6d4d12941de00cee58b939756bdf20a9efe7d4fa5a5738b73
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"md5.js@npm:^1.3.4":
  version: 1.3.5
  resolution: "md5.js@npm:1.3.5"
  dependencies:
    hash-base: "npm:^3.0.0"
    inherits: "npm:^2.0.1"
    safe-buffer: "npm:^5.1.2"
  checksum: 10c0/b7bd75077f419c8e013fc4d4dada48be71882e37d69a44af65a2f2804b91e253441eb43a0614423a1c91bb830b8140b0dc906bc797245e2e275759584f4efcc5
  languageName: node
  linkType: hard

"media-engine@npm:^1.0.3":
  version: 1.0.3
  resolution: "media-engine@npm:1.0.3"
  checksum: 10c0/60e602ec362bc9e119b687608f3264a5980442c22f0d8ff4917003fc4046a3df49d4cbb7b0914bb4809f91d5da82310780d7c738b2bca8dc75ca6aadf28ca765
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: 10c0/d160f31246907e79fed398470285f21bafb45a62869dc469b1c8877f3f064f5eabc4bcc122f9479b8b605bc5c76187d7871cf84c4ee3ecd3e487da1993279928
  languageName: node
  linkType: hard

"mem@npm:^1.1.0":
  version: 1.1.0
  resolution: "mem@npm:1.1.0"
  dependencies:
    mimic-fn: "npm:^1.0.0"
  checksum: 10c0/f5150bb975a7d641375d3c1962426bf338952142a429ce01e9792b03df9b63d5911d3feb7e5e50f406531ace646f3fbe39b7dc716c729d617a28b3bbdc799649
  languageName: node
  linkType: hard

"memory-fs@npm:^0.4.0, memory-fs@npm:~0.4.1":
  version: 0.4.1
  resolution: "memory-fs@npm:0.4.1"
  dependencies:
    errno: "npm:^0.1.3"
    readable-stream: "npm:^2.0.1"
  checksum: 10c0/f114c44ad8285103cb0e71420cf5bb628d3eb6cbd918197f5951590ff56ba2072f4a97924949c170320cdf180d2da4e8d16a0edd92ba0ca2d2de51dc932841e2
  languageName: node
  linkType: hard

"meow@npm:^3.3.0":
  version: 3.7.0
  resolution: "meow@npm:3.7.0"
  dependencies:
    camelcase-keys: "npm:^2.0.0"
    decamelize: "npm:^1.1.2"
    loud-rejection: "npm:^1.0.0"
    map-obj: "npm:^1.0.1"
    minimist: "npm:^1.1.3"
    normalize-package-data: "npm:^2.3.4"
    object-assign: "npm:^4.0.1"
    read-pkg-up: "npm:^1.0.1"
    redent: "npm:^1.0.0"
    trim-newlines: "npm:^1.0.0"
  checksum: 10c0/e5ba4632b6558006b5f4df64b5a35e777d75629ab08d84f7bbc967e7603a396e16baa8f67aae26c7833a6a117e4857afef393e0b9aee21f52320e54812d9ae09
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.3":
  version: 1.0.3
  resolution: "merge-descriptors@npm:1.0.3"
  checksum: 10c0/866b7094afd9293b5ea5dcd82d71f80e51514bed33b4c4e9f516795dc366612a4cbb4dc94356e943a8a6914889a914530badff27f397191b9b75cda20b6bae93
  languageName: node
  linkType: hard

"merge-refs@npm:^1.3.0":
  version: 1.3.0
  resolution: "merge-refs@npm:1.3.0"
  peerDependencies:
    "@types/react": ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/403d20d283a595565a6bef813415df509dad12a5ad157f0ae04861b3aee4a3691971ccae7079e20497d9f367a478ad60e5b63a2ca9ffb2cc3d511284b49b4bd6
  languageName: node
  linkType: hard

"merge-refs@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-refs@npm:2.0.0"
  peerDependencies:
    "@types/react": ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/3a8c72da328ca68dd8f9e1168687a348baae585d9c1a5ae0f524e067663f00ce3e46465a54616d0cf65f03d55cc9b847b0902f4642c4425d6dc009610ea26ebb
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"merge@npm:^1.2.0":
  version: 1.2.1
  resolution: "merge@npm:1.2.1"
  checksum: 10c0/09f0ed1e85f336feb32d93d1243cb8930be22c7e6e4aa0c59a08a6f680d274dbe021c7d2a35ee8383cab2524efbb3f51985c3052e3dcf134951656511edf4693
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 10c0/bdf7cc72ff0a33e3eede03708c08983c4d7a173f91348b4b1e4f47d4cdbf734433ad971e7d1e8c77247d9e5cd8adb81ea4c67b0a2db526b758b2233d7814b8b2
  languageName: node
  linkType: hard

"micromatch@npm:^3.1.10, micromatch@npm:^3.1.4":
  version: 3.1.10
  resolution: "micromatch@npm:3.1.10"
  dependencies:
    arr-diff: "npm:^4.0.0"
    array-unique: "npm:^0.3.2"
    braces: "npm:^2.3.1"
    define-property: "npm:^2.0.2"
    extend-shallow: "npm:^3.0.2"
    extglob: "npm:^2.0.4"
    fragment-cache: "npm:^0.2.1"
    kind-of: "npm:^6.0.2"
    nanomatch: "npm:^1.2.9"
    object.pick: "npm:^1.3.0"
    regex-not: "npm:^1.0.0"
    snapdragon: "npm:^0.8.1"
    to-regex: "npm:^3.0.2"
  checksum: 10c0/531a32e7ac92bef60657820202be71b63d0f945c08a69cc4c239c0b19372b751483d464a850a2e3a5ff6cc9060641e43d44c303af104c1a27493d137d8af017f
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/166fa6eb926b9553f32ef81f5f531d27b4ce7da60e5baf8c021d043b27a388fb95e46a8038d5045877881e673f8134122b59624d5cecbd16eb50a42e7a6b5ca8
  languageName: node
  linkType: hard

"miller-rabin@npm:^4.0.0":
  version: 4.0.1
  resolution: "miller-rabin@npm:4.0.1"
  dependencies:
    bn.js: "npm:^4.0.0"
    brorand: "npm:^1.0.1"
  bin:
    miller-rabin: bin/miller-rabin
  checksum: 10c0/26b2b96f6e49dbcff7faebb78708ed2f5f9ae27ac8cbbf1d7c08f83cf39bed3d418c0c11034dce997da70d135cc0ff6f3a4c15dc452f8e114c11986388a64346
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-db@npm:>= 1.43.0 < 2":
  version: 1.54.0
  resolution: "mime-db@npm:1.54.0"
  checksum: 10c0/8d907917bc2a90fa2df842cdf5dfeaf509adc15fe0531e07bb2f6ab15992416479015828d6a74200041c492e42cce3ebf78e5ce714388a0a538ea9c53eece284
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:~2.1.17, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:1.6.0, mime@npm:^1.5.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"mimic-fn@npm:^1.0.0":
  version: 1.2.0
  resolution: "mimic-fn@npm:1.2.0"
  checksum: 10c0/ad55214aec6094c0af4c0beec1a13787556f8116ed88807cf3f05828500f21f93a9482326bcd5a077ae91e3e8795b4e76b5b4c8bb12237ff0e4043a365516cba
  languageName: node
  linkType: hard

"mimic-response@npm:^2.0.0":
  version: 2.1.0
  resolution: "mimic-response@npm:2.1.0"
  checksum: 10c0/717475c840f20deca87a16cb2f7561f9115f5de225ea2377739e09890c81aec72f43c81fd4984650c4044e66be5a846fa7a517ac7908f01009e1e624e19864d5
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0, minimalistic-assert@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: 10c0/96730e5601cd31457f81a296f521eb56036e6f69133c0b18c13fe941109d53ad23a4204d946a0d638d7f3099482a0cec8c9bb6d642604612ce43ee536be3dddd
  languageName: node
  linkType: hard

"minimalistic-crypto-utils@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-crypto-utils@npm:1.0.1"
  checksum: 10c0/790ecec8c5c73973a4fbf2c663d911033e8494d5fb0960a4500634766ab05d6107d20af896ca2132e7031741f19888154d44b2408ada0852446705441383e9f8
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minimist@npm:^1.1.3, minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10c0/19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 10c0/a91d8043f691796a8ac88df039da19933ef0f633e3d7f0d35dcd5373af49131cf2399bfc355f41515dc495e3990369c3858cd319e5c2722b4753c90bf3152462
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: "npm:^3.0.0"
    yallist: "npm:^4.0.0"
  checksum: 10c0/64fae024e1a7d0346a1102bb670085b17b7f95bf6cfdf5b128772ec8faf9ea211464ea4add406a3a6384a7d87a0cd1a96263692134323477b4fb43659a6cab78
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10c0/9f3bd35e41d40d02469cb30470c55ccc21cae0db40e08d1d0b1dff01cc8cc89a6f78e9c5d2b7c844e485ec0a8abc2238111213fdc5b2038e6d1012eacf316f78
  languageName: node
  linkType: hard

"mixin-deep@npm:^1.2.0":
  version: 1.3.2
  resolution: "mixin-deep@npm:1.3.2"
  dependencies:
    for-in: "npm:^1.0.2"
    is-extendable: "npm:^1.0.1"
  checksum: 10c0/cb39ffb73c377222391af788b4c83d1a6cecb2d9fceb7015384f8deb46e151a9b030c21ef59a79cb524d4557e3f74c7248ab948a62a6e7e296b42644863d183b
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/46ea0f3ffa8bc6a5bc0c7081ffc3907777f0ed6516888d40a518c5111f8366d97d2678911ad1a6882bf592fa9de6c784fea32e1687bb94e1f4944170af48a5cf
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"mkdirp@npm:~0.5.0":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: "npm:^1.2.6"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/e2e2be789218807b58abced04e7b49851d9e46e88a2f9539242cc8a92c9b5c3a0b9bab360bd3014e02a140fc4fbc58e31176c408b493f8a2a6f4986bd7527b01
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10c0/f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"multicast-dns-service-types@npm:^1.1.0":
  version: 1.1.0
  resolution: "multicast-dns-service-types@npm:1.1.0"
  checksum: 10c0/25abc0e9ee509f38d874e22b03d563b16009d3976760d29bed25bf70ea992cfe30b0403743f49342279c67178a03311d31ecc1ec54bf79af2e6fe55f11af2660
  languageName: node
  linkType: hard

"multicast-dns@npm:^6.0.1":
  version: 6.2.3
  resolution: "multicast-dns@npm:6.2.3"
  dependencies:
    dns-packet: "npm:^1.3.1"
    thunky: "npm:^1.0.2"
  bin:
    multicast-dns: cli.js
  checksum: 10c0/972fc50869e922d80d66eeb91ad39fd2e107241e0c791fc914e76578e4f7f3dfe3bf007020dd4d7ed4d0ffd69d9aa2238a9f8bbb4d160bd6eb3f35dde0c2c513
  languageName: node
  linkType: hard

"nan@npm:^2.12.1, nan@npm:^2.17.0":
  version: 2.23.0
  resolution: "nan@npm:2.23.0"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/b986dd257dca53ab43a3b6ca6d0eafde697b69e1d63b242fa4aece50ce97eb169f9c4a5d8eb0eb5f58d118a9595fee11f3198fa210f023440053bb6f54109e73
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.11":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/40e7f70b3d15f725ca072dfc4f74e81fcf1fbb02e491cf58ac0c79093adc9b0a73b152bcde57df4b79cd097e13023d7504acb38404a4da7bc1cd8e887b82fe0b
  languageName: node
  linkType: hard

"nanomatch@npm:^1.2.9":
  version: 1.2.13
  resolution: "nanomatch@npm:1.2.13"
  dependencies:
    arr-diff: "npm:^4.0.0"
    array-unique: "npm:^0.3.2"
    define-property: "npm:^2.0.2"
    extend-shallow: "npm:^3.0.2"
    fragment-cache: "npm:^0.2.1"
    is-windows: "npm:^1.0.2"
    kind-of: "npm:^6.0.2"
    object.pick: "npm:^1.3.0"
    regex-not: "npm:^1.0.0"
    snapdragon: "npm:^0.8.1"
    to-regex: "npm:^3.0.1"
  checksum: 10c0/0f5cefa755ca2e20c86332821995effb24acb79551ddaf51c1b9112628cad234a0d8fd9ac6aa56ad1f8bfad6ff6ae86e851acb960943249d9fa44b091479953a
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10c0/f5f9a7974bfb28a91afafa254b197f0f22c684d4a1731763dda960d2c8e375b36c7d690e0d9dc8fba774c537af14a7e979129bca23d88d052fbeb9466955e447
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10c0/3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"negotiator@npm:~0.6.4":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 10c0/3e677139c7fb7628a6f36335bf11a885a62c21d5390204590a1a214a5631fcbe5ea74ef6a610b60afe84b4d975cbe0566a23f20ee17c77c73e74b80032108dea
  languageName: node
  linkType: hard

"neo-async@npm:^2.5.0":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: 10c0/c2f5a604a54a8ec5438a342e1f356dff4bc33ccccdb6dc668d94fe8e5eccfc9d2c2eea6064b0967a767ba63b33763f51ccf2cd2441b461a7322656c1f06b3f5d
  languageName: node
  linkType: hard

"next-tick@npm:^1.1.0":
  version: 1.1.0
  resolution: "next-tick@npm:1.1.0"
  checksum: 10c0/3ba80dd805fcb336b4f52e010992f3e6175869c8d88bf4ff0a81d5d66e6049f89993463b28211613e58a6b7fe93ff5ccbba0da18d4fa574b96289e8f0b577f28
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.7":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-forge@npm:^0.10.0":
  version: 0.10.0
  resolution: "node-forge@npm:0.10.0"
  checksum: 10c0/9cbf9ac8fc3889a5a46b0248f7238ee4014770bf31d22e04c0c7f04ed91c8be4584c5f534cdf6037e99f236c636c925cba960501ed2b850e077512e152760663
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.3.0
  resolution: "node-gyp@npm:11.3.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/5f4ad5a729386f7b50096efd4934b06c071dbfbc7d7d541a66d6959a7dccd62f53ff3dc95fffb60bf99d8da1270e23769f82246fcaa6c5645a70c967ae9a3398
  languageName: node
  linkType: hard

"node-libs-browser@npm:^2.0.0":
  version: 2.2.1
  resolution: "node-libs-browser@npm:2.2.1"
  dependencies:
    assert: "npm:^1.1.1"
    browserify-zlib: "npm:^0.2.0"
    buffer: "npm:^4.3.0"
    console-browserify: "npm:^1.1.0"
    constants-browserify: "npm:^1.0.0"
    crypto-browserify: "npm:^3.11.0"
    domain-browser: "npm:^1.1.1"
    events: "npm:^3.0.0"
    https-browserify: "npm:^1.0.0"
    os-browserify: "npm:^0.3.0"
    path-browserify: "npm:0.0.1"
    process: "npm:^0.11.10"
    punycode: "npm:^1.2.4"
    querystring-es3: "npm:^0.2.0"
    readable-stream: "npm:^2.3.3"
    stream-browserify: "npm:^2.0.1"
    stream-http: "npm:^2.7.2"
    string_decoder: "npm:^1.0.0"
    timers-browserify: "npm:^2.0.4"
    tty-browserify: "npm:0.0.0"
    url: "npm:^0.11.0"
    util: "npm:^0.11.0"
    vm-browserify: "npm:^1.0.1"
  checksum: 10c0/0e05321a6396408903ed642231d2bca7dd96492d074c7af161ba06a63c95378bd3de50b4105eccbbc02d93ba3da69f0ff5e624bc2a8c92ca462ceb6a403e7986
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10c0/52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"nopt@npm:^5.0.0":
  version: 5.0.0
  resolution: "nopt@npm:5.0.0"
  dependencies:
    abbrev: "npm:1"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/fc5c4f07155cb455bf5fc3dd149fac421c1a40fd83c6bfe83aa82b52f02c17c5e88301321318adaa27611c8a6811423d51d29deaceab5fa158b585a61a551061
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"normalize-package-data@npm:^2.3.2, normalize-package-data@npm:^2.3.4":
  version: 2.5.0
  resolution: "normalize-package-data@npm:2.5.0"
  dependencies:
    hosted-git-info: "npm:^2.1.4"
    resolve: "npm:^1.10.0"
    semver: "npm:2 || 3 || 4 || 5"
    validate-npm-package-license: "npm:^3.0.1"
  checksum: 10c0/357cb1646deb42f8eb4c7d42c4edf0eec312f3628c2ef98501963cc4bbe7277021b2b1d977f982b2edce78f5a1014613ce9cf38085c3df2d76730481357ca504
  languageName: node
  linkType: hard

"normalize-path@npm:^2.1.1":
  version: 2.1.1
  resolution: "normalize-path@npm:2.1.1"
  dependencies:
    remove-trailing-separator: "npm:^1.0.1"
  checksum: 10c0/db814326ff88057437233361b4c7e9cac7b54815b051b57f2d341ce89b1d8ec8cbd43e7fa95d7652b3b69ea8fcc294b89b8530d556a84d1bdace94229e1e9a8b
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"normalize-svg-path@npm:^1.1.0":
  version: 1.1.0
  resolution: "normalize-svg-path@npm:1.1.0"
  dependencies:
    svg-arc-to-cubic-bezier: "npm:^3.0.0"
  checksum: 10c0/2e24e1d0a9ca7f172cec161d9c14fef616c57040664d82f6077d97c42e6e8a113f288dc0cb55e19fc4873baa0c5ddc7857d24d4a1b6df594924451d1adbb1b41
  languageName: node
  linkType: hard

"notistack@npm:^3.0.2":
  version: 3.0.2
  resolution: "notistack@npm:3.0.2"
  dependencies:
    clsx: "npm:^1.1.0"
    goober: "npm:^2.0.33"
  peerDependencies:
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/82a0270ee1b9e70bc1dfad4d7e1d2bbb6dc08ac887b920986dcfa58cc2aa975bd48b71f7cf3afc2e11badd142ba9ade5f708c858ee2080b9d4cd950b80c43811
  languageName: node
  linkType: hard

"npm-run-path@npm:^2.0.0":
  version: 2.0.2
  resolution: "npm-run-path@npm:2.0.2"
  dependencies:
    path-key: "npm:^2.0.0"
  checksum: 10c0/95549a477886f48346568c97b08c4fda9cdbf7ce8a4fbc2213f36896d0d19249e32d68d7451bdcbca8041b5fba04a6b2c4a618beaf19849505c05b700740f1de
  languageName: node
  linkType: hard

"npmlog@npm:^5.0.1":
  version: 5.0.1
  resolution: "npmlog@npm:5.0.1"
  dependencies:
    are-we-there-yet: "npm:^2.0.0"
    console-control-strings: "npm:^1.1.0"
    gauge: "npm:^3.0.0"
    set-blocking: "npm:^2.0.0"
  checksum: 10c0/489ba519031013001135c463406f55491a17fc7da295c18a04937fe3a4d523fd65e88dd418a28b967ab743d913fdeba1e29838ce0ad8c75557057c481f7d49fa
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: "npm:^1.0.0"
  checksum: 10c0/5fee7ff309727763689cfad844d979aedd2204a817fbaaf0e1603794a7c20db28548d7b024692f953557df6ce4a0ee4ae46cd8ebd9b36cfb300b9226b567c479
  languageName: node
  linkType: hard

"number-is-nan@npm:^1.0.0":
  version: 1.0.1
  resolution: "number-is-nan@npm:1.0.1"
  checksum: 10c0/cb97149006acc5cd512c13c1838223abdf202e76ddfa059c5e8e7507aff2c3a78cd19057516885a2f6f5b576543dc4f7b6f3c997cc7df53ae26c260855466df5
  languageName: node
  linkType: hard

"nwsapi@npm:^2.2.2":
  version: 2.2.20
  resolution: "nwsapi@npm:2.2.20"
  checksum: 10c0/07f4dafa3186aef7c007863e90acd4342a34ba9d44b22f14f644fdb311f6086887e21c2fc15efaa826c2bc39ab2bc841364a1a630e7c87e0cb723ba59d729297
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-copy@npm:^0.1.0":
  version: 0.1.0
  resolution: "object-copy@npm:0.1.0"
  dependencies:
    copy-descriptor: "npm:^0.1.0"
    define-property: "npm:^0.2.5"
    kind-of: "npm:^3.0.3"
  checksum: 10c0/79314b05e9d626159a04f1d913f4c4aba9eae8848511cf5f4c8e3b04bb3cc313b65f60357f86462c959a14c2d58380fedf89b6b32ecec237c452a5ef3900a293
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3, object-inspect@npm:^1.13.4":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10c0/d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"object-is@npm:^1.1.5":
  version: 1.1.6
  resolution: "object-is@npm:1.1.6"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
  checksum: 10c0/506af444c4dce7f8e31f34fc549e2fb8152d6b9c4a30c6e62852badd7f520b579c679af433e7a072f9d78eb7808d230dc12e1cf58da9154dfbf8813099ea0fe0
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10c0/b11f7ccdbc6d406d1f186cdadb9d54738e347b2692a14439ca5ac70c225fa6db46db809711b78589866d47b25fc3e8dee0b4c722ac751e11180f9380e3d8601d
  languageName: node
  linkType: hard

"object-visit@npm:^1.0.0":
  version: 1.0.1
  resolution: "object-visit@npm:1.0.1"
  dependencies:
    isobject: "npm:^3.0.0"
  checksum: 10c0/086b475bda24abd2318d2b187c3e928959b89f5cb5883d6fe5a42d03719b61fc18e765f658de9ac8730e67ba9ff26d61e73d991215948ff9ecefe771e0071029
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4, object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    object-keys: "npm:^1.1.1"
  checksum: 10c0/3b2732bd860567ea2579d1567525168de925a8d852638612846bd8082b3a1602b7b89b67b09913cbb5b9bd6e95923b2ae73580baa9d99cb4e990564e8cbf5ddc
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.9":
  version: 1.1.9
  resolution: "object.entries@npm:1.1.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.1.1"
  checksum: 10c0/d4b8c1e586650407da03370845f029aa14076caca4e4d4afadbc69cfb5b78035fd3ee7be417141abdb0258fa142e59b11923b4c44d8b1255b28f5ffcc50da7db
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/cd4327e6c3369cfa805deb4cbbe919bfb7d3aeebf0bcaba291bb568ea7169f8f8cdbcabe2f00b40db0c20cd20f08e11b5f3a5a36fb7dd3fe04850c50db3bf83b
  languageName: node
  linkType: hard

"object.pick@npm:^1.3.0":
  version: 1.3.0
  resolution: "object.pick@npm:1.3.0"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: 10c0/cd316ec986e49895a28f2df9182de9cdeee57cd2a952c122aacc86344c28624fe002d9affc4f48b5014ec7c033da9942b08821ddb44db8c5bac5b3ec54bdc31e
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6, object.values@npm:^1.2.1":
  version: 1.2.1
  resolution: "object.values@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/3c47814fdc64842ae3d5a74bc9d06bdd8d21563c04d9939bf6716a9c00596a4ebc342552f8934013d1ec991c74e3671b26710a0c51815f0b603795605ab6b2c9
  languageName: node
  linkType: hard

"obuf@npm:^1.0.0, obuf@npm:^1.1.2":
  version: 1.1.2
  resolution: "obuf@npm:1.1.2"
  checksum: 10c0/520aaac7ea701618eacf000fc96ae458e20e13b0569845800fc582f81b386731ab22d55354b4915d58171db00e79cfcd09c1638c02f89577ef092b38c65b7d81
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"on-headers@npm:~1.1.0":
  version: 1.1.0
  resolution: "on-headers@npm:1.1.0"
  checksum: 10c0/2c3b6b0d68ec9adbd561dc2d61c9b14da8ac03d8a2f0fd9e97bdf0600c887d5d97f664ff3be6876cf40cda6e3c587d73a4745e10b426ac50c7664fc5a0dfc0a1
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"opn@npm:^5.1.0":
  version: 5.5.0
  resolution: "opn@npm:5.5.0"
  dependencies:
    is-wsl: "npm:^1.1.0"
  checksum: 10c0/03f78b1ab464fd0d97543e2a90e47ca872e2324696bc13f741467693060fe058e87e38e9cfc9f3b568e60dfb31579fbe664d8e806b2f219262c423da953bba4c
  languageName: node
  linkType: hard

"option@npm:~0.2.1":
  version: 0.2.4
  resolution: "option@npm:0.2.4"
  checksum: 10c0/b605e5f3f65b21e0a9ec49a4ead50acb953696678109bb0decd80cc4cc4b466691c14472b2e281866cef513fc63f0310a09677c2d4cedd1e0d9607be1ce25831
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10c0/4afb687a059ee65b61df74dfe87d8d6815cd6883cb8b3d5883a910df72d0f5d029821f37025e4bccf4048873dbdb09acc6d303d27b8f76b1a80dd5a7d5334675
  languageName: node
  linkType: hard

"original@npm:>=0.0.5":
  version: 1.0.2
  resolution: "original@npm:1.0.2"
  dependencies:
    url-parse: "npm:^1.4.3"
  checksum: 10c0/af143d5be62b055ffb6fc2a0d08a650b822f48feca4ea21940c1f7a147727a317faedca254394ad0028ddaa4eecf7cf4376c370d746386cc35974e9122ebb14a
  languageName: node
  linkType: hard

"os-browserify@npm:^0.3.0":
  version: 0.3.0
  resolution: "os-browserify@npm:0.3.0"
  checksum: 10c0/6ff32cb1efe2bc6930ad0fd4c50e30c38010aee909eba8d65be60af55efd6cbb48f0287e3649b4e3f3a63dce5a667b23c187c4293a75e557f0d5489d735bcf52
  languageName: node
  linkType: hard

"os-locale@npm:^1.4.0":
  version: 1.4.0
  resolution: "os-locale@npm:1.4.0"
  dependencies:
    lcid: "npm:^1.0.0"
  checksum: 10c0/302173159d562000ddf982ed75c493a0d861e91372c9e1b13aab21590ff2e1ba264a41995b29be8dc5278a6127ffcd2ad5591779e8164a570fc5fa6c0787b057
  languageName: node
  linkType: hard

"os-locale@npm:^2.0.0":
  version: 2.1.0
  resolution: "os-locale@npm:2.1.0"
  dependencies:
    execa: "npm:^0.7.0"
    lcid: "npm:^1.0.0"
    mem: "npm:^1.1.0"
  checksum: 10c0/6f1acc060552a59f477ab541e9149a712f93a4d7b5262d070698dbe98cf047f35c7685d759a86dc56c12b76fdfbab1bf7216a74232263efbe7365de2a5d70834
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.6"
    object-keys: "npm:^1.1.1"
    safe-push-apply: "npm:^1.0.0"
  checksum: 10c0/6dfeb3455bff92ec3f16a982d4e3e65676345f6902d9f5ded1d8265a6318d0200ce461956d6d1c70053c7fe9f9fe65e552faac03f8140d37ef0fdd108e67013a
  languageName: node
  linkType: hard

"p-finally@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-finally@npm:1.0.0"
  checksum: 10c0/6b8552339a71fe7bd424d01d8451eea92d379a711fc62f6b2fe64cad8a472c7259a236c9a22b4733abca0b5666ad503cb497792a0478c5af31ded793d00937e7
  languageName: node
  linkType: hard

"p-limit@npm:^1.1.0":
  version: 1.3.0
  resolution: "p-limit@npm:1.3.0"
  dependencies:
    p-try: "npm:^1.0.0"
  checksum: 10c0/5c1b1d53d180b2c7501efb04b7c817448e10efe1ba46f4783f8951994d5027e4cd88f36ad79af50546682594c4ebd11702ac4b9364c47f8074890e2acad0edee
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-locate@npm:^2.0.0":
  version: 2.0.0
  resolution: "p-locate@npm:2.0.0"
  dependencies:
    p-limit: "npm:^1.1.0"
  checksum: 10c0/82da4be88fb02fd29175e66021610c881938d3cc97c813c71c1a605fac05617d57fd5d3b337494a6106c0edb2a37c860241430851411f1b265108cead34aee67
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10c0/2290d627ab7903b8b70d11d384fee714b797f6040d9278932754a6860845c4d3190603a0772a663c8cb5a7b21d1b16acb3a6487ebcafa9773094edc3dfe6009a
  languageName: node
  linkType: hard

"p-map@npm:^1.1.1":
  version: 1.2.0
  resolution: "p-map@npm:1.2.0"
  checksum: 10c0/8474eb31eff94640522e17a55d1273eed1021749d36616c5069d72255e356bf017d25c56387fe6d480710bd2cb48bb8c5cf0df8834331b2386d574d4a512e224
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"p-try@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-try@npm:1.0.0"
  checksum: 10c0/757ba31de5819502b80c447826fac8be5f16d3cb4fbf9bc8bc4971dba0682e84ac33e4b24176ca7058c69e29f64f34d8d9e9b08e873b7b7bb0aa89d620fa224a
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"pako@npm:^0.2.5":
  version: 0.2.9
  resolution: "pako@npm:0.2.9"
  checksum: 10c0/79c1806ebcf325b60ae599e4d7227c2e346d7b829dc20f5cf24cef07c934079dc3a61c5b3c8278a2f7a190c4a613e343ea11e5302dbe252efd11712df4b6b041
  languageName: node
  linkType: hard

"pako@npm:^1.0.10, pako@npm:^1.0.11, pako@npm:^1.0.6, pako@npm:~1.0.2, pako@npm:~1.0.5":
  version: 1.0.11
  resolution: "pako@npm:1.0.11"
  checksum: 10c0/86dd99d8b34c3930345b8bbeb5e1cd8a05f608eeb40967b293f72fe469d0e9c88b783a8777e4cc7dc7c91ce54c5e93d88ff4b4f060e6ff18408fd21030d9ffbe
  languageName: node
  linkType: hard

"pako@npm:^2.1.0":
  version: 2.1.0
  resolution: "pako@npm:2.1.0"
  checksum: 10c0/8e8646581410654b50eb22a5dfd71159cae98145bd5086c9a7a816ec0370b5f72b4648d08674624b3870a521e6a3daffd6c2f7bc00fdefc7063c9d8232ff5116
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10c0/c63d6e80000d4babd11978e0d3fee386ca7752a02b035fd2435960ffaa7219dc42146f07069fb65e6e8bf1caef89daf9af7535a39bddf354d78bf50d8294f556
  languageName: node
  linkType: hard

"parse-asn1@npm:^5.0.0, parse-asn1@npm:^5.1.7":
  version: 5.1.7
  resolution: "parse-asn1@npm:5.1.7"
  dependencies:
    asn1.js: "npm:^4.10.1"
    browserify-aes: "npm:^1.2.0"
    evp_bytestokey: "npm:^1.0.3"
    hash-base: "npm:~3.0"
    pbkdf2: "npm:^3.1.2"
    safe-buffer: "npm:^5.2.1"
  checksum: 10c0/05eb5937405c904eb5a7f3633bab1acc11f4ae3478a07ef5c6d81ce88c3c0e505ff51f9c7b935ebc1265c868343793698fc91025755a895d0276f620f95e8a82
  languageName: node
  linkType: hard

"parse-json@npm:^2.2.0":
  version: 2.2.0
  resolution: "parse-json@npm:2.2.0"
  dependencies:
    error-ex: "npm:^1.2.0"
  checksum: 10c0/7a90132aa76016f518a3d5d746a21b3f1ad0f97a68436ed71b6f995b67c7151141f5464eea0c16c59aec9b7756519a0e3007a8f98cf3714632d509ec07736df6
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 10c0/77947f2253005be7a12d858aedbafa09c9ae39eb4863adf330f7b416ca4f4a08132e453e08de2db46459256fb66afaac5ee758b44fe6541b7cdaf9d252e59585
  languageName: node
  linkType: hard

"parse-svg-path@npm:^0.1.2":
  version: 0.1.2
  resolution: "parse-svg-path@npm:0.1.2"
  checksum: 10c0/005af72d535f47bbe4673108e51d657b81cb19dcc4f10ab425dd9f824066ef300ddbc83640607ef318f853a9fce7065114705531dcf88fb87fe58c287b562f2b
  languageName: node
  linkType: hard

"parse5-htmlparser2-tree-adapter@npm:^7.1.0":
  version: 7.1.0
  resolution: "parse5-htmlparser2-tree-adapter@npm:7.1.0"
  dependencies:
    domhandler: "npm:^5.0.3"
    parse5: "npm:^7.0.0"
  checksum: 10c0/e5a4e0b834c84c9e244b5749f8d007f4baaeafac7a1da2c54be3421ffd9ef8fdec4f198bf55cda22e88e6ba95e9943f6ed5aa3ae5900b39972ebf5dc8c3f4722
  languageName: node
  linkType: hard

"parse5-parser-stream@npm:^7.1.2":
  version: 7.1.2
  resolution: "parse5-parser-stream@npm:7.1.2"
  dependencies:
    parse5: "npm:^7.0.0"
  checksum: 10c0/e236c61000d38ecad369e725a48506b051cebad8abb00e6d4e8bff7aa85c183820fcb45db1559cc90955bdbbdbd665ea94c41259594e74566fff411478dc7fcb
  languageName: node
  linkType: hard

"parse5@npm:^7.0.0, parse5@npm:^7.1.1, parse5@npm:^7.3.0":
  version: 7.3.0
  resolution: "parse5@npm:7.3.0"
  dependencies:
    entities: "npm:^6.0.0"
  checksum: 10c0/7fd2e4e247e85241d6f2a464d0085eed599a26d7b0a5233790c49f53473232eb85350e8133344d9b3fd58b89339e7ad7270fe1f89d28abe50674ec97b87f80b5
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.2, parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10c0/90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"pascalcase@npm:^0.1.1":
  version: 0.1.1
  resolution: "pascalcase@npm:0.1.1"
  checksum: 10c0/48dfe90618e33810bf58211d8f39ad2c0262f19ad6354da1ba563935b5f429f36409a1fb9187c220328f7a4dc5969917f8e3e01ee089b5f1627b02aefe39567b
  languageName: node
  linkType: hard

"path-browserify@npm:0.0.1":
  version: 0.0.1
  resolution: "path-browserify@npm:0.0.1"
  checksum: 10c0/3d59710cddeea06509d91935196185900f3d9d29376dff68ff0e146fbd41d0fb304e983d0158f30cabe4dd2ffcc6a7d3d977631994ee984c88e66aed50a1ccd3
  languageName: node
  linkType: hard

"path-dirname@npm:^1.0.0":
  version: 1.0.2
  resolution: "path-dirname@npm:1.0.2"
  checksum: 10c0/71e59be2bada7c91f62b976245fd421b7cb01fde3207fe53a82d8880621ad04fd8b434e628c9cf4e796259fc168a107d77cd56837725267c5b2c58cefe2c4e1b
  languageName: node
  linkType: hard

"path-exists@npm:^2.0.0":
  version: 2.1.0
  resolution: "path-exists@npm:2.1.0"
  dependencies:
    pinkie-promise: "npm:^2.0.0"
  checksum: 10c0/87352f1601c085d5a6eb202f60e5c016c1b790bd0bc09398af446ed3f5c4510b4531ff99cf8acac2d91868886e792927b4292f768b35a83dce12588fb7cbb46e
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 10c0/17d6a5664bc0a11d48e2b2127d28a0e58822c6740bde30403f08013da599182289c56518bec89407e3f31d3c2b6b296a4220bc3f867f0911fee6952208b04167
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10c0/8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10c0/127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-is-inside@npm:^1.0.1":
  version: 1.0.2
  resolution: "path-is-inside@npm:1.0.2"
  checksum: 10c0/7fdd4b41672c70461cce734fc222b33e7b447fa489c7c4377c95e7e6852d83d69741f307d88ec0cc3b385b41cb4accc6efac3c7c511cd18512e95424f5fa980c
  languageName: node
  linkType: hard

"path-key@npm:^2.0.0":
  version: 2.0.1
  resolution: "path-key@npm:2.0.1"
  checksum: 10c0/dd2044f029a8e58ac31d2bf34c34b93c3095c1481942960e84dd2faa95bbb71b9b762a106aead0646695330936414b31ca0bd862bf488a937ad17c8c5d73b32b
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10c0/11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.12":
  version: 0.1.12
  resolution: "path-to-regexp@npm:0.1.12"
  checksum: 10c0/1c6ff10ca169b773f3bba943bbc6a07182e332464704572962d277b900aeee81ac6aa5d060ff9e01149636c30b1f63af6e69dd7786ba6e0ddb39d4dee1f0645b
  languageName: node
  linkType: hard

"path-type@npm:^1.0.0":
  version: 1.1.0
  resolution: "path-type@npm:1.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    pify: "npm:^2.0.0"
    pinkie-promise: "npm:^2.0.0"
  checksum: 10c0/2b8c348cb52bbc0c0568afa10a0a5d8f6233adfe5ae75feb56064f6aed6324ab74185c61c2545f4e52ca08acdc76005f615da4e127ed6eecb866002cf491f350
  languageName: node
  linkType: hard

"path-type@npm:^2.0.0":
  version: 2.0.0
  resolution: "path-type@npm:2.0.0"
  dependencies:
    pify: "npm:^2.0.0"
  checksum: 10c0/e475cead839e65a2f8fdde634b24a4116b49daea3917470552e19d49c63e59ef17963bec2f57df2c72a85fcd1f86c8850d9742e68dba9c9c8d9bcac38bab03d6
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 10c0/666f6973f332f27581371efaf303fd6c272cc43c2057b37aa99e3643158c7e4b2626549555d88626e99ea9e046f82f32e41bbde5f1508547e9a11b149b52387c
  languageName: node
  linkType: hard

"path2d@npm:^0.2.0":
  version: 0.2.2
  resolution: "path2d@npm:0.2.2"
  checksum: 10c0/1bb76c7f275d07f1bc7ca12171d828e91bf8a12596f0765a52e9d4d47fe1a428455dc1dd4c9002924a9bc554f6ac25e09a6c22eaecf32e5e33fba2985b5168f8
  languageName: node
  linkType: hard

"pathe@npm:^2.0.1":
  version: 2.0.3
  resolution: "pathe@npm:2.0.3"
  checksum: 10c0/c118dc5a8b5c4166011b2b70608762e260085180bb9e33e80a50dcdb1e78c010b1624f4280c492c92b05fc276715a4c357d1f9edc570f8f1b3d90b6839ebaca1
  languageName: node
  linkType: hard

"pbkdf2@npm:^3.1.2":
  version: 3.1.3
  resolution: "pbkdf2@npm:3.1.3"
  dependencies:
    create-hash: "npm:~1.1.3"
    create-hmac: "npm:^1.1.7"
    ripemd160: "npm:=2.0.1"
    safe-buffer: "npm:^5.2.1"
    sha.js: "npm:^2.4.11"
    to-buffer: "npm:^1.2.0"
  checksum: 10c0/12779463dfb847701f186e0b7e5fd538a1420409a485dcf5100689c2b3ec3cb113204e82a68668faf3b6dd76ec19260b865313c9d3a9c252807163bdc24652ae
  languageName: node
  linkType: hard

"pdf-lib@npm:^1.17.1":
  version: 1.17.1
  resolution: "pdf-lib@npm:1.17.1"
  dependencies:
    "@pdf-lib/standard-fonts": "npm:^1.0.0"
    "@pdf-lib/upng": "npm:^1.0.1"
    pako: "npm:^1.0.11"
    tslib: "npm:^1.11.1"
  checksum: 10c0/a9489a402880dacd1389a3e14ff8f6139d58e3bc82f26b0fcbd0798b841aee6ccb7fcfab0992b574e57b40404ced0330a7170b3c6467363461a9df5d9daec489
  languageName: node
  linkType: hard

"pdfjs-dist@npm:4.3.136":
  version: 4.3.136
  resolution: "pdfjs-dist@npm:4.3.136"
  dependencies:
    canvas: "npm:^2.11.2"
    path2d: "npm:^0.2.0"
  dependenciesMeta:
    canvas:
      optional: true
    path2d:
      optional: true
  checksum: 10c0/4cb88d737f56100d45d2c9c0c6e8dc2f3dfd856362a55e47801ae32c52b2f12b3e74819c3cafb995a1e84c43eb580598a69c6710a78b1a11cdb52ef24bf8a633
  languageName: node
  linkType: hard

"pdfjs-dist@npm:5.3.31":
  version: 5.3.31
  resolution: "pdfjs-dist@npm:5.3.31"
  dependencies:
    "@napi-rs/canvas": "npm:^0.1.67"
  dependenciesMeta:
    "@napi-rs/canvas":
      optional: true
  checksum: 10c0/7abbd2bff8ebec77892cd03eb6b3367e1ffec1b15efd188937e07be7851e1892e99aba498debc3c42fe8e044ec451030923cee3108196c8fa71068d99487c3c1
  languageName: node
  linkType: hard

"pdfjs-dist@npm:^2.0.197":
  version: 2.16.105
  resolution: "pdfjs-dist@npm:2.16.105"
  dependencies:
    dommatrix: "npm:^1.0.3"
    web-streams-polyfill: "npm:^3.2.1"
  peerDependencies:
    worker-loader: ^3.0.8
  peerDependenciesMeta:
    worker-loader:
      optional: true
  checksum: 10c0/903a2d95fb3b8a2ec66fb3164813e6181512a144abc39b7f87a735fbcf3384b1d2ee3c6ed8dde70bfbe87c8ae5f87fea8c799c22a2ebe2669ed8573bc076d1f6
  languageName: node
  linkType: hard

"pdfjs-dist@npm:^4.5.136":
  version: 4.10.38
  resolution: "pdfjs-dist@npm:4.10.38"
  dependencies:
    "@napi-rs/canvas": "npm:^0.1.65"
  dependenciesMeta:
    "@napi-rs/canvas":
      optional: true
  checksum: 10c0/77b022109be7aac00372750a53decea3979409e6ef1cf93bf554351569cd4d1fafc70afae4a9a3e4b4de3facf59d3acd54d324b0fcff781374bcb00493d449ce
  languageName: node
  linkType: hard

"pdfjs-dist@npm:^5.3.93":
  version: 5.3.93
  resolution: "pdfjs-dist@npm:5.3.93"
  dependencies:
    "@napi-rs/canvas": "npm:^0.1.71"
  dependenciesMeta:
    "@napi-rs/canvas":
      optional: true
  checksum: 10c0/4956f1ef76cbf35f77ca2341b5bb83de4bdd95c10bb7b3f2472f51cf302da1f3d2f734a991b4a7935a61528c11f83051ff8ccd1333bf8d5e506a464fc3bd708a
  languageName: node
  linkType: hard

"performance-now@npm:^2.1.0":
  version: 2.1.0
  resolution: "performance-now@npm:2.1.0"
  checksum: 10c0/22c54de06f269e29f640e0e075207af57de5052a3d15e360c09b9a8663f393f6f45902006c1e71aa8a5a1cdfb1a47fe268826f8496d6425c362f00f5bc3e85d9
  languageName: node
  linkType: hard

"picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.2.2, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2, picomatch@npm:^4.0.3":
  version: 4.0.3
  resolution: "picomatch@npm:4.0.3"
  checksum: 10c0/9582c951e95eebee5434f59e426cddd228a7b97a0161a375aed4be244bd3fe8e3a31b846808ea14ef2c8a2527a6eeab7b3946a67d5979e81694654f939473ae2
  languageName: node
  linkType: hard

"pify@npm:^2.0.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 10c0/551ff8ab830b1052633f59cb8adc9ae8407a436e06b4a9718bcb27dc5844b83d535c3a8512b388b6062af65a98c49bdc0dd523d8b2617b188f7c8fee457158dc
  languageName: node
  linkType: hard

"pify@npm:^3.0.0":
  version: 3.0.0
  resolution: "pify@npm:3.0.0"
  checksum: 10c0/fead19ed9d801f1b1fcd0638a1ac53eabbb0945bf615f2f8806a8b646565a04a1b0e7ef115c951d225f042cca388fdc1cd3add46d10d1ed6951c20bd2998af10
  languageName: node
  linkType: hard

"pinkie-promise@npm:^2.0.0":
  version: 2.0.1
  resolution: "pinkie-promise@npm:2.0.1"
  dependencies:
    pinkie: "npm:^2.0.0"
  checksum: 10c0/11b5e5ce2b090c573f8fad7b517cbca1bb9a247587306f05ae71aef6f9b2cd2b923c304aa9663c2409cfde27b367286179f1379bc4ec18a3fbf2bb0d473b160a
  languageName: node
  linkType: hard

"pinkie@npm:^2.0.0":
  version: 2.0.4
  resolution: "pinkie@npm:2.0.4"
  checksum: 10c0/25228b08b5597da42dc384221aa0ce56ee0fbf32965db12ba838e2a9ca0193c2f0609c45551ee077ccd2060bf109137fdb185b00c6d7e0ed7e35006d20fdcbc6
  languageName: node
  linkType: hard

"pizzip@npm:^3.2.0":
  version: 3.2.0
  resolution: "pizzip@npm:3.2.0"
  dependencies:
    pako: "npm:^2.1.0"
  checksum: 10c0/8581befc8317fdf554aa02efc5cb1fbaafecd69e8a0c5fcc0f16a5a84e92312802844925607cebb10564ac951ba58c77f9304ed43dcc145d514a9796318b90df
  languageName: node
  linkType: hard

"pkg-dir@npm:^2.0.0":
  version: 2.0.0
  resolution: "pkg-dir@npm:2.0.0"
  dependencies:
    find-up: "npm:^2.1.0"
  checksum: 10c0/7cdc46c4921bf2c5f9a438851d16243ddde9906928116647ec7784982dd9038ea61c964fbca6f489201845742188180ecd1001b4f69781de1d1dc7d100b14089
  languageName: node
  linkType: hard

"portfinder@npm:^1.0.9":
  version: 1.0.37
  resolution: "portfinder@npm:1.0.37"
  dependencies:
    async: "npm:^3.2.6"
    debug: "npm:^4.3.6"
  checksum: 10c0/eabd2764ced7bb0e6da7a1382bb77f9531309f7782fb6169021d05eecff0c0a17958bcf87573047a164dd0bb23f294d5d74b08ffe58c47005c28ed92eea9a6a7
  languageName: node
  linkType: hard

"posix-character-classes@npm:^0.1.0":
  version: 0.1.1
  resolution: "posix-character-classes@npm:0.1.1"
  checksum: 10c0/cce88011548a973b4af58361cd8f5f7b5a6faff8eef0901565802f067bcabf82597e920d4c97c22068464be3cbc6447af589f6cc8a7d813ea7165be60a0395bc
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: 10c0/c810983414142071da1d644662ce4caebce890203eb2bc7bf119f37f3fe5796226e117e6cca146b521921fa6531072674174a3325066ac66fce089a53e1e5196
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.2, postcss-value-parser@npm:^4.1.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 10c0/f4142a4f56565f77c1831168e04e3effd9ffcc5aebaf0f538eee4b2d465adfd4b85a44257bb48418202a63806a7da7fe9f56c330aebb3cac898e46b4cbf49161
  languageName: node
  linkType: hard

"postcss@npm:^8.5.6":
  version: 8.5.6
  resolution: "postcss@npm:8.5.6"
  dependencies:
    nanoid: "npm:^3.3.11"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10c0/5127cc7c91ed7a133a1b7318012d8bfa112da9ef092dddf369ae699a1f10ebbd89b1b9f25f3228795b84585c72aabd5ced5fc11f2ba467eedf7b081a66fad024
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10c0/b00d617431e7886c520a6f498a2e14c75ec58f6d93ba48c3b639cf241b54232d90daa05d83a9e9b9fef6baa63cb7e1e4602c2372fea5bc169668401eb127d0cd
  languageName: node
  linkType: hard

"prettier@npm:^3.5.0, prettier@npm:^3.6.2":
  version: 3.6.2
  resolution: "prettier@npm:3.6.2"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10c0/488cb2f2b99ec13da1e50074912870217c11edaddedeadc649b1244c749d15ba94e846423d062e2c4c9ae683e2d65f754de28889ba06e697ac4f988d44f45812
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 10c0/bec089239487833d46b59d80327a1605e1c5287eaad770a291add7f45fda1bb5e28b38e0e061add0a1d0ee0984788ce74fa394d345eed1c420cacf392c554367
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: 10c0/40c3ce4b7e6d4b8c3355479df77aeed46f81b279818ccdc500124e6a5ab882c0cc81ff7ea16384873a95a74c4570b01b120f287abbdd4c877931460eca6084b3
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"prop-types@npm:^15.6.2, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 10c0/59ece7ca2fb9838031d73a48d4becb9a7cc1ed10e610517c7d8f19a1e02fa47f7c27d557d8a5702bec3cfeccddc853579832b43f449e54635803f277b1c78077
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: 10c0/c3eed999781a35f7fd935f398b6d8920b6fb00bbc14287bc6de78128ccc1a02c89b95b56742bf7cf0362cc333c61d138532049c7dedc7a328ef13343eff81210
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10c0/fe7dd8b1bdbbbea18d1459107729c3e4a2243ca870d26d34c2c1bcd3e4425b7bcc5112362df2d93cc7fb9746f6142b5e272fd1cc5c86ddf8580175186f6ad42b
  languageName: node
  linkType: hard

"prr@npm:~1.0.1":
  version: 1.0.1
  resolution: "prr@npm:1.0.1"
  checksum: 10c0/5b9272c602e4f4472a215e58daff88f802923b84bc39c8860376bb1c0e42aaf18c25d69ad974bd06ec6db6f544b783edecd5502cd3d184748d99080d68e4be5f
  languageName: node
  linkType: hard

"pseudomap@npm:^1.0.2":
  version: 1.0.2
  resolution: "pseudomap@npm:1.0.2"
  checksum: 10c0/5a91ce114c64ed3a6a553aa7d2943868811377388bb31447f9d8028271bae9b05b340fe0b6961a64e45b9c72946aeb0a4ab635e8f7cb3715ffd0ff2beeb6a679
  languageName: node
  linkType: hard

"psl@npm:^1.1.33":
  version: 1.15.0
  resolution: "psl@npm:1.15.0"
  dependencies:
    punycode: "npm:^2.3.1"
  checksum: 10c0/d8d45a99e4ca62ca12ac3c373e63d80d2368d38892daa40cfddaa1eb908be98cd549ac059783ef3a56cfd96d57ae8e2fd9ae53d1378d90d42bc661ff924e102a
  languageName: node
  linkType: hard

"public-encrypt@npm:^4.0.3":
  version: 4.0.3
  resolution: "public-encrypt@npm:4.0.3"
  dependencies:
    bn.js: "npm:^4.1.0"
    browserify-rsa: "npm:^4.0.0"
    create-hash: "npm:^1.1.0"
    parse-asn1: "npm:^5.0.0"
    randombytes: "npm:^2.0.1"
    safe-buffer: "npm:^5.1.2"
  checksum: 10c0/6c2cc19fbb554449e47f2175065d6b32f828f9b3badbee4c76585ac28ae8641aafb9bb107afc430c33c5edd6b05dbe318df4f7d6d7712b1093407b11c4280700
  languageName: node
  linkType: hard

"punycode@npm:^1.2.4, punycode@npm:^1.4.1":
  version: 1.4.1
  resolution: "punycode@npm:1.4.1"
  checksum: 10c0/354b743320518aef36f77013be6e15da4db24c2b4f62c5f1eb0529a6ed02fbaf1cb52925785f6ab85a962f2b590d9cd5ad730b70da72b5f180e2556b8bd3ca08
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.1.1, punycode@npm:^2.3.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10c0/14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: "npm:^1.0.6"
  checksum: 10c0/62372cdeec24dc83a9fb240b7533c0fdcf0c5f7e0b83343edd7310f0ab4c8205a5e7c56406531f2e47e1b4878a3821d652be4192c841de5b032ca83619d8f860
  languageName: node
  linkType: hard

"qs@npm:^6.12.3":
  version: 6.14.0
  resolution: "qs@npm:6.14.0"
  dependencies:
    side-channel: "npm:^1.1.0"
  checksum: 10c0/8ea5d91bf34f440598ee389d4a7d95820e3b837d3fd9f433871f7924801becaa0cd3b3b4628d49a7784d06a8aea9bc4554d2b6d8d584e2d221dc06238a42909c
  languageName: node
  linkType: hard

"querystring-es3@npm:^0.2.0":
  version: 0.2.1
  resolution: "querystring-es3@npm:0.2.1"
  checksum: 10c0/476938c1adb45c141f024fccd2ffd919a3746e79ed444d00e670aad68532977b793889648980e7ca7ff5ffc7bfece623118d0fbadcaf217495eeb7059ae51580
  languageName: node
  linkType: hard

"querystringify@npm:^2.1.1":
  version: 2.2.0
  resolution: "querystringify@npm:2.2.0"
  checksum: 10c0/3258bc3dbdf322ff2663619afe5947c7926a6ef5fb78ad7d384602974c467fadfc8272af44f5eb8cddd0d011aae8fabf3a929a8eee4b86edcc0a21e6bd10f9aa
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"queue@npm:^6.0.1":
  version: 6.0.2
  resolution: "queue@npm:6.0.2"
  dependencies:
    inherits: "npm:~2.0.3"
  checksum: 10c0/cf987476cc72e7d3aaabe23ccefaab1cd757a2b5e0c8d80b67c9575a6b5e1198807ffd4f0948a3f118b149d1111d810ee773473530b77a5c606673cac2c9c996
  languageName: node
  linkType: hard

"raf@npm:^3.4.1":
  version: 3.4.1
  resolution: "raf@npm:3.4.1"
  dependencies:
    performance-now: "npm:^2.1.0"
  checksum: 10c0/337f0853c9e6a77647b0f499beedafea5d6facfb9f2d488a624f88b03df2be72b8a0e7f9118a3ff811377d534912039a3311815700d2b6d2313f82f736f9eb6e
  languageName: node
  linkType: hard

"randombytes@npm:^2.0.0, randombytes@npm:^2.0.1, randombytes@npm:^2.0.5, randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: "npm:^5.1.0"
  checksum: 10c0/50395efda7a8c94f5dffab564f9ff89736064d32addf0cc7e8bf5e4166f09f8ded7a0849ca6c2d2a59478f7d90f78f20d8048bca3cdf8be09d8e8a10790388f3
  languageName: node
  linkType: hard

"randomfill@npm:^1.0.4":
  version: 1.0.4
  resolution: "randomfill@npm:1.0.4"
  dependencies:
    randombytes: "npm:^2.0.5"
    safe-buffer: "npm:^5.1.0"
  checksum: 10c0/11aeed35515872e8f8a2edec306734e6b74c39c46653607f03c68385ab8030e2adcc4215f76b5e4598e028c4750d820afd5c65202527d831d2a5f207fe2bc87c
  languageName: node
  linkType: hard

"range-parser@npm:^1.0.3, range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10c0/96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10c0/b201c4b66049369a60e766318caff5cb3cc5a900efd89bdac431463822d976ad0670912c931fdbdcf5543207daf6f6833bca57aa116e1661d2ea91e12ca692c4
  languageName: node
  linkType: hard

"react-doc-viewer@npm:^0.1.14":
  version: 0.1.14
  resolution: "react-doc-viewer@npm:0.1.14"
  dependencies:
    pdfjs-dist: "npm:^4.5.136"
    react-pdf: "npm:9.0.0"
    styled-components: "npm:^5.3.11"
    wl-msg-reader: "npm:^0.2.0"
  checksum: 10c0/be19c148aa32b3b0878e397b7cbb04673c866194d5d5e0097aedf4360a2f3bc493e935435013fd4b584a9518863f435ed8341da6cf0db5a14aa9560781771367
  languageName: node
  linkType: hard

"react-dom@npm:^16.2.0":
  version: 16.14.0
  resolution: "react-dom@npm:16.14.0"
  dependencies:
    loose-envify: "npm:^1.1.0"
    object-assign: "npm:^4.1.1"
    prop-types: "npm:^15.6.2"
    scheduler: "npm:^0.19.1"
  peerDependencies:
    react: ^16.14.0
  checksum: 10c0/ca146e780631672a2d57c8d77775d38f394a6cd67db30c6af7964d0b3574ef7edccb1de8d592e990b98f4f5f8d1c8460b0691f04e7a45799962a51dcbaaa1371
  languageName: node
  linkType: hard

"react-dom@npm:^19.1.0":
  version: 19.1.1
  resolution: "react-dom@npm:19.1.1"
  dependencies:
    scheduler: "npm:^0.26.0"
  peerDependencies:
    react: ^19.1.1
  checksum: 10c0/8c91198510521299c56e4e8d5e3a4508b2734fb5e52f29eeac33811de64e76fe586ad32c32182e2e84e070d98df67125da346c3360013357228172dbcd20bcdd
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1, react-is@npm:^16.7.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10c0/33977da7a5f1a287936a0c85639fec6ca74f4f15ef1e59a6bc20338fc73dc69555381e211f7a3529b8150a1f71e4225525b41b60b52965bda53ce7d47377ada1
  languageName: node
  linkType: hard

"react-is@npm:^19.1.1":
  version: 19.1.1
  resolution: "react-is@npm:19.1.1"
  checksum: 10c0/3dba763fcd69835ae263dcd6727d7ffcc44c1d616f04b7329e67aefdc66a567af4f8dcecdd29454c7a707c968aa1eb85083a83fb616f01675ef25e71cf082f97
  languageName: node
  linkType: hard

"react-pdf-viewer@npm:^0.1.0":
  version: 0.1.0
  resolution: "react-pdf-viewer@npm:0.1.0"
  dependencies:
    pdfjs-dist: "npm:^2.0.197"
    react: "npm:^16.2.0"
    react-dom: "npm:^16.2.0"
    webpack: "npm:^3.10.0"
    webpack-dev-server: "npm:^2.9.7"
  checksum: 10c0/65021b76917a209ceba4626bc2cee90a4376fdfb652d01d8541002e87e7a2e895f84a0ea055f4f64cce0fffd6e7ae7d280e64270ea25721831f4c2d007cddfab
  languageName: node
  linkType: hard

"react-pdf@npm:9.0.0":
  version: 9.0.0
  resolution: "react-pdf@npm:9.0.0"
  dependencies:
    clsx: "npm:^2.0.0"
    dequal: "npm:^2.0.3"
    make-cancellable-promise: "npm:^1.3.1"
    make-event-props: "npm:^1.6.0"
    merge-refs: "npm:^1.3.0"
    pdfjs-dist: "npm:4.3.136"
    tiny-invariant: "npm:^1.0.0"
    warning: "npm:^4.0.0"
  peerDependencies:
    "@types/react": ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/c9e5606ee5a36acab0746914f2efa84459bf969e0f4d5e3a945da6d14f9882c57c45ea66f181ed16112b933321b963241e5c7f6b1cb0890322a24b4c8925a48b
  languageName: node
  linkType: hard

"react-pdf@npm:^10.0.1":
  version: 10.0.1
  resolution: "react-pdf@npm:10.0.1"
  dependencies:
    clsx: "npm:^2.0.0"
    dequal: "npm:^2.0.3"
    make-cancellable-promise: "npm:^2.0.0"
    make-event-props: "npm:^2.0.0"
    merge-refs: "npm:^2.0.0"
    pdfjs-dist: "npm:5.3.31"
    tiny-invariant: "npm:^1.0.0"
    warning: "npm:^4.0.0"
  peerDependencies:
    "@types/react": ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10c0/cefc45534f2f594d81cdddae833fc3afc89f245ff56dbe3aba19f9e4abf733e30a4a137a301dbae29ac1c10891a7c7eadec7d2a174eeabe0d8f0dc546b8d829f
  languageName: node
  linkType: hard

"react-property@npm:2.0.2":
  version: 2.0.2
  resolution: "react-property@npm:2.0.2"
  checksum: 10c0/27a3dfa68d29d45fc3582552715203291d26c6f1b228fdb6775e7ca19b10753141dbe98a0aa3a4da745b39fcd7427dc2d623055e63742062231ee18692a6f0fa
  languageName: node
  linkType: hard

"react-refresh@npm:^0.13.0":
  version: 0.13.0
  resolution: "react-refresh@npm:0.13.0"
  checksum: 10c0/cb9f324d471485e569628854dc08d1550c0798cde57f1bfb8d954e006659de1da0bdccaf7d5d2ac0d3d53df1aae7b740b2a36128789afb8aff0f7ec01b128587
  languageName: node
  linkType: hard

"react-refresh@npm:^0.17.0":
  version: 0.17.0
  resolution: "react-refresh@npm:0.17.0"
  checksum: 10c0/002cba940384c9930008c0bce26cac97a9d5682bc623112c2268ba0c155127d9c178a9a5cc2212d560088d60dfd503edd808669a25f9b377f316a32361d0b23c
  languageName: node
  linkType: hard

"react-transition-group@npm:^4.4.5":
  version: 4.4.5
  resolution: "react-transition-group@npm:4.4.5"
  dependencies:
    "@babel/runtime": "npm:^7.5.5"
    dom-helpers: "npm:^5.0.1"
    loose-envify: "npm:^1.4.0"
    prop-types: "npm:^15.6.2"
  peerDependencies:
    react: ">=16.6.0"
    react-dom: ">=16.6.0"
  checksum: 10c0/2ba754ba748faefa15f87c96dfa700d5525054a0141de8c75763aae6734af0740e77e11261a1e8f4ffc08fd9ab78510122e05c21c2d79066c38bb6861a886c82
  languageName: node
  linkType: hard

"react@npm:^16.2.0":
  version: 16.14.0
  resolution: "react@npm:16.14.0"
  dependencies:
    loose-envify: "npm:^1.1.0"
    object-assign: "npm:^4.1.1"
    prop-types: "npm:^15.6.2"
  checksum: 10c0/df8faae43e01387013900e8f8fb3c4ce9935b7edbcbaa77e12999c913eb958000a0a8750bf9a0886dae0ad768dd4a4ee983752d5bade8d840adbe0ce890a2438
  languageName: node
  linkType: hard

"react@npm:^19.1.0":
  version: 19.1.1
  resolution: "react@npm:19.1.1"
  checksum: 10c0/8c9769a2dfd02e603af6445058325e6c8a24b47b185d0e461f66a6454765ddcaecb3f0a90184836c68bb509f3c38248359edbc42f0d07c23eb500a5c30c87b4e
  languageName: node
  linkType: hard

"read-pkg-up@npm:^1.0.1":
  version: 1.0.1
  resolution: "read-pkg-up@npm:1.0.1"
  dependencies:
    find-up: "npm:^1.0.0"
    read-pkg: "npm:^1.0.0"
  checksum: 10c0/36c4fc8bd73edf77a4eeb497b6e43010819ea4aef64cbf8e393439fac303398751c5a299feab84e179a74507e3a1416e1ed033a888b1dac3463bf46d1765f7ac
  languageName: node
  linkType: hard

"read-pkg-up@npm:^2.0.0":
  version: 2.0.0
  resolution: "read-pkg-up@npm:2.0.0"
  dependencies:
    find-up: "npm:^2.0.0"
    read-pkg: "npm:^2.0.0"
  checksum: 10c0/6fbb9f8c1a9ed3c8a5764387a77ac4456082f1fe98757d1ed300d8b0a4c70501f28cbb053ae7b3e0de6094930fb7258fbfe099957a53c999337aaf8bc53ff37f
  languageName: node
  linkType: hard

"read-pkg@npm:^1.0.0":
  version: 1.1.0
  resolution: "read-pkg@npm:1.1.0"
  dependencies:
    load-json-file: "npm:^1.0.0"
    normalize-package-data: "npm:^2.3.2"
    path-type: "npm:^1.0.0"
  checksum: 10c0/51fce9f7066787dc7688ea7014324cedeb9f38daa7dace4f1147d526f22354a07189ef728710bc97e27fcf5ed3a03b68ad8b60afb4251984640b6f09c180d572
  languageName: node
  linkType: hard

"read-pkg@npm:^2.0.0":
  version: 2.0.0
  resolution: "read-pkg@npm:2.0.0"
  dependencies:
    load-json-file: "npm:^2.0.0"
    normalize-package-data: "npm:^2.3.2"
    path-type: "npm:^2.0.0"
  checksum: 10c0/c0d1b66c58e58fadaabe48c83c525fb1966304555e5089fab5ccce2c3dfe0fad726720b170a5fa23ff20452e22d1dbe817f5c63f03546bb85cbfb6b84da84c2b
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.1, readable-stream@npm:^2.0.2, readable-stream@npm:^2.3.3, readable-stream@npm:^2.3.6, readable-stream@npm:^2.3.8, readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 10c0/7efdb01f3853bc35ac62ea25493567bf588773213f5f4a79f9c365e1ad13bab845ac0dae7bc946270dc40c3929483228415e92a3fc600cc7e4548992f41ee3fa
  languageName: node
  linkType: hard

"readable-stream@npm:^3.0.6, readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10c0/e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"readdirp@npm:^2.2.1":
  version: 2.2.1
  resolution: "readdirp@npm:2.2.1"
  dependencies:
    graceful-fs: "npm:^4.1.11"
    micromatch: "npm:^3.1.10"
    readable-stream: "npm:^2.0.2"
  checksum: 10c0/770d177372ff2212d382d425d55ca48301fcbf3231ab3827257bbcca7ff44fb51fe4af6acc2dda8512dc7f29da390e9fbea5b2b3fc724b86e85cc828395b7797
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10c0/6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"recast@npm:^0.23.11":
  version: 0.23.11
  resolution: "recast@npm:0.23.11"
  dependencies:
    ast-types: "npm:^0.16.1"
    esprima: "npm:~4.0.0"
    source-map: "npm:~0.6.1"
    tiny-invariant: "npm:^1.3.3"
    tslib: "npm:^2.0.1"
  checksum: 10c0/45b520a8f0868a5a24ecde495be9de3c48e69a54295d82a7331106554b75cfba75d16c909959d056e9ceed47a1be5e061e2db8b9ecbcd6ba44c2f3ef9a47bd18
  languageName: node
  linkType: hard

"redent@npm:^1.0.0":
  version: 1.0.0
  resolution: "redent@npm:1.0.0"
  dependencies:
    indent-string: "npm:^2.1.0"
    strip-indent: "npm:^1.0.1"
  checksum: 10c0/9fa48d250d4e645acac9de57cb82dc29cd7f5f27257ec367461e3dd0c9f14c55f1c40fd3d9cf7f9a3ed337f209ad4e0370abfcf5cf75569ebd31c97a7949b8a2
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.1"
    which-builtin-type: "npm:^1.2.1"
  checksum: 10c0/7facec28c8008876f8ab98e80b7b9cb4b1e9224353fd4756dda5f2a4ab0d30fa0a5074777c6df24e1e0af463a2697513b0a11e548d99cf52f21f7bc6ba48d3ac
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.7":
  version: 0.13.11
  resolution: "regenerator-runtime@npm:0.13.11"
  checksum: 10c0/12b069dc774001fbb0014f6a28f11c09ebfe3c0d984d88c9bced77fdb6fedbacbca434d24da9ae9371bfbf23f754869307fb51a4c98a8b8b18e5ef748677ca24
  languageName: node
  linkType: hard

"regex-not@npm:^1.0.0, regex-not@npm:^1.0.2":
  version: 1.0.2
  resolution: "regex-not@npm:1.0.2"
  dependencies:
    extend-shallow: "npm:^3.0.2"
    safe-regex: "npm:^1.1.0"
  checksum: 10c0/a0f8d6045f63b22e9759db10e248369c443b41cedd7dba0922d002b66c2734bc2aef0d98c4d45772d1f756245f4c5203856b88b9624bba2a58708858a8d485d6
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.1, regexp.prototype.flags@npm:^1.5.3, regexp.prototype.flags@npm:^1.5.4":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10c0/83b88e6115b4af1c537f8dabf5c3744032cb875d63bc05c288b1b8c0ef37cbe55353f95d8ca817e8843806e3e150b118bc624e4279b24b4776b4198232735a77
  languageName: node
  linkType: hard

"remove-trailing-separator@npm:^1.0.1":
  version: 1.1.0
  resolution: "remove-trailing-separator@npm:1.1.0"
  checksum: 10c0/3568f9f8f5af3737b4aee9e6e1e8ec4be65a92da9cb27f989e0893714d50aa95ed2ff02d40d1fa35e1b1a234dc9c2437050ef356704a3999feaca6667d9e9bfc
  languageName: node
  linkType: hard

"repeat-element@npm:^1.1.2":
  version: 1.1.4
  resolution: "repeat-element@npm:1.1.4"
  checksum: 10c0/81aa8d82bc845780803ef52df3533fa399974b99df571d0bb86e91f0ffca9ee4b9c4e8e5e72af087938cc28d2aef93d106a6d01da685d72ce96455b90a9f9f69
  languageName: node
  linkType: hard

"repeat-string@npm:^1.5.2, repeat-string@npm:^1.6.1":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 10c0/87fa21bfdb2fbdedc44b9a5b118b7c1239bdd2c2c1e42742ef9119b7d412a5137a1d23f1a83dc6bb686f4f27429ac6f542e3d923090b44181bafa41e8ac0174d
  languageName: node
  linkType: hard

"repeating@npm:^2.0.0":
  version: 2.0.1
  resolution: "repeating@npm:2.0.1"
  dependencies:
    is-finite: "npm:^1.0.0"
  checksum: 10c0/7f5cd293ec47d9c074ef0852800d5ff5c49028ce65242a7528d84f32bd2fe200b142930562af58c96d869c5a3046e87253030058e45231acaa129c1a7087d2e7
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10c0/83aa76a7bc1531f68d92c75a2ca2f54f1b01463cb566cf3fbc787d0de8be30c9dbc211d1d46be3497dac5785fe296f2dd11d531945ac29730643357978966e99
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: 10c0/aaa267e0c5b022fc5fd4eef49d8285086b15f2a1c54b28240fdf03599cbd9c26049fee3eab894f2e1f6ca65e513b030a7c264201e3f005601e80c49fb2937ce2
  languageName: node
  linkType: hard

"require-main-filename@npm:^1.0.1":
  version: 1.0.1
  resolution: "require-main-filename@npm:1.0.1"
  checksum: 10c0/1ab87efb72a0e223a667154e92f29ca753fd42eb87f22db142b91c86d134e29ecf18af929111ccd255fd340b57d84a9d39489498d8dfd5136b300ded30a5f0b6
  languageName: node
  linkType: hard

"requires-port@npm:^1.0.0":
  version: 1.0.0
  resolution: "requires-port@npm:1.0.0"
  checksum: 10c0/b2bfdd09db16c082c4326e573a82c0771daaf7b53b9ce8ad60ea46aa6e30aaf475fe9b164800b89f93b748d2c234d8abff945d2551ba47bf5698e04cd7713267
  languageName: node
  linkType: hard

"reselect@npm:^5.1.1":
  version: 5.1.1
  resolution: "reselect@npm:5.1.1"
  checksum: 10c0/219c30da122980f61853db3aebd173524a2accd4b3baec770e3d51941426c87648a125ca08d8c57daa6b8b086f2fdd2703cb035dd6231db98cdbe1176a71f489
  languageName: node
  linkType: hard

"resolve-cwd@npm:^2.0.0":
  version: 2.0.0
  resolution: "resolve-cwd@npm:2.0.0"
  dependencies:
    resolve-from: "npm:^3.0.0"
  checksum: 10c0/10c3a7ffeb55af51206f5ca8696ed833376179399336ce8e9df8f76c044c13bccd0e9a3148708daf272193179a581ddb076e203eaa71efa0ad341b243174ca12
  languageName: node
  linkType: hard

"resolve-from@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-from@npm:3.0.0"
  checksum: 10c0/24affcf8e81f4c62f0dcabc774afe0e19c1f38e34e43daac0ddb409d79435fc3037f612b0cc129178b8c220442c3babd673e88e870d27215c99454566e770ebc
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10c0/8408eec31a3112ef96e3746c37be7d64020cda07c03a920f5024e77290a218ea758b26ca9529fd7b1ad283947f34b2291c1c0f6aa0ed34acfdda9c6014c8d190
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 10c0/fb8f7bbe2ca281a73b7ef423a1cbc786fb244bd7a95cbe5c3fba25b27d327150beca8ba02f622baea65919a57e061eb5005204daa5f93ed590d9b77463a567ab
  languageName: node
  linkType: hard

"resolve-url@npm:^0.2.1":
  version: 0.2.1
  resolution: "resolve-url@npm:0.2.1"
  checksum: 10c0/c285182cfcddea13a12af92129ce0569be27fb0074ffaefbd3ba3da2eac2acecdfc996d435c4982a9fa2b4708640e52837c9153a5ab9255886a00b0b9e8d2a54
  languageName: node
  linkType: hard

"resolve@npm:^1.10.0, resolve@npm:^1.19.0":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/8967e1f4e2cc40f79b7e080b4582b9a8c5ee36ffb46041dccb20e6461161adf69f843b43067b4a375de926a2cd669157e29a29578191def399dd5ef89a1b5203
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.5":
  version: 2.0.0-next.5
  resolution: "resolve@npm:2.0.0-next.5"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/a6c33555e3482ea2ec4c6e3d3bf0d78128abf69dca99ae468e64f1e30acaa318fd267fb66c8836b04d558d3e2d6ed875fe388067e7d8e0de647d3c21af21c43a
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.10.0#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.19.0#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/52a4e505bbfc7925ac8f4cd91fd8c4e096b6a89728b9f46861d3b405ac9a1ccf4dcbf8befb4e89a2e11370dacd0160918163885cbc669369590f2f31f4c58939
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^2.0.0-next.5#optional!builtin<compat/resolve>":
  version: 2.0.0-next.5
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.5#optional!builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/78ad6edb8309a2bfb720c2c1898f7907a37f858866ce11a5974643af1203a6a6e05b2fa9c53d8064a673a447b83d42569260c306d43628bff5bb101969708355
  languageName: node
  linkType: hard

"restructure@npm:^3.0.0":
  version: 3.0.2
  resolution: "restructure@npm:3.0.2"
  checksum: 10c0/f13536c094ba40a9af704e6a9fc030afd48d6112e9a3bec5f9cf5bad50416a22a7cf9aaece542bbac8c82204ad4901bf455e6204613abedbc075bc221ea6bdef
  languageName: node
  linkType: hard

"ret@npm:~0.1.10":
  version: 0.1.15
  resolution: "ret@npm:0.1.15"
  checksum: 10c0/01f77cad0f7ea4f955852c03d66982609893edc1240c0c964b4c9251d0f9fb6705150634060d169939b096d3b77f4c84d6b6098a5b5d340160898c8581f1f63f
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10c0/4eff0d4a5f9383566c7d7ec437b671cc51b25963bd61bf127c3f3d3f68e44a026d99b8d2f1ad344afff8d278a8fe70a8ea092650a716d22287e8bef7126bb2fa
  languageName: node
  linkType: hard

"rgbcolor@npm:^1.0.1":
  version: 1.0.1
  resolution: "rgbcolor@npm:1.0.1"
  checksum: 10c0/13af06c523351bac2854b85a22d1dfafd9310efd898e9bd96c8706f9aa09a3ddc8392ab00ae03d12950782164a97677f21834ffd84ffebf76ae106add319f956
  languageName: node
  linkType: hard

"right-align@npm:^0.1.1":
  version: 0.1.3
  resolution: "right-align@npm:0.1.3"
  dependencies:
    align-text: "npm:^0.1.1"
  checksum: 10c0/8fdafcb1e4cadd03d392f2a2185ab39265deb80bbe37c6ee4b0a552937c84a10fae5afd7ab4623734f7c5356b1d748daf4130529a2fbc8caa311b6257473ec95
  languageName: node
  linkType: hard

"rimraf@npm:^2.2.8":
  version: 2.7.1
  resolution: "rimraf@npm:2.7.1"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: ./bin.js
  checksum: 10c0/4eef73d406c6940927479a3a9dee551e14a54faf54b31ef861250ac815172bade86cc6f7d64a4dc5e98b65e4b18a2e1c9ff3b68d296be0c748413f092bb0dd40
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 10c0/9cb7757acb489bd83757ba1a274ab545eafd75598a9d817e0c3f8b164238dd90eba50d6b848bd4dcc5f3040912e882dc7ba71653e35af660d77b25c381d402e8
  languageName: node
  linkType: hard

"ripemd160@npm:=2.0.1":
  version: 2.0.1
  resolution: "ripemd160@npm:2.0.1"
  dependencies:
    hash-base: "npm:^2.0.0"
    inherits: "npm:^2.0.1"
  checksum: 10c0/d4cbb4713c1268bb35e44815b12e3744a952a72b72e6a72110c8f3932227ddf68841110285fe2ed1c04805e2621d85f905deb5f55f9d91fa1bfc0f8081a244e6
  languageName: node
  linkType: hard

"ripemd160@npm:^2.0.0, ripemd160@npm:^2.0.1":
  version: 2.0.2
  resolution: "ripemd160@npm:2.0.2"
  dependencies:
    hash-base: "npm:^3.0.0"
    inherits: "npm:^2.0.1"
  checksum: 10c0/f6f0df78817e78287c766687aed4d5accbebc308a8e7e673fb085b9977473c1f139f0c5335d353f172a915bb288098430755d2ad3c4f30612f4dd0c901cd2c3a
  languageName: node
  linkType: hard

"rollup@npm:2.79.2":
  version: 2.79.2
  resolution: "rollup@npm:2.79.2"
  dependencies:
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10c0/bc3746c988d903c2211266ddc539379d53d92689b9cc5c2b4e3ae161689de9af491957a567c629b6cc81f48d0928a7591fc4c383fba68a48d2966c9fb8a2bce9
  languageName: node
  linkType: hard

"rollup@npm:^4.40.0":
  version: 4.46.2
  resolution: "rollup@npm:4.46.2"
  dependencies:
    "@rollup/rollup-android-arm-eabi": "npm:4.46.2"
    "@rollup/rollup-android-arm64": "npm:4.46.2"
    "@rollup/rollup-darwin-arm64": "npm:4.46.2"
    "@rollup/rollup-darwin-x64": "npm:4.46.2"
    "@rollup/rollup-freebsd-arm64": "npm:4.46.2"
    "@rollup/rollup-freebsd-x64": "npm:4.46.2"
    "@rollup/rollup-linux-arm-gnueabihf": "npm:4.46.2"
    "@rollup/rollup-linux-arm-musleabihf": "npm:4.46.2"
    "@rollup/rollup-linux-arm64-gnu": "npm:4.46.2"
    "@rollup/rollup-linux-arm64-musl": "npm:4.46.2"
    "@rollup/rollup-linux-loongarch64-gnu": "npm:4.46.2"
    "@rollup/rollup-linux-ppc64-gnu": "npm:4.46.2"
    "@rollup/rollup-linux-riscv64-gnu": "npm:4.46.2"
    "@rollup/rollup-linux-riscv64-musl": "npm:4.46.2"
    "@rollup/rollup-linux-s390x-gnu": "npm:4.46.2"
    "@rollup/rollup-linux-x64-gnu": "npm:4.46.2"
    "@rollup/rollup-linux-x64-musl": "npm:4.46.2"
    "@rollup/rollup-win32-arm64-msvc": "npm:4.46.2"
    "@rollup/rollup-win32-ia32-msvc": "npm:4.46.2"
    "@rollup/rollup-win32-x64-msvc": "npm:4.46.2"
    "@types/estree": "npm:1.0.8"
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-freebsd-arm64":
      optional: true
    "@rollup/rollup-freebsd-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm-musleabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-loongarch64-gnu":
      optional: true
    "@rollup/rollup-linux-ppc64-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-musl":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10c0/f428497fe119fe7c4e34f1020d45ba13e99b94c9aa36958d88823d932b155c9df3d84f53166f3ee913ff68ea6c7599a9ab34861d88562ad9d8420f64ca5dad4c
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"rxjs@npm:7.5.7":
  version: 7.5.7
  resolution: "rxjs@npm:7.5.7"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: 10c0/283620b3c90b85467c3549f7cda0dd768bc18719cccbbdd9aacadb0f0946827ab20d036f1a00d78066d769764e73070bfee8706091d77b8d971975598f6cbbd4
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    has-symbols: "npm:^1.1.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/43c86ffdddc461fb17ff8a17c5324f392f4868f3c7dd2c6a5d9f5971713bc5fd755667212c80eab9567595f9a7509cc2f83e590ddaebd1bd19b780f9c79f9a8d
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:>=5.1.0, safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:^5.1.1, safe-buffer@npm:^5.1.2, safe-buffer@npm:^5.2.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10c0/780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    isarray: "npm:^2.0.5"
  checksum: 10c0/831f1c9aae7436429e7862c7e46f847dfe490afac20d0ee61bae06108dbf5c745a0de3568ada30ccdd3eeb0864ca8331b2eef703abd69bfea0745b21fd320750
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-regex: "npm:^1.2.1"
  checksum: 10c0/f2c25281bbe5d39cddbbce7f86fca5ea9b3ce3354ea6cd7c81c31b006a5a9fff4286acc5450a3b9122c56c33eba69c56b9131ad751457b2b4a585825e6a10665
  languageName: node
  linkType: hard

"safe-regex@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex@npm:1.1.0"
  dependencies:
    ret: "npm:~0.1.10"
  checksum: 10c0/547d58aa5184cbef368fd5ed5f28d20f911614748c5da6b35f53fd6626396707587251e6e3d1e3010fd3ff1212e413841b8825eaa5f317017ca62a30899af31a
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"sax@npm:1.3.0":
  version: 1.3.0
  resolution: "sax@npm:1.3.0"
  checksum: 10c0/599dbe0ba9d8bd55e92d920239b21d101823a6cedff71e542589303fa0fa8f3ece6cf608baca0c51be846a2e88365fac94a9101a9c341d94b98e30c4deea5bea
  languageName: node
  linkType: hard

"saxes@npm:^6.0.0":
  version: 6.0.0
  resolution: "saxes@npm:6.0.0"
  dependencies:
    xmlchars: "npm:^2.2.0"
  checksum: 10c0/3847b839f060ef3476eb8623d099aa502ad658f5c40fd60c105ebce86d244389b0d76fcae30f4d0c728d7705ceb2f7e9b34bb54717b6a7dbedaf5dad2d9a4b74
  languageName: node
  linkType: hard

"scheduler@npm:0.25.0-rc-603e6108-20241029":
  version: 0.25.0-rc-603e6108-20241029
  resolution: "scheduler@npm:0.25.0-rc-603e6108-20241029"
  checksum: 10c0/aa95c1ea0cba0c6c7031cfbbd131fc2de2b8a7e7e167c56c3072fbff1d500ae1c2bb92d5a54057b9bfef810be2b718df3020a19fca6bd6d3fbcd74c961e65ebf
  languageName: node
  linkType: hard

"scheduler@npm:^0.19.1":
  version: 0.19.1
  resolution: "scheduler@npm:0.19.1"
  dependencies:
    loose-envify: "npm:^1.1.0"
    object-assign: "npm:^4.1.1"
  checksum: 10c0/9658932a73148a93d791c064b331d9690ddfecc4de25bcd6c9b89f5f1166e3d23d9c31c1595d66565e5ffbb34d47035cb14841aba6444bc266bfcd215cefe9c0
  languageName: node
  linkType: hard

"scheduler@npm:^0.26.0":
  version: 0.26.0
  resolution: "scheduler@npm:0.26.0"
  checksum: 10c0/5b8d5bfddaae3513410eda54f2268e98a376a429931921a81b5c3a2873aab7ca4d775a8caac5498f8cbc7d0daeab947cf923dbd8e215d61671f9f4e392d34356
  languageName: node
  linkType: hard

"select-hose@npm:^2.0.0":
  version: 2.0.0
  resolution: "select-hose@npm:2.0.0"
  checksum: 10c0/01cc52edd29feddaf379efb4328aededa633f0ac43c64b11a8abd075ff34f05b0d280882c4fbcbdf1a0658202c9cd2ea8d5985174dcf9a2dac7e3a4996fa9b67
  languageName: node
  linkType: hard

"selfsigned@npm:^1.9.1":
  version: 1.10.14
  resolution: "selfsigned@npm:1.10.14"
  dependencies:
    node-forge: "npm:^0.10.0"
  checksum: 10c0/cb7c92e28a3e8a34e91a3b20b9dd7d237a1bffccd9890c434f74eef65ae8bfffe7c1ab3379faac017b957ea44522923e06e2b41e0858c643edb01137a507cd16
  languageName: node
  linkType: hard

"semver@npm:2 || 3 || 4 || 5":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: 10c0/e4cf10f86f168db772ae95d86ba65b3fd6c5967c94d97c708ccb463b778c2ee53b914cd7167620950fc07faf5a564e6efe903836639e512a1aa15fbc9667fa25
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.6.0":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10c0/aca305edfbf2383c22571cb7714f48cadc7ac95371b4b52362fb8eeffdfbc0de0669368b82b2b15978f8848f01d7114da65697e56cd8c37b0dab8c58e543f9ea
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/ea3f8a67a8f0be3d6bf9080f0baed6d2c51d11d4f7b4470de96a5029c598a7011c497511ccc28968b70ef05508675cebff27da9151dd2ceadd60be4e6cf845e3
  languageName: node
  linkType: hard

"seroval-plugins@npm:^1.3.2":
  version: 1.3.2
  resolution: "seroval-plugins@npm:1.3.2"
  peerDependencies:
    seroval: ^1.0
  checksum: 10c0/67b108b3cbc189acca445b512ebd77e11b55c6aa3d1610c3a0b4822b63e5c6d0a4426ac6e50574772cc743257f0a16a8a4d12e5e4f28a2da8e1f583b00a27bbe
  languageName: node
  linkType: hard

"seroval@npm:^1.3.2":
  version: 1.3.2
  resolution: "seroval@npm:1.3.2"
  checksum: 10c0/19e74825643786d22e5c58054bd28065238de0156545afba82f9a7d3ee70ea4f0249b427f317bc6bf983849dde8e4190264728d90c84620aa163bfbc5971f1bc
  languageName: node
  linkType: hard

"serve-index@npm:^1.9.1":
  version: 1.9.1
  resolution: "serve-index@npm:1.9.1"
  dependencies:
    accepts: "npm:~1.3.4"
    batch: "npm:0.6.1"
    debug: "npm:2.6.9"
    escape-html: "npm:~1.0.3"
    http-errors: "npm:~1.6.2"
    mime-types: "npm:~2.1.17"
    parseurl: "npm:~1.3.2"
  checksum: 10c0/a666471a24196f74371edf2c3c7bcdd82adbac52f600804508754b5296c3567588bf694258b19e0cb23a567acfa20d9721bfdaed3286007b81f9741ada8a3a9c
  languageName: node
  linkType: hard

"serve-static@npm:1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 10c0/528fff6f5e12d0c5a391229ad893910709bc51b5705962b09404a1d813857578149b8815f35d3ee5752f44cd378d0f31669d4b1d7e2d11f41e08283d5134bd1f
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 10c0/9f8c1b2d800800d0b589de1477c753492de5c1548d4ade52f57f1d1f5e04af5481554d75ce5e5c43d4004b80a3eb714398d6907027dc0534177b7539119f4454
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/82850e62f412a258b71e123d4ed3873fa9377c216809551192bb6769329340176f109c2eeae8c22a8d386c76739855f78e8716515c818bcaef384b51110f0f3c
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/fce59f90696c450a8523e754abb305e2b8c73586452619c2bad5f7bf38c7b6b4651895c9db895679c5bef9554339cf3ef1c329b66ece3eda7255785fbe299316
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/ca5c3ccbba479d07c30460e367e66337cec825560b11e8ba9c5ebe13a2a0d6021ae34eddf94ff3dfe17a3104dc1f191519cb6c48378b503e5c3f36393938776a
  languageName: node
  linkType: hard

"set-value@npm:^2.0.0, set-value@npm:^2.0.1":
  version: 2.0.1
  resolution: "set-value@npm:2.0.1"
  dependencies:
    extend-shallow: "npm:^2.0.1"
    is-extendable: "npm:^0.1.1"
    is-plain-object: "npm:^2.0.3"
    split-string: "npm:^3.0.1"
  checksum: 10c0/4c40573c4f6540456e4b38b95f570272c4cfbe1d12890ad4057886da8535047cd772dfadf5b58e2e87aa244dfb4c57e3586f6716b976fc47c5144b6b09e1811b
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.4, setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 10c0/5bae81bfdbfbd0ce992893286d49c9693c82b1bcc00dcaaf3a09c8f428fdeacf4190c013598b81875dfac2b08a572422db7df779a99332d0fce186d15a3e4d49
  languageName: node
  linkType: hard

"setprototypeof@npm:1.1.0":
  version: 1.1.0
  resolution: "setprototypeof@npm:1.1.0"
  checksum: 10c0/a77b20876689c6a89c3b42f0c3596a9cae02f90fc902570cbd97198e9e8240382086c9303ad043e88cee10f61eae19f1004e51d885395a1e9bf49f9ebed12872
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10c0/68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"sha.js@npm:^2.4.0, sha.js@npm:^2.4.11, sha.js@npm:^2.4.8":
  version: 2.4.12
  resolution: "sha.js@npm:2.4.12"
  dependencies:
    inherits: "npm:^2.0.4"
    safe-buffer: "npm:^5.2.1"
    to-buffer: "npm:^1.2.0"
  bin:
    sha.js: bin.js
  checksum: 10c0/9d36bdd76202c8116abbe152a00055ccd8a0099cb28fc17c01fa7bb2c8cffb9ca60e2ab0fe5f274ed6c45dc2633d8c39cf7ab050306c231904512ba9da4d8ab1
  languageName: node
  linkType: hard

"shallowequal@npm:^1.1.0":
  version: 1.1.0
  resolution: "shallowequal@npm:1.1.0"
  checksum: 10c0/b926efb51cd0f47aa9bc061add788a4a650550bbe50647962113a4579b60af2abe7b62f9b02314acc6f97151d4cf87033a2b15fc20852fae306d1a095215396c
  languageName: node
  linkType: hard

"shebang-command@npm:^1.2.0":
  version: 1.2.0
  resolution: "shebang-command@npm:1.2.0"
  dependencies:
    shebang-regex: "npm:^1.0.0"
  checksum: 10c0/7b20dbf04112c456b7fc258622dafd566553184ac9b6938dd30b943b065b21dabd3776460df534cc02480db5e1b6aec44700d985153a3da46e7db7f9bd21326d
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "shebang-regex@npm:1.0.0"
  checksum: 10c0/9abc45dee35f554ae9453098a13fdc2f1730e525a5eb33c51f096cc31f6f10a4b38074c1ebf354ae7bffa7229506083844008dfc3bb7818228568c0b2dc1fff2
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10c0/71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6, side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10c0/cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.0":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10c0/25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"simple-concat@npm:^1.0.0":
  version: 1.0.1
  resolution: "simple-concat@npm:1.0.1"
  checksum: 10c0/62f7508e674414008910b5397c1811941d457dfa0db4fd5aa7fa0409eb02c3609608dfcd7508cace75b3a0bf67a2a77990711e32cd213d2c76f4fd12ee86d776
  languageName: node
  linkType: hard

"simple-get@npm:^3.0.3":
  version: 3.1.1
  resolution: "simple-get@npm:3.1.1"
  dependencies:
    decompress-response: "npm:^4.2.0"
    once: "npm:^1.3.1"
    simple-concat: "npm:^1.0.0"
  checksum: 10c0/438c78844ea1b1e7268d13ee0b3a39c7d644183367aec916aed3b676b45d3037a61d9f975c200a49b42eb851f29f03745118af1e13c01e60a7b4044f2fd60be7
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: "npm:^0.3.1"
  checksum: 10c0/df5e4662a8c750bdba69af4e8263c5d96fe4cd0f9fe4bdfa3cbdeb45d2e869dff640beaaeb1ef0e99db4d8d2ec92f85508c269f50c972174851bc1ae5bd64308
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"snapdragon-node@npm:^2.0.1":
  version: 2.1.1
  resolution: "snapdragon-node@npm:2.1.1"
  dependencies:
    define-property: "npm:^1.0.0"
    isobject: "npm:^3.0.0"
    snapdragon-util: "npm:^3.0.1"
  checksum: 10c0/7616e6a1ca054afe3ad8defda17ebe4c73b0800d2e0efd635c44ee1b286f8ac7900517314b5330862ce99b28cd2782348ee78bae573ff0f55832ad81d9657f3f
  languageName: node
  linkType: hard

"snapdragon-util@npm:^3.0.1":
  version: 3.0.1
  resolution: "snapdragon-util@npm:3.0.1"
  dependencies:
    kind-of: "npm:^3.2.0"
  checksum: 10c0/4441856d343399ba7f37f79681949d51b922e290fcc07e7bc94655a50f584befa4fb08f40c3471cd160e004660161964d8ff140cba49baa59aa6caba774240e3
  languageName: node
  linkType: hard

"snapdragon@npm:^0.8.1":
  version: 0.8.2
  resolution: "snapdragon@npm:0.8.2"
  dependencies:
    base: "npm:^0.11.1"
    debug: "npm:^2.2.0"
    define-property: "npm:^0.2.5"
    extend-shallow: "npm:^2.0.1"
    map-cache: "npm:^0.2.2"
    source-map: "npm:^0.5.6"
    source-map-resolve: "npm:^0.5.0"
    use: "npm:^3.1.0"
  checksum: 10c0/dfdac1f73d47152d72fc07f4322da09bbddfa31c1c9c3ae7346f252f778c45afa5b03e90813332f02f04f6de8003b34a168c456f8bb719024d092f932520ffca
  languageName: node
  linkType: hard

"socket.io-client@npm:^4.8.1":
  version: 4.8.1
  resolution: "socket.io-client@npm:4.8.1"
  dependencies:
    "@socket.io/component-emitter": "npm:~3.1.0"
    debug: "npm:~4.3.2"
    engine.io-client: "npm:~6.6.1"
    socket.io-parser: "npm:~4.2.4"
  checksum: 10c0/544c49cc8cc77118ef68b758a8a580c8e680a5909cae05c566d2cc07ec6cd6720a4f5b7e985489bf2a8391749177a5437ac30b8afbdf30b9da6402687ad51c86
  languageName: node
  linkType: hard

"socket.io-parser@npm:~4.2.4":
  version: 4.2.4
  resolution: "socket.io-parser@npm:4.2.4"
  dependencies:
    "@socket.io/component-emitter": "npm:~3.1.0"
    debug: "npm:~4.3.1"
  checksum: 10c0/9383b30358fde4a801ea4ec5e6860915c0389a091321f1c1f41506618b5cf7cd685d0a31c587467a0c4ee99ef98c2b99fb87911f9dfb329716c43b587f29ca48
  languageName: node
  linkType: hard

"sockjs-client@npm:1.1.5":
  version: 1.1.5
  resolution: "sockjs-client@npm:1.1.5"
  dependencies:
    debug: "npm:^2.6.6"
    eventsource: "npm:0.1.6"
    faye-websocket: "npm:~0.11.0"
    inherits: "npm:^2.0.1"
    json3: "npm:^3.3.2"
    url-parse: "npm:^1.1.8"
  checksum: 10c0/2a39349146fc6e015447751a28e516edfafe6bc9d9b2e88f4ec8c99174e28c8497411fe585fe8931128b16b05d2ae1951bc513ccdd0c7d73b4ce2135cb52c210
  languageName: node
  linkType: hard

"sockjs@npm:0.3.19":
  version: 0.3.19
  resolution: "sockjs@npm:0.3.19"
  dependencies:
    faye-websocket: "npm:^0.10.0"
    uuid: "npm:^3.0.1"
  checksum: 10c0/ad534b2abf52c8efcba9bc30691992dfbb23eaec692787bd7261b5b9def67757d2037ba2de642c36cd74e94e92c0b7cc653da23874488e97e0ddd1cca3bfcf7f
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.6
  resolution: "socks@npm:2.8.6"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/15b95db4caa359c80bfa880ff3e58f3191b9ffa4313570e501a60ee7575f51e4be664a296f4ee5c2c40544da179db6140be53433ce41ec745f9d51f342557514
  languageName: node
  linkType: hard

"source-list-map@npm:^2.0.0":
  version: 2.0.1
  resolution: "source-list-map@npm:2.0.1"
  checksum: 10c0/2e5e421b185dcd857f46c3c70e2e711a65d717b78c5f795e2e248c9d67757882ea989b80ebc08cf164eeeda5f4be8aa95d3b990225070b2daaaf3257c5958149
  languageName: node
  linkType: hard

"source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10c0/7bda1fc4c197e3c6ff17de1b8b2c20e60af81b63a52cb32ec5a5d67a20a7d42651e2cb34ebe93833c5a2a084377e17455854fee3e21e7925c64a51b6a52b0faf
  languageName: node
  linkType: hard

"source-map-resolve@npm:^0.5.0":
  version: 0.5.3
  resolution: "source-map-resolve@npm:0.5.3"
  dependencies:
    atob: "npm:^2.1.2"
    decode-uri-component: "npm:^0.2.0"
    resolve-url: "npm:^0.2.1"
    source-map-url: "npm:^0.4.0"
    urix: "npm:^0.1.0"
  checksum: 10c0/410acbe93882e058858d4c1297be61da3e1533f95f25b95903edddc1fb719654e705663644677542d1fb78a66390238fad1a57115fc958a0724cf9bb509caf57
  languageName: node
  linkType: hard

"source-map-url@npm:^0.4.0":
  version: 0.4.1
  resolution: "source-map-url@npm:0.4.1"
  checksum: 10c0/f8af0678500d536c7f643e32094d6718a4070ab4ca2d2326532512cfbe2d5d25a45849b4b385879326f2d7523bb3b686d0360dd347a3cda09fd89a5c28d4bc58
  languageName: node
  linkType: hard

"source-map@npm:^0.5.3, source-map@npm:^0.5.6, source-map@npm:^0.5.7, source-map@npm:~0.5.1":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 10c0/904e767bb9c494929be013017380cbba013637da1b28e5943b566031e29df04fba57edf3f093e0914be094648b577372bd8ad247fa98cfba9c600794cd16b599
  languageName: node
  linkType: hard

"source-map@npm:^0.7.4":
  version: 0.7.6
  resolution: "source-map@npm:0.7.6"
  checksum: 10c0/59f6f05538539b274ba771d2e9e32f6c65451982510564438e048bc1352f019c6efcdc6dd07909b1968144941c14015c2c7d4369fb7c4d7d53ae769716dcc16c
  languageName: node
  linkType: hard

"source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"spdx-correct@npm:^3.0.0":
  version: 3.2.0
  resolution: "spdx-correct@npm:3.2.0"
  dependencies:
    spdx-expression-parse: "npm:^3.0.0"
    spdx-license-ids: "npm:^3.0.0"
  checksum: 10c0/49208f008618b9119208b0dadc9208a3a55053f4fd6a0ae8116861bd22696fc50f4142a35ebfdb389e05ccf2de8ad142573fefc9e26f670522d899f7b2fe7386
  languageName: node
  linkType: hard

"spdx-exceptions@npm:^2.1.0":
  version: 2.5.0
  resolution: "spdx-exceptions@npm:2.5.0"
  checksum: 10c0/37217b7762ee0ea0d8b7d0c29fd48b7e4dfb94096b109d6255b589c561f57da93bf4e328c0290046115961b9209a8051ad9f525e48d433082fc79f496a4ea940
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^3.0.0":
  version: 3.0.1
  resolution: "spdx-expression-parse@npm:3.0.1"
  dependencies:
    spdx-exceptions: "npm:^2.1.0"
    spdx-license-ids: "npm:^3.0.0"
  checksum: 10c0/6f8a41c87759fa184a58713b86c6a8b028250f158159f1d03ed9d1b6ee4d9eefdc74181c8ddc581a341aa971c3e7b79e30b59c23b05d2436d5de1c30bdef7171
  languageName: node
  linkType: hard

"spdx-license-ids@npm:^3.0.0":
  version: 3.0.21
  resolution: "spdx-license-ids@npm:3.0.21"
  checksum: 10c0/ecb24c698d8496aa9efe23e0b1f751f8a7a89faedcdfcbfabae772b546c2db46ccde8f3bc447a238eb86bbcd4f73fea88720ef3b8394f7896381bec3d7736411
  languageName: node
  linkType: hard

"spdy-transport@npm:^3.0.0":
  version: 3.0.0
  resolution: "spdy-transport@npm:3.0.0"
  dependencies:
    debug: "npm:^4.1.0"
    detect-node: "npm:^2.0.4"
    hpack.js: "npm:^2.1.6"
    obuf: "npm:^1.1.2"
    readable-stream: "npm:^3.0.6"
    wbuf: "npm:^1.7.3"
  checksum: 10c0/eaf7440fa90724fffc813c386d4a8a7427d967d6e46d7c51d8f8a533d1a6911b9823ea9218703debbae755337e85f110185d7a00ae22ec5c847077b908ce71bb
  languageName: node
  linkType: hard

"spdy@npm:^4.0.0":
  version: 4.0.2
  resolution: "spdy@npm:4.0.2"
  dependencies:
    debug: "npm:^4.1.0"
    handle-thing: "npm:^2.0.0"
    http-deceiver: "npm:^1.2.7"
    select-hose: "npm:^2.0.0"
    spdy-transport: "npm:^3.0.0"
  checksum: 10c0/983509c0be9d06fd00bb9dff713c5b5d35d3ffd720db869acdd5ad7aa6fc0e02c2318b58f75328957d8ff772acdf1f7d19382b6047df342044ff3e2d6805ccdf
  languageName: node
  linkType: hard

"split-string@npm:^3.0.1, split-string@npm:^3.0.2":
  version: 3.1.0
  resolution: "split-string@npm:3.1.0"
  dependencies:
    extend-shallow: "npm:^3.0.0"
  checksum: 10c0/72d7cd625445c7af215130e1e2bc183013bb9dd48a074eda1d35741e2b0dcb355e6df5b5558a62543a24dcec37dd1d6eb7a6228ff510d3c9de0f3dc1d1da8a70
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10c0/ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"stackblur-canvas@npm:^2.0.0":
  version: 2.7.0
  resolution: "stackblur-canvas@npm:2.7.0"
  checksum: 10c0/df290d0629056d5bb43d37548d0b24cb8593c79d742650e68489abf61013db578c9980724c2508bb738d107204f2e2494ab94c3cf69d6b725caa9c63b8c7e272
  languageName: node
  linkType: hard

"static-extend@npm:^0.1.1":
  version: 0.1.2
  resolution: "static-extend@npm:0.1.2"
  dependencies:
    define-property: "npm:^0.2.5"
    object-copy: "npm:^0.1.0"
  checksum: 10c0/284f5865a9e19d079f1badbcd70d5f9f82e7a08393f818a220839cd5f71729e89105e1c95322bd28e833161d484cee671380ca443869ae89578eef2bf55c0653
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10c0/34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"statuses@npm:>= 1.4.0 < 2":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: 10c0/e433900956357b3efd79b1c547da4d291799ac836960c016d10a98f6a810b1b5c0dcc13b5a7aa609a58239b5190e1ea176ad9221c2157d2fd1c747393e6b2940
  languageName: node
  linkType: hard

"stop-iteration-iterator@npm:^1.1.0":
  version: 1.1.0
  resolution: "stop-iteration-iterator@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    internal-slot: "npm:^1.1.0"
  checksum: 10c0/de4e45706bb4c0354a4b1122a2b8cc45a639e86206807ce0baf390ee9218d3ef181923fa4d2b67443367c491aa255c5fbaa64bb74648e3c5b48299928af86c09
  languageName: node
  linkType: hard

"stream-browserify@npm:^2.0.1":
  version: 2.0.2
  resolution: "stream-browserify@npm:2.0.2"
  dependencies:
    inherits: "npm:~2.0.1"
    readable-stream: "npm:^2.0.2"
  checksum: 10c0/485562bd5d962d633ae178449029c6fa2611052e356bdb5668f768544aa4daa94c4f9a97de718f3f30ad98f3cb98a5f396252bb3855aff153c138f79c0e8f6ac
  languageName: node
  linkType: hard

"stream-http@npm:^2.7.2":
  version: 2.8.3
  resolution: "stream-http@npm:2.8.3"
  dependencies:
    builtin-status-codes: "npm:^3.0.0"
    inherits: "npm:^2.0.1"
    readable-stream: "npm:^2.3.6"
    to-arraybuffer: "npm:^1.0.0"
    xtend: "npm:^4.0.0"
  checksum: 10c0/fbe7d327a29216bbabe88d3819bb8f7a502f11eeacf3212579e5af1f76fa7283f6ffa66134ab7d80928070051f571d1029e85f65ce3369fffd4c4df3669446c4
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.1.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^1.0.1, string-width@npm:^1.0.2":
  version: 1.0.2
  resolution: "string-width@npm:1.0.2"
  dependencies:
    code-point-at: "npm:^1.0.0"
    is-fullwidth-code-point: "npm:^1.0.0"
    strip-ansi: "npm:^3.0.0"
  checksum: 10c0/c558438baed23a9ab9370bb6a939acbdb2b2ffc517838d651aad0f5b2b674fb85d460d9b1d0b6a4c210dffd09e3235222d89a5bd4c0c1587f78b2bb7bc00c65e
  languageName: node
  linkType: hard

"string-width@npm:^2.0.0":
  version: 2.1.1
  resolution: "string-width@npm:2.1.1"
  dependencies:
    is-fullwidth-code-point: "npm:^2.0.0"
    strip-ansi: "npm:^4.0.0"
  checksum: 10c0/e5f2b169fcf8a4257a399f95d069522f056e92ec97dbdcb9b0cdf14d688b7ca0b1b1439a1c7b9773cd79446cbafd582727279d6bfdd9f8edd306ea5e90e5b610
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.12":
  version: 4.0.12
  resolution: "string.prototype.matchall@npm:4.0.12"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.6"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.6"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    internal-slot: "npm:^1.1.0"
    regexp.prototype.flags: "npm:^1.5.3"
    set-function-name: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10c0/1a53328ada73f4a77f1fdf1c79414700cf718d0a8ef6672af5603e709d26a24f2181208144aed7e858b1bcc1a0d08567a570abfb45567db4ae47637ed2c2f85c
  languageName: node
  linkType: hard

"string.prototype.repeat@npm:^1.0.0":
  version: 1.0.0
  resolution: "string.prototype.repeat@npm:1.0.0"
  dependencies:
    define-properties: "npm:^1.1.3"
    es-abstract: "npm:^1.17.5"
  checksum: 10c0/94c7978566cffa1327d470fd924366438af9b04b497c43a9805e476e2e908aa37a1fd34cc0911156c17556dab62159d12c7b92b3cc304c3e1281fe4c8e668f40
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-data-property: "npm:^1.1.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-object-atoms: "npm:^1.0.0"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10c0/8a8854241c4b54a948e992eb7dd6b8b3a97185112deb0037a134f5ba57541d8248dd610c966311887b6c2fd1181a3877bffb14d873ce937a344535dabcc648f8
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/59e1a70bf9414cb4c536a6e31bef5553c8ceb0cf44d8b4d0ed65c9653358d1c64dd0ec203b100df83d0413bbcde38b8c5d49e14bc4b86737d74adc593a0d35b6
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/d53af1899959e53c83b64a5fd120be93e067da740e7e75acb433849aa640782fb6c7d4cd5b84c954c84413745a3764df135a8afeb22908b86a835290788d8366
  languageName: node
  linkType: hard

"string_decoder@npm:^1.0.0, string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 10c0/b4f89f3a92fd101b5653ca3c99550e07bdf9e13b35037e9e2a1c7b47cec4e55e06ff3fc468e314a0b5e80bfbaf65c1ca5a84978764884ae9413bec1fc6ca924e
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10c0/810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^3.0.0, strip-ansi@npm:^3.0.1":
  version: 3.0.1
  resolution: "strip-ansi@npm:3.0.1"
  dependencies:
    ansi-regex: "npm:^2.0.0"
  checksum: 10c0/f6e7fbe8e700105dccf7102eae20e4f03477537c74b286fd22cfc970f139002ed6f0d9c10d0e21aa9ed9245e0fa3c9275930e8795c5b947da136e4ecb644a70f
  languageName: node
  linkType: hard

"strip-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-ansi@npm:4.0.0"
  dependencies:
    ansi-regex: "npm:^3.0.0"
  checksum: 10c0/d75d9681e0637ea316ddbd7d4d3be010b1895a17e885155e0ed6a39755ae0fd7ef46e14b22162e66a62db122d3a98ab7917794e255532ab461bb0a04feb03e7d
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-bom@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-bom@npm:2.0.0"
  dependencies:
    is-utf8: "npm:^0.2.0"
  checksum: 10c0/4fcbb248af1d5c1f2d710022b7d60245077e7942079bfb7ef3fc8c1ae78d61e96278525ba46719b15ab12fced5c7603777105bc898695339d7c97c64d300ed0b
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 10c0/51201f50e021ef16672593d7434ca239441b7b760e905d9f33df6e4f3954ff54ec0e0a06f100d028af0982d6f25c35cd5cda2ce34eaebccd0250b8befb90d8f1
  languageName: node
  linkType: hard

"strip-eof@npm:^1.0.0":
  version: 1.0.0
  resolution: "strip-eof@npm:1.0.0"
  checksum: 10c0/f336beed8622f7c1dd02f2cbd8422da9208fae81daf184f73656332899978919d5c0ca84dc6cfc49ad1fc4dd7badcde5412a063cf4e0d7f8ed95a13a63f68f45
  languageName: node
  linkType: hard

"strip-indent@npm:^1.0.1":
  version: 1.0.1
  resolution: "strip-indent@npm:1.0.1"
  dependencies:
    get-stdin: "npm:^4.0.1"
  bin:
    strip-indent: cli.js
  checksum: 10c0/671370d44105b63daf4582a42f0a0168d58a351f6558eb913d1ede05d0ad5f964548b99f15c63fa6c7415c3980aad72f28c62997fd98fbb6da2eee1051d3c21a
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10c0/9681a6257b925a7fa0f285851c0e613cc934a50661fa7bb41ca9cbbff89686bb4a0ee366e6ecedc4daafd01e83eee0720111ab294366fe7c185e935475ebcecd
  languageName: node
  linkType: hard

"style-to-js@npm:1.1.17":
  version: 1.1.17
  resolution: "style-to-js@npm:1.1.17"
  dependencies:
    style-to-object: "npm:1.0.9"
  checksum: 10c0/429b9d5593a238d73761324e2c12f75b238f6964e12e4ecf7ea02b44c0ec1940b45c1c1fa8fac9a58637b753aa3ce973a2413b2b6da679584117f27a79e33ba3
  languageName: node
  linkType: hard

"style-to-object@npm:1.0.9":
  version: 1.0.9
  resolution: "style-to-object@npm:1.0.9"
  dependencies:
    inline-style-parser: "npm:0.2.4"
  checksum: 10c0/acc89a291ac348a57fa1d00b8eb39973ea15a6c7d7fe4b11339ea0be3b84acea3670c98aa22e166be20ca3d67e12f68f83cf114dde9d43ebb692593e859a804f
  languageName: node
  linkType: hard

"styled-components@npm:^5.3.11":
  version: 5.3.11
  resolution: "styled-components@npm:5.3.11"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.0.0"
    "@babel/traverse": "npm:^7.4.5"
    "@emotion/is-prop-valid": "npm:^1.1.0"
    "@emotion/stylis": "npm:^0.8.4"
    "@emotion/unitless": "npm:^0.7.4"
    babel-plugin-styled-components: "npm:>= 1.12.0"
    css-to-react-native: "npm:^3.0.0"
    hoist-non-react-statics: "npm:^3.0.0"
    shallowequal: "npm:^1.1.0"
    supports-color: "npm:^5.5.0"
  peerDependencies:
    react: ">= 16.8.0"
    react-dom: ">= 16.8.0"
    react-is: ">= 16.8.0"
  checksum: 10c0/90b73479770c5d68e22e6366d210119d7203154a3e49dc828f6f6b4c2d5c077f7548210dfddd0af3cb15b0b63fab3eec8dc995c1734e97a313a9b83ba893668e
  languageName: node
  linkType: hard

"stylis@npm:4.2.0":
  version: 4.2.0
  resolution: "stylis@npm:4.2.0"
  checksum: 10c0/a7128ad5a8ed72652c6eba46bed4f416521bc9745a460ef5741edc725252cebf36ee45e33a8615a7057403c93df0866ab9ee955960792db210bb80abd5ac6543
  languageName: node
  linkType: hard

"supports-color@npm:^4.2.1":
  version: 4.5.0
  resolution: "supports-color@npm:4.5.0"
  dependencies:
    has-flag: "npm:^2.0.0"
  checksum: 10c0/2dc369eeac73954e87037dea1ebae0238b2abc0a39d7e35aa60eb8a84cc8d1dcade8b62e010597f5859f94c937e992abe6a6195460855fcc5e51f8cfc7fcc72a
  languageName: node
  linkType: hard

"supports-color@npm:^5.1.0, supports-color@npm:^5.5.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 10c0/6ae5ff319bfbb021f8a86da8ea1f8db52fac8bd4d499492e30ec17095b58af11f0c55f8577390a749b1c4dde691b6a0315dab78f5f54c9b3d83f8fb5905c1c05
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10c0/6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"svg-arc-to-cubic-bezier@npm:^3.0.0, svg-arc-to-cubic-bezier@npm:^3.2.0":
  version: 3.2.0
  resolution: "svg-arc-to-cubic-bezier@npm:3.2.0"
  checksum: 10c0/6dddbaff9defa55a891593b48ba6bd5f1b3c89d747cf818a8342ec4cac10b06de6cd37478dc9483cba5e8d1a7d3dac2d0bbab47858d207a2d1a6a61b5acdd442
  languageName: node
  linkType: hard

"svg-pathdata@npm:^6.0.3":
  version: 6.0.3
  resolution: "svg-pathdata@npm:6.0.3"
  checksum: 10c0/1ba4ad2fa81e86df37d6e78d3be9e664bbedf97773b725a863a85db384285be32dc37d9c0d61e477d89594ee95b967d2c53d6bee2d76420aab670ab4124a38b9
  languageName: node
  linkType: hard

"symbol-tree@npm:^3.2.4":
  version: 3.2.4
  resolution: "symbol-tree@npm:3.2.4"
  checksum: 10c0/dfbe201ae09ac6053d163578778c53aa860a784147ecf95705de0cd23f42c851e1be7889241495e95c37cabb058edb1052f141387bef68f705afc8f9dd358509
  languageName: node
  linkType: hard

"tapable@npm:^0.2.7":
  version: 0.2.9
  resolution: "tapable@npm:0.2.9"
  checksum: 10c0/93d0e5994621a9496112fe241784d59325684b509eb74d1abd9032802639b53ba9619db0f5db6ccdab994a5279bc4ed07b638a8a25143ba6b10603e409f314fb
  languageName: node
  linkType: hard

"tar@npm:^6.1.11":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: "npm:^2.0.0"
    fs-minipass: "npm:^2.0.0"
    minipass: "npm:^5.0.0"
    minizlib: "npm:^2.1.1"
    mkdirp: "npm:^1.0.3"
    yallist: "npm:^4.0.0"
  checksum: 10c0/a5eca3eb50bc11552d453488344e6507156b9193efd7635e98e867fab275d527af53d8866e2370cd09dfe74378a18111622ace35af6a608e5223a7d27fe99537
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"text-segmentation@npm:^1.0.3":
  version: 1.0.3
  resolution: "text-segmentation@npm:1.0.3"
  dependencies:
    utrie: "npm:^1.0.2"
  checksum: 10c0/8b9ae8524e3a332371060d0ca62f10ad49a13e954719ea689a6c3a8b8c15c8a56365ede2bb91c322fb0d44b6533785f0da603e066b7554d052999967fb72d600
  languageName: node
  linkType: hard

"thunky@npm:^1.0.2":
  version: 1.1.0
  resolution: "thunky@npm:1.1.0"
  checksum: 10c0/369764f39de1ce1de2ba2fa922db4a3f92e9c7f33bcc9a713241bc1f4a5238b484c17e0d36d1d533c625efb00e9e82c3e45f80b47586945557b45abb890156d2
  languageName: node
  linkType: hard

"time-stamp@npm:^2.0.0":
  version: 2.2.0
  resolution: "time-stamp@npm:2.2.0"
  checksum: 10c0/589e83c9e01b7c1d81edb171256fed810e4b4325ac8b4131f657ab4728c70de740a9620911a269ff881ad89d83e9698d178166cbdf947807ba91e6b19bad549f
  languageName: node
  linkType: hard

"timers-browserify@npm:^2.0.4":
  version: 2.0.12
  resolution: "timers-browserify@npm:2.0.12"
  dependencies:
    setimmediate: "npm:^1.0.4"
  checksum: 10c0/98e84db1a685bc8827c117a8bc62aac811ad56a995d07938fc7ed8cdc5bf3777bfe2d4e5da868847194e771aac3749a20f6cdd22091300fe889a76fe214a4641
  languageName: node
  linkType: hard

"tiny-inflate@npm:^1.0.0, tiny-inflate@npm:^1.0.3":
  version: 1.0.3
  resolution: "tiny-inflate@npm:1.0.3"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"tiny-invariant@npm:^1.0.0, tiny-invariant@npm:^1.3.3":
  version: 1.3.3
  resolution: "tiny-invariant@npm:1.3.3"
  checksum: 10c0/65af4a07324b591a059b35269cd696aba21bef2107f29b9f5894d83cc143159a204b299553435b03874ebb5b94d019afa8b8eff241c8a4cfee95872c2e1c1c4a
  languageName: node
  linkType: hard

"tiny-warning@npm:^1.0.3":
  version: 1.0.3
  resolution: "tiny-warning@npm:1.0.3"
  checksum: 10c0/ef8531f581b30342f29670cb41ca248001c6fd7975ce22122bd59b8d62b4fc84ad4207ee7faa95cde982fa3357cd8f4be650142abc22805538c3b1392d7084fa
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12, tinyglobby@npm:^0.2.14":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/f789ed6c924287a9b7d3612056ed0cda67306cd2c80c249fd280cf1504742b12583a2089b61f4abbd24605f390809017240e250241f09938054c9b363e51c0a6
  languageName: node
  linkType: hard

"to-arraybuffer@npm:^1.0.0":
  version: 1.0.1
  resolution: "to-arraybuffer@npm:1.0.1"
  checksum: 10c0/2460bd95524f4845a751e4f8bf9937f9f3dcd1651f104e1512868782f858f8302c1cf25bbc30794bc1b3ff65c4e135158377302f2abaff43a2d8e3c38dfe098c
  languageName: node
  linkType: hard

"to-buffer@npm:^1.2.0":
  version: 1.2.1
  resolution: "to-buffer@npm:1.2.1"
  dependencies:
    isarray: "npm:^2.0.5"
    safe-buffer: "npm:^5.2.1"
    typed-array-buffer: "npm:^1.0.3"
  checksum: 10c0/bbf07a2a7d6ff9e3ffe503c689176c7149cf3ec25887ce7c4aa5c4841a8845cc71121cd7b4a4769957f823b3f31dbf6b1be6e0a5955798ad864bf2245ee8b5e4
  languageName: node
  linkType: hard

"to-object-path@npm:^0.3.0":
  version: 0.3.0
  resolution: "to-object-path@npm:0.3.0"
  dependencies:
    kind-of: "npm:^3.0.2"
  checksum: 10c0/731832a977614c03a770363ad2bd9e9c82f233261861724a8e612bb90c705b94b1a290a19f52958e8e179180bb9b71121ed65e245691a421467726f06d1d7fc3
  languageName: node
  linkType: hard

"to-regex-range@npm:^2.1.0":
  version: 2.1.1
  resolution: "to-regex-range@npm:2.1.1"
  dependencies:
    is-number: "npm:^3.0.0"
    repeat-string: "npm:^1.6.1"
  checksum: 10c0/440d82dbfe0b2e24f36dd8a9467240406ad1499fc8b2b0f547372c22ed1d092ace2a3eb522bb09bfd9c2f39bf1ca42eb78035cf6d2b8c9f5c78da3abc96cd949
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"to-regex@npm:^3.0.1, to-regex@npm:^3.0.2":
  version: 3.0.2
  resolution: "to-regex@npm:3.0.2"
  dependencies:
    define-property: "npm:^2.0.2"
    extend-shallow: "npm:^3.0.2"
    regex-not: "npm:^1.0.2"
    safe-regex: "npm:^1.1.0"
  checksum: 10c0/99d0b8ef397b3f7abed4bac757b0f0bb9f52bfd39167eb7105b144becfaa9a03756892352d01ac6a911f0c1ceef9f81db68c46899521a3eed054082042796120
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10c0/93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"tough-cookie@npm:^4.1.2":
  version: 4.1.4
  resolution: "tough-cookie@npm:4.1.4"
  dependencies:
    psl: "npm:^1.1.33"
    punycode: "npm:^2.1.1"
    universalify: "npm:^0.2.0"
    url-parse: "npm:^1.5.3"
  checksum: 10c0/aca7ff96054f367d53d1e813e62ceb7dd2eda25d7752058a74d64b7266fd07be75908f3753a32ccf866a2f997604b414cfb1916d6e7f69bc64d9d9939b0d6c45
  languageName: node
  linkType: hard

"tr46@npm:^3.0.0":
  version: 3.0.0
  resolution: "tr46@npm:3.0.0"
  dependencies:
    punycode: "npm:^2.1.1"
  checksum: 10c0/cdc47cad3a9d0b6cb293e39ccb1066695ae6fdd39b9e4f351b010835a1f8b4f3a6dc3a55e896b421371187f22b48d7dac1b693de4f6551bdef7b6ab6735dfe3b
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"trim-newlines@npm:^1.0.0":
  version: 1.0.0
  resolution: "trim-newlines@npm:1.0.0"
  checksum: 10c0/ae859c83d0dbcbde32245509f7c51a4bc9696d56e080bc19acc95c4188381e34fba05a4b2fefb47b4ee4537150a11d57a0fd3cd1179837c06210795d7f62e795
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "ts-api-utils@npm:2.1.0"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: 10c0/9806a38adea2db0f6aa217ccc6bc9c391ddba338a9fe3080676d0d50ed806d305bb90e8cef0276e793d28c8a929f400abb184ddd7ff83a416959c0f4d2ce754f
  languageName: node
  linkType: hard

"tslib@npm:^1.11.1":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: 10c0/69ae09c49eea644bc5ebe1bca4fa4cc2c82b7b3e02f43b84bd891504edf66dbc6b2ec0eef31a957042de2269139e4acff911e6d186a258fb14069cd7f6febce2
  languageName: node
  linkType: hard

"tslib@npm:^2.0.1, tslib@npm:^2.1.0, tslib@npm:^2.8.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"tsx@npm:^4.19.2":
  version: 4.20.3
  resolution: "tsx@npm:4.20.3"
  dependencies:
    esbuild: "npm:~0.25.0"
    fsevents: "npm:~2.3.3"
    get-tsconfig: "npm:^4.7.5"
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    tsx: dist/cli.mjs
  checksum: 10c0/6ff0d91ed046ec743fac7ed60a07f3c025e5b71a5aaf58f3d2a6b45e4db114c83e59ebbb078c8e079e48d3730b944a02bc0de87695088aef4ec8bbc705dc791b
  languageName: node
  linkType: hard

"tty-browserify@npm:0.0.0":
  version: 0.0.0
  resolution: "tty-browserify@npm:0.0.0"
  checksum: 10c0/c0c68206565f1372e924d5cdeeff1a0d9cc729833f1da98c03d78be8f939e5f61a107bd0ab77d1ef6a47d62bb0e48b1081fbea273acf404959e22fd3891439c5
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10c0/7b3fd0ed43891e2080bf0c5c504b418fbb3e5c7b9708d3d015037ba2e6323a28152ec163bcb65212741fa5d2022e3075ac3c76440dbd344c9035f818e8ecee58
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: 10c0/a23daeb538591b7efbd61ecf06b6feb2501b683ffdc9a19c74ef5baba362b4347e42f1b4ed81f5882a8c96a3bfff7f93ce3ffaf0cbbc879b532b04c97a55db9d
  languageName: node
  linkType: hard

"type@npm:^2.7.2":
  version: 2.7.3
  resolution: "type@npm:2.7.3"
  checksum: 10c0/dec6902c2c42fcb86e3adf8cdabdf80e5ef9de280872b5fd547351e9cca2fe58dd2aa6d2547626ddff174145db272f62d95c7aa7038e27c11315657d781a688d
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/1105071756eb248774bc71646bfe45b682efcad93b55532c6ffa4518969fb6241354e4aa62af679ae83899ec296d69ef88f1f3763657cdb3a4d29321f7b83079
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10c0/6ae083c6f0354f1fce18b90b243343b9982affd8d839c57bbd2c174a5d5dc71be9eb7019ffd12628a96a4815e7afa85d718d6f1e758615151d5f35df841ffb3e
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.15"
    reflect.getprototypeof: "npm:^1.0.9"
  checksum: 10c0/3d805b050c0c33b51719ee52de17c1cd8e6a571abdf0fffb110e45e8dd87a657e8b56eee94b776b13006d3d347a0c18a730b903cf05293ab6d92e99ff8f77e53
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.7"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.13"
    possible-typed-array-names: "npm:^1.0.0"
    reflect.getprototypeof: "npm:^1.0.6"
  checksum: 10c0/e38f2ae3779584c138a2d8adfa8ecf749f494af3cd3cdafe4e688ce51418c7d2c5c88df1bd6be2bbea099c3f7cea58c02ca02ed438119e91f162a9de23f61295
  languageName: node
  linkType: hard

"typescript-eslint@npm:^8.36.0":
  version: 8.39.0
  resolution: "typescript-eslint@npm:8.39.0"
  dependencies:
    "@typescript-eslint/eslint-plugin": "npm:8.39.0"
    "@typescript-eslint/parser": "npm:8.39.0"
    "@typescript-eslint/typescript-estree": "npm:8.39.0"
    "@typescript-eslint/utils": "npm:8.39.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <6.0.0"
  checksum: 10c0/4625a271dc18b37ab454688ded9812f30178cb79413f6fd7a7959cff834e8b0e78066d478781509c0f85e14e93126d2271576e2c9788de17d0316c385cfb75e7
  languageName: node
  linkType: hard

"typescript@npm:^5.8.3":
  version: 5.9.2
  resolution: "typescript@npm:5.9.2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/cd635d50f02d6cf98ed42de2f76289701c1ec587a363369255f01ed15aaf22be0813226bff3c53e99d971f9b540e0b3cc7583dbe05faded49b1b0bed2f638a18
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A^5.8.3#optional!builtin<compat/typescript>":
  version: 5.9.2
  resolution: "typescript@patch:typescript@npm%3A5.9.2#optional!builtin<compat/typescript>::version=5.9.2&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/34d2a8e23eb8e0d1875072064d5e1d9c102e0bdce56a10a25c0b917b8aa9001a9cf5c225df12497e99da107dc379360bc138163c66b55b95f5b105b50578067e
  languageName: node
  linkType: hard

"uglify-js@npm:^2.8.29":
  version: 2.8.29
  resolution: "uglify-js@npm:2.8.29"
  dependencies:
    source-map: "npm:~0.5.1"
    uglify-to-browserify: "npm:~1.0.0"
    yargs: "npm:~3.10.0"
  dependenciesMeta:
    uglify-to-browserify:
      optional: true
  bin:
    uglifyjs: bin/uglifyjs
  checksum: 10c0/e2e5d67a551975d70aef4b693d369934b4917b17dd2a36270cabd8df884eeb59cb517d9a0a52ca996f11f3b3e202f9563bf620089ffa73d804d08619e338f080
  languageName: node
  linkType: hard

"uglify-to-browserify@npm:~1.0.0":
  version: 1.0.2
  resolution: "uglify-to-browserify@npm:1.0.2"
  checksum: 10c0/7927b554c2cbc110f3e6a5f8f5ed430710e72adadd9dc900fc3393b6d67f281048f2dfd1b2da3df9e88b9308b215247688d16337dccd5202d56636173a83ce16
  languageName: node
  linkType: hard

"uglifyjs-webpack-plugin@npm:^0.4.6":
  version: 0.4.6
  resolution: "uglifyjs-webpack-plugin@npm:0.4.6"
  dependencies:
    source-map: "npm:^0.5.6"
    uglify-js: "npm:^2.8.29"
    webpack-sources: "npm:^1.0.1"
  peerDependencies:
    webpack: ^1.9 || ^2 || ^2.1.0-beta || ^2.2.0-rc || ^3.0.0
  checksum: 10c0/76d3b9fc83274e916bda9e05585a3efe20baffb5b05300f21e1e9159b9c70813bec8081e51c0d6e5c5a242d25586b015241cbc6695b4403b78fb2ff73998ccc9
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    which-boxed-primitive: "npm:^1.1.1"
  checksum: 10c0/7dbd35ab02b0e05fe07136c72cb9355091242455473ec15057c11430129bab38b7b3624019b8778d02a881c13de44d63cd02d122ee782fb519e1de7775b5b982
  languageName: node
  linkType: hard

"underscore@npm:^1.13.1":
  version: 1.13.7
  resolution: "underscore@npm:1.13.7"
  checksum: 10c0/fad2b4aac48847674aaf3c30558f383399d4fdafad6dd02dd60e4e1b8103b52c5a9e5937e0cc05dacfd26d6a0132ed0410ab4258241240757e4a4424507471cd
  languageName: node
  linkType: hard

"undici-types@npm:~7.10.0":
  version: 7.10.0
  resolution: "undici-types@npm:7.10.0"
  checksum: 10c0/8b00ce50e235fe3cc601307f148b5e8fb427092ee3b23e8118ec0a5d7f68eca8cee468c8fc9f15cbb2cf2a3797945ebceb1cbd9732306a1d00e0a9b6afa0f635
  languageName: node
  linkType: hard

"undici@npm:^7.12.0":
  version: 7.13.0
  resolution: "undici@npm:7.13.0"
  checksum: 10c0/8865d40b141f073215a6763aad5d1b2f4bd4e252600e93e68055d6c5d23a8a0e5782669236b2ecfa4d415d1d969d9c4623ff1c0386d32fa60088a19ffa58c611
  languageName: node
  linkType: hard

"unicode-properties@npm:^1.4.0, unicode-properties@npm:^1.4.1":
  version: 1.4.1
  resolution: "unicode-properties@npm:1.4.1"
  dependencies:
    base64-js: "npm:^1.3.0"
    unicode-trie: "npm:^2.0.0"
  checksum: 10c0/1d140b7945664fb0ef53de955170821e077b949eef377c6e4905902f07e339039271bfa2a005e4f4c6074b080d3420b486c52dc905e11f924949a04d1fb47ffd
  languageName: node
  linkType: hard

"unicode-trie@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-trie@npm:2.0.0"
  dependencies:
    pako: "npm:^0.2.5"
    tiny-inflate: "npm:^1.0.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"union-value@npm:^1.0.0":
  version: 1.0.1
  resolution: "union-value@npm:1.0.1"
  dependencies:
    arr-union: "npm:^3.1.0"
    get-value: "npm:^2.0.6"
    is-extendable: "npm:^0.1.1"
    set-value: "npm:^2.0.1"
  checksum: 10c0/8758d880cb9545f62ce9cfb9b791b2b7a206e0ff5cc4b9d7cd6581da2c6839837fbb45e639cf1fd8eef3cae08c0201b614b7c06dd9f5f70d9dbe7c5fe2fbf592
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"universalify@npm:^0.2.0":
  version: 0.2.0
  resolution: "universalify@npm:0.2.0"
  checksum: 10c0/cedbe4d4ca3967edf24c0800cfc161c5a15e240dac28e3ce575c689abc11f2c81ccc6532c8752af3b40f9120fb5e454abecd359e164f4f6aa44c29cd37e194fe
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10c0/193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"unplugin@npm:^2.1.2":
  version: 2.3.5
  resolution: "unplugin@npm:2.3.5"
  dependencies:
    acorn: "npm:^8.14.1"
    picomatch: "npm:^4.0.2"
    webpack-virtual-modules: "npm:^0.6.2"
  checksum: 10c0/42d172be9b52cc139c69a8baefab6c44e99d7fbdafb9e5738348bc60f6c1302b9913543641f69b76bb07c6ff7d01fde3d8a67c9d3d7a976cc17d972c1ff88081
  languageName: node
  linkType: hard

"unset-value@npm:^1.0.0":
  version: 1.0.0
  resolution: "unset-value@npm:1.0.0"
  dependencies:
    has-value: "npm:^0.3.1"
    isobject: "npm:^3.0.0"
  checksum: 10c0/68a796dde4a373afdbf017de64f08490a3573ebee549136da0b3a2245299e7f65f647ef70dc13c4ac7f47b12fba4de1646fa0967a365638578fedce02b9c0b1f
  languageName: node
  linkType: hard

"upath@npm:^1.1.1":
  version: 1.2.0
  resolution: "upath@npm:1.2.0"
  checksum: 10c0/3746f24099bf69dbf8234cecb671e1016e1f6b26bd306de4ff8966fb0bc463fa1014ffc48646b375de1ab573660e3a0256f6f2a87218b2dfa1779a84ef6992fa
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10c0/682e8ecbf9de474a626f6462aa85927936cdd256fe584c6df2508b0df9f7362c44c957e9970df55dfe44d3623807d26316ea2c7d26b80bb76a16c56c37233c32
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"urix@npm:^0.1.0":
  version: 0.1.0
  resolution: "urix@npm:0.1.0"
  checksum: 10c0/264f1b29360c33c0aec5fb9819d7e28f15d1a3b83175d2bcc9131efe8583f459f07364957ae3527f1478659ec5b2d0f1ad401dfb625f73e4d424b3ae35fc5fc0
  languageName: node
  linkType: hard

"url-parse@npm:^1.1.8, url-parse@npm:^1.4.3, url-parse@npm:^1.5.3":
  version: 1.5.10
  resolution: "url-parse@npm:1.5.10"
  dependencies:
    querystringify: "npm:^2.1.1"
    requires-port: "npm:^1.0.0"
  checksum: 10c0/bd5aa9389f896974beb851c112f63b466505a04b4807cea2e5a3b7092f6fbb75316f0491ea84e44f66fed55f1b440df5195d7e3a8203f64fcefa19d182f5be87
  languageName: node
  linkType: hard

"url@npm:^0.11.0":
  version: 0.11.4
  resolution: "url@npm:0.11.4"
  dependencies:
    punycode: "npm:^1.4.1"
    qs: "npm:^6.12.3"
  checksum: 10c0/cc93405ae4a9b97a2aa60ca67f1cb1481c0221cb4725a7341d149be5e2f9cfda26fd432d64dbbec693d16593b68b8a46aad8e5eab21f814932134c9d8620c662
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.5.0":
  version: 1.5.0
  resolution: "use-sync-external-store@npm:1.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/1b8663515c0be34fa653feb724fdcce3984037c78dd4a18f68b2c8be55cc1a1084c578d5b75f158d41b5ddffc2bf5600766d1af3c19c8e329bb20af2ec6f52f4
  languageName: node
  linkType: hard

"use@npm:^3.1.0":
  version: 3.1.1
  resolution: "use@npm:3.1.1"
  checksum: 10c0/75b48673ab80d5139c76922630d5a8a44e72ed58dbaf54dee1b88352d10e1c1c1fc332066c782d8ae9a56503b85d3dc67ff6d2ffbd9821120466d1280ebb6d6e
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"util@npm:^0.10.4":
  version: 0.10.4
  resolution: "util@npm:0.10.4"
  dependencies:
    inherits: "npm:2.0.3"
  checksum: 10c0/d29f6893e406b63b088ce9924da03201df89b31490d4d011f1c07a386ea4b3dbe907464c274023c237da470258e1805d806c7e4009a5974cd6b1d474b675852a
  languageName: node
  linkType: hard

"util@npm:^0.11.0":
  version: 0.11.1
  resolution: "util@npm:0.11.1"
  dependencies:
    inherits: "npm:2.0.3"
  checksum: 10c0/8e9d1a85e661c8a8d9883d821aedbff3f8d9c3accd85357020905386ada5653b20389fc3591901e2a0bde64f8dc86b28c3f990114aa5a38eaaf30b455fa3cdf6
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10c0/02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"utrie@npm:^1.0.2":
  version: 1.0.2
  resolution: "utrie@npm:1.0.2"
  dependencies:
    base64-arraybuffer: "npm:^1.0.2"
  checksum: 10c0/eaffe645bd81a39e4bc3abb23df5895e9961dbdd49748ef3b173529e8b06ce9dd1163e9705d5309a1c61ee41ffcb825e2043bc0fd1659845ffbdf4b1515dfdb4
  languageName: node
  linkType: hard

"uuid@npm:^3.0.1":
  version: 3.4.0
  resolution: "uuid@npm:3.4.0"
  bin:
    uuid: ./bin/uuid
  checksum: 10c0/1c13950df865c4f506ebfe0a24023571fa80edf2e62364297a537c80af09c618299797bbf2dbac6b1f8ae5ad182ba474b89db61e0e85839683991f7e08795347
  languageName: node
  linkType: hard

"validate-npm-package-license@npm:^3.0.1":
  version: 3.0.4
  resolution: "validate-npm-package-license@npm:3.0.4"
  dependencies:
    spdx-correct: "npm:^3.0.0"
    spdx-expression-parse: "npm:^3.0.0"
  checksum: 10c0/7b91e455a8de9a0beaa9fe961e536b677da7f48c9a493edf4d4d4a87fd80a7a10267d438723364e432c2fcd00b5650b5378275cded362383ef570276e6312f4f
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10c0/f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"vite-compatible-readable-stream@npm:^3.6.1":
  version: 3.6.1
  resolution: "vite-compatible-readable-stream@npm:3.6.1"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10c0/cfed3833a77275584e0421e44b0c95ca2f31af7f4346c3ea35527949c2d2ed708b6490bdafec958deb94fc7a5e20bb0c66f8e9942b30109b568e7244fdcb1e73
  languageName: node
  linkType: hard

"vite@npm:^7.0.2":
  version: 7.0.6
  resolution: "vite@npm:7.0.6"
  dependencies:
    esbuild: "npm:^0.25.0"
    fdir: "npm:^6.4.6"
    fsevents: "npm:~2.3.3"
    picomatch: "npm:^4.0.3"
    postcss: "npm:^8.5.6"
    rollup: "npm:^4.40.0"
    tinyglobby: "npm:^0.2.14"
  peerDependencies:
    "@types/node": ^20.19.0 || >=22.12.0
    jiti: ">=1.21.0"
    less: ^4.0.0
    lightningcss: ^1.21.0
    sass: ^1.70.0
    sass-embedded: ^1.70.0
    stylus: ">=0.54.8"
    sugarss: ^5.0.0
    terser: ^5.16.0
    tsx: ^4.8.1
    yaml: ^2.4.2
  dependenciesMeta:
    fsevents:
      optional: true
  peerDependenciesMeta:
    "@types/node":
      optional: true
    jiti:
      optional: true
    less:
      optional: true
    lightningcss:
      optional: true
    sass:
      optional: true
    sass-embedded:
      optional: true
    stylus:
      optional: true
    sugarss:
      optional: true
    terser:
      optional: true
    tsx:
      optional: true
    yaml:
      optional: true
  bin:
    vite: bin/vite.js
  checksum: 10c0/3b14dfa661281b4843789884199ba2a9cca940a7666970036fe3fb1abff52b88e63e8be5ab419dd04d9f96c0415ee0f1e3ec8ebe357041648af7ccd8e348b6ad
  languageName: node
  linkType: hard

"vm-browserify@npm:^1.0.1":
  version: 1.1.2
  resolution: "vm-browserify@npm:1.1.2"
  checksum: 10c0/0cc1af6e0d880deb58bc974921320c187f9e0a94f25570fca6b1bd64e798ce454ab87dfd797551b1b0cc1849307421aae0193cedf5f06bdb5680476780ee344b
  languageName: node
  linkType: hard

"w3c-xmlserializer@npm:^4.0.0":
  version: 4.0.0
  resolution: "w3c-xmlserializer@npm:4.0.0"
  dependencies:
    xml-name-validator: "npm:^4.0.0"
  checksum: 10c0/02cc66d6efc590bd630086cd88252444120f5feec5c4043932b0d0f74f8b060512f79dc77eb093a7ad04b4f02f39da79ce4af47ceb600f2bf9eacdc83204b1a8
  languageName: node
  linkType: hard

"warning@npm:^4.0.0":
  version: 4.0.3
  resolution: "warning@npm:4.0.3"
  dependencies:
    loose-envify: "npm:^1.0.0"
  checksum: 10c0/aebab445129f3e104c271f1637fa38e55eb25f968593e3825bd2f7a12bd58dc3738bb70dc8ec85826621d80b4acfed5a29ebc9da17397c6125864d72301b937e
  languageName: node
  linkType: hard

"watch@npm:^1.0.2":
  version: 1.0.2
  resolution: "watch@npm:1.0.2"
  dependencies:
    exec-sh: "npm:^0.2.0"
    minimist: "npm:^1.2.0"
  bin:
    watch: ./cli.js
  checksum: 10c0/6914c5cd44d6e3cb60c8ab7849ec03c51a07259d86410356630729a14c3f4ccb011865cdb33a052c75fb9ead2eaa646afa32d0fe7682b0b585e65a868b153eca
  languageName: node
  linkType: hard

"watchpack-chokidar2@npm:^2.0.1":
  version: 2.0.1
  resolution: "watchpack-chokidar2@npm:2.0.1"
  dependencies:
    chokidar: "npm:^2.1.8"
  checksum: 10c0/9b8d880ae2543dd4f26a69f6b7f881119494f6b772b7431027a06a5cf963e0ebc1cac91a3ef479365c358b693c65fa80a1f8297427fa11fd4c080c3d6408c372
  languageName: node
  linkType: hard

"watchpack@npm:^1.4.0":
  version: 1.7.5
  resolution: "watchpack@npm:1.7.5"
  dependencies:
    chokidar: "npm:^3.4.1"
    graceful-fs: "npm:^4.1.2"
    neo-async: "npm:^2.5.0"
    watchpack-chokidar2: "npm:^2.0.1"
  dependenciesMeta:
    chokidar:
      optional: true
    watchpack-chokidar2:
      optional: true
  checksum: 10c0/53e3b112064f5de9edbb2a14973fb3901d9697b24cc70f8531a143eaace2353a273ca25c0ba21def8d3803cfedb8f6861ca1e49e9782257e40d5b5f8f5365c86
  languageName: node
  linkType: hard

"wbuf@npm:^1.1.0, wbuf@npm:^1.7.3":
  version: 1.7.3
  resolution: "wbuf@npm:1.7.3"
  dependencies:
    minimalistic-assert: "npm:^1.0.0"
  checksum: 10c0/56edcc5ef2b3d30913ba8f1f5cccc364d180670b24d5f3f8849c1e6fb514e5c7e3a87548ae61227a82859eba6269c11393ae24ce12a2ea1ecb9b465718ddced7
  languageName: node
  linkType: hard

"web-streams-polyfill@npm:^3.2.1":
  version: 3.3.3
  resolution: "web-streams-polyfill@npm:3.3.3"
  checksum: 10c0/64e855c47f6c8330b5436147db1c75cb7e7474d924166800e8e2aab5eb6c76aac4981a84261dd2982b3e754490900b99791c80ae1407a9fa0dcff74f82ea3a7f
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"webidl-conversions@npm:^7.0.0":
  version: 7.0.0
  resolution: "webidl-conversions@npm:7.0.0"
  checksum: 10c0/228d8cb6d270c23b0720cb2d95c579202db3aaf8f633b4e9dd94ec2000a04e7e6e43b76a94509cdb30479bd00ae253ab2371a2da9f81446cc313f89a4213a2c4
  languageName: node
  linkType: hard

"webpack-dev-middleware@npm:1.12.2":
  version: 1.12.2
  resolution: "webpack-dev-middleware@npm:1.12.2"
  dependencies:
    memory-fs: "npm:~0.4.1"
    mime: "npm:^1.5.0"
    path-is-absolute: "npm:^1.0.0"
    range-parser: "npm:^1.0.3"
    time-stamp: "npm:^2.0.0"
  peerDependencies:
    webpack: ^1.0.0 || ^2.0.0 || ^3.0.0
  checksum: 10c0/102b3e86a7008a0aca0b1e0906010086beb94cd0f1317b1813694220ec921da598eb70d1f164c858e8d68030ae6bea7e47f022c0a373a54e2bd918e7a8dcc20d
  languageName: node
  linkType: hard

"webpack-dev-server@npm:^2.9.7":
  version: 2.11.5
  resolution: "webpack-dev-server@npm:2.11.5"
  dependencies:
    ansi-html: "npm:0.0.7"
    array-includes: "npm:^3.0.3"
    bonjour: "npm:^3.5.0"
    chokidar: "npm:^2.1.2"
    compression: "npm:^1.7.3"
    connect-history-api-fallback: "npm:^1.3.0"
    debug: "npm:^3.1.0"
    del: "npm:^3.0.0"
    express: "npm:^4.16.2"
    html-entities: "npm:^1.2.0"
    http-proxy-middleware: "npm:^0.19.1"
    import-local: "npm:^1.0.0"
    internal-ip: "npm:1.2.0"
    ip: "npm:^1.1.5"
    killable: "npm:^1.0.0"
    loglevel: "npm:^1.4.1"
    opn: "npm:^5.1.0"
    portfinder: "npm:^1.0.9"
    selfsigned: "npm:^1.9.1"
    serve-index: "npm:^1.9.1"
    sockjs: "npm:0.3.19"
    sockjs-client: "npm:1.1.5"
    spdy: "npm:^4.0.0"
    strip-ansi: "npm:^3.0.0"
    supports-color: "npm:^5.1.0"
    webpack-dev-middleware: "npm:1.12.2"
    yargs: "npm:6.6.0"
  peerDependencies:
    webpack: ^2.2.0 || ^3.0.0
  bin:
    webpack-dev-server: bin/webpack-dev-server.js
  checksum: 10c0/466e161f65d352098d975ffbb19bba3565b1e3a39c57d8027be79dedcc46143034d7e61a5ca7b28c88c2ff0ea5c68b40f87b30330c897c093c7c924ccc32cd1e
  languageName: node
  linkType: hard

"webpack-sources@npm:^1.0.1":
  version: 1.4.3
  resolution: "webpack-sources@npm:1.4.3"
  dependencies:
    source-list-map: "npm:^2.0.0"
    source-map: "npm:~0.6.1"
  checksum: 10c0/78dafb3e1e297d3f4eb6204311e8c64d28cd028f82887ba33aaf03fffc82482d8e1fdf6de25a60f4dde621d3565f4c3b1bfb350f09add8f4e54e00279ff3db5e
  languageName: node
  linkType: hard

"webpack-virtual-modules@npm:^0.6.2":
  version: 0.6.2
  resolution: "webpack-virtual-modules@npm:0.6.2"
  checksum: 10c0/5ffbddf0e84bf1562ff86cf6fcf039c74edf09d78358a6904a09bbd4484e8bb6812dc385fe14330b715031892dcd8423f7a88278b57c9f5002c84c2860179add
  languageName: node
  linkType: hard

"webpack@npm:^3.10.0":
  version: 3.12.0
  resolution: "webpack@npm:3.12.0"
  dependencies:
    acorn: "npm:^5.0.0"
    acorn-dynamic-import: "npm:^2.0.0"
    ajv: "npm:^6.1.0"
    ajv-keywords: "npm:^3.1.0"
    async: "npm:^2.1.2"
    enhanced-resolve: "npm:^3.4.0"
    escope: "npm:^3.6.0"
    interpret: "npm:^1.0.0"
    json-loader: "npm:^0.5.4"
    json5: "npm:^0.5.1"
    loader-runner: "npm:^2.3.0"
    loader-utils: "npm:^1.1.0"
    memory-fs: "npm:~0.4.1"
    mkdirp: "npm:~0.5.0"
    node-libs-browser: "npm:^2.0.0"
    source-map: "npm:^0.5.3"
    supports-color: "npm:^4.2.1"
    tapable: "npm:^0.2.7"
    uglifyjs-webpack-plugin: "npm:^0.4.6"
    watchpack: "npm:^1.4.0"
    webpack-sources: "npm:^1.0.1"
    yargs: "npm:^8.0.2"
  bin:
    webpack: ./bin/webpack.js
  checksum: 10c0/64051e33b94d641ea6ed85e6d33d0964ff27ce28f4dd9a989dee39ce98b67fadd057fecd2bfdf49c8fe8909c0c74e415f01381e7a105f926968b1a6eb92f5220
  languageName: node
  linkType: hard

"websocket-driver@npm:>=0.5.1":
  version: 0.7.4
  resolution: "websocket-driver@npm:0.7.4"
  dependencies:
    http-parser-js: "npm:>=0.5.1"
    safe-buffer: "npm:>=5.1.0"
    websocket-extensions: "npm:>=0.1.1"
  checksum: 10c0/5f09547912b27bdc57bac17b7b6527d8993aa4ac8a2d10588bb74aebaf785fdcf64fea034aae0c359b7adff2044dd66f3d03866e4685571f81b13e548f9021f1
  languageName: node
  linkType: hard

"websocket-extensions@npm:>=0.1.1":
  version: 0.1.4
  resolution: "websocket-extensions@npm:0.1.4"
  checksum: 10c0/bbc8c233388a0eb8a40786ee2e30d35935cacbfe26ab188b3e020987e85d519c2009fe07cfc37b7f718b85afdba7e54654c9153e6697301f72561bfe429177e0
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^2.0.0":
  version: 2.0.0
  resolution: "whatwg-encoding@npm:2.0.0"
  dependencies:
    iconv-lite: "npm:0.6.3"
  checksum: 10c0/91b90a49f312dc751496fd23a7e68981e62f33afe938b97281ad766235c4872fc4e66319f925c5e9001502b3040dd25a33b02a9c693b73a4cbbfdc4ad10c3e3e
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^3.1.1":
  version: 3.1.1
  resolution: "whatwg-encoding@npm:3.1.1"
  dependencies:
    iconv-lite: "npm:0.6.3"
  checksum: 10c0/273b5f441c2f7fda3368a496c3009edbaa5e43b71b09728f90425e7f487e5cef9eb2b846a31bd760dd8077739c26faf6b5ca43a5f24033172b003b72cf61a93e
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^3.0.0":
  version: 3.0.0
  resolution: "whatwg-mimetype@npm:3.0.0"
  checksum: 10c0/323895a1cda29a5fb0b9ca82831d2c316309fede0365047c4c323073e3239067a304a09a1f4b123b9532641ab604203f33a1403b5ca6a62ef405bcd7a204080f
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^4.0.0":
  version: 4.0.0
  resolution: "whatwg-mimetype@npm:4.0.0"
  checksum: 10c0/a773cdc8126b514d790bdae7052e8bf242970cebd84af62fb2f35a33411e78e981f6c0ab9ed1fe6ec5071b09d5340ac9178e05b52d35a9c4bcf558ba1b1551df
  languageName: node
  linkType: hard

"whatwg-url@npm:^11.0.0":
  version: 11.0.0
  resolution: "whatwg-url@npm:11.0.0"
  dependencies:
    tr46: "npm:^3.0.0"
    webidl-conversions: "npm:^7.0.0"
  checksum: 10c0/f7ec264976d7c725e0696fcaf9ebe056e14422eacbf92fdbb4462034609cba7d0c85ffa1aab05e9309d42969bcf04632ba5ed3f3882c516d7b093053315bf4c1
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: "npm:^1.1.0"
    is-boolean-object: "npm:^1.2.1"
    is-number-object: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
    is-symbol: "npm:^1.1.1"
  checksum: 10c0/aceea8ede3b08dede7dce168f3883323f7c62272b49801716e8332ff750e7ae59a511ae088840bc6874f16c1b7fd296c05c949b0e5b357bfe3c431b98c417abe
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    function.prototype.name: "npm:^1.1.6"
    has-tostringtag: "npm:^1.0.2"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.1.0"
    is-finalizationregistry: "npm:^1.1.0"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.2.1"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.1.0"
    which-collection: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.16"
  checksum: 10c0/8dcf323c45e5c27887800df42fbe0431d0b66b1163849bb7d46b5a730ad6a96ee8bfe827d078303f825537844ebf20c02459de41239a0a9805e2fcb3cae0d471
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: "npm:^2.0.3"
    is-set: "npm:^2.0.3"
    is-weakmap: "npm:^2.0.2"
    is-weakset: "npm:^2.0.3"
  checksum: 10c0/3345fde20964525a04cdf7c4a96821f85f0cc198f1b2ecb4576e08096746d129eb133571998fe121c77782ac8f21cbd67745a3d35ce100d26d4e684c142ea1f2
  languageName: node
  linkType: hard

"which-module@npm:^1.0.0":
  version: 1.0.0
  resolution: "which-module@npm:1.0.0"
  checksum: 10c0/ce5088fb12dae0b6d5997b6221342943ff6275c3b2cd9c569f04ec23847c71013d254c6127d531010dccc22c0fc0f8dce2b6ecf6898941a60b576adb2018af22
  languageName: node
  linkType: hard

"which-module@npm:^2.0.0":
  version: 2.0.1
  resolution: "which-module@npm:2.0.1"
  checksum: 10c0/087038e7992649eaffa6c7a4f3158d5b53b14cf5b6c1f0e043dccfacb1ba179d12f17545d5b85ebd94a42ce280a6fe65d0cbcab70f4fc6daad1dfae85e0e6a3e
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.19":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    for-each: "npm:^0.3.5"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10c0/702b5dc878addafe6c6300c3d0af5983b175c75fcb4f2a72dfc3dd38d93cf9e89581e4b29c854b16ea37e50a7d7fca5ae42ece5c273d8060dcd603b2404bbb3f
  languageName: node
  linkType: hard

"which@npm:^1.2.9":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    which: ./bin/which
  checksum: 10c0/e945a8b6bbf6821aaaef7f6e0c309d4b615ef35699576d5489b4261da9539f70393c6b2ce700ee4321c18f914ebe5644bc4631b15466ffbaad37d83151f6af59
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.2":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: "npm:^1.0.2 || 2 || 3 || 4"
  checksum: 10c0/1d9c2a3e36dfb09832f38e2e699c367ef190f96b82c71f809bc0822c306f5379df87bab47bed27ea99106d86447e50eb972d3c516c2f95782807a9d082fbea95
  languageName: node
  linkType: hard

"window-size@npm:0.1.0":
  version: 0.1.0
  resolution: "window-size@npm:0.1.0"
  checksum: 10c0/4753f1d55afde8e89f49ab161a5c5bff9a5e025443a751f6e9654168c319cf9e429ac9ed19e12241cdf0fb9d7fdc4af220abd18f05ad8e254899d331f798723e
  languageName: node
  linkType: hard

"wl-msg-reader@npm:^0.2.0":
  version: 0.2.1
  resolution: "wl-msg-reader@npm:0.2.1"
  checksum: 10c0/607935df521d6cf3a4e05f3fd2f67bb03ed83b44b43d095c8927799b524258c797a2a12ed256211b859e2dbd0b447b1480ba933b07b8fc889e3c0133e339b98f
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10c0/e0e4a1ca27599c92a6ca4c32260e8a92e8a44f4ef6ef93f803f8ed823f486e0889fc0b93be4db59c8d51b3064951d25e43d434e95dc8c960cc3a63d65d00ba20
  languageName: node
  linkType: hard

"wordwrap@npm:0.0.2":
  version: 0.0.2
  resolution: "wordwrap@npm:0.0.2"
  checksum: 10c0/a697f8d4de35aa5fc09c84756a471a72cf602abcd8f45e132a28b0140369a4abd142676db4daa64632f10472cd7d6fa89daa308914b84ba6a5f43e35b6711501
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^2.0.0":
  version: 2.1.0
  resolution: "wrap-ansi@npm:2.1.0"
  dependencies:
    string-width: "npm:^1.0.1"
    strip-ansi: "npm:^3.0.1"
  checksum: 10c0/1a47367eef192fc9ecaf00238bad5de8987c3368082b619ab36c5e2d6d7b0a2aef95a2ca65840be598c56ced5090a3ba487956c7aee0cac7c45017502fa980fb
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"ws@npm:^8.11.0":
  version: 8.18.3
  resolution: "ws@npm:8.18.3"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/eac918213de265ef7cb3d4ca348b891a51a520d839aa51cdb8ca93d4fa7ff9f6ccb339ccee89e4075324097f0a55157c89fa3f7147bde9d8d7e90335dc087b53
  languageName: node
  linkType: hard

"ws@npm:~8.17.1":
  version: 8.17.1
  resolution: "ws@npm:8.17.1"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/f4a49064afae4500be772abdc2211c8518f39e1c959640457dcee15d4488628620625c783902a52af2dd02f68558da2868fd06e6fd0e67ebcd09e6881b1b5bfe
  languageName: node
  linkType: hard

"xml-name-validator@npm:^4.0.0":
  version: 4.0.0
  resolution: "xml-name-validator@npm:4.0.0"
  checksum: 10c0/c1bfa219d64e56fee265b2bd31b2fcecefc063ee802da1e73bad1f21d7afd89b943c9e2c97af2942f60b1ad46f915a4c81e00039c7d398b53cf410e29d3c30bd
  languageName: node
  linkType: hard

"xmlbuilder@npm:^10.0.0":
  version: 10.1.1
  resolution: "xmlbuilder@npm:10.1.1"
  checksum: 10c0/26c465e8bd16b4e882d39c2e2a29bb277434d254717aa05df117dd0009041d92855426714b2d1a6a5f76983640349f4edb80073b6ae374e0e6c3d13029ea8237
  languageName: node
  linkType: hard

"xmlchars@npm:^2.2.0":
  version: 2.2.0
  resolution: "xmlchars@npm:2.2.0"
  checksum: 10c0/b64b535861a6f310c5d9bfa10834cf49127c71922c297da9d4d1b45eeaae40bf9b4363275876088fbe2667e5db028d2cd4f8ee72eed9bede840a67d57dab7593
  languageName: node
  linkType: hard

"xmlhttprequest-ssl@npm:~2.1.1":
  version: 2.1.2
  resolution: "xmlhttprequest-ssl@npm:2.1.2"
  checksum: 10c0/70d60869323e823f473a238f78fd108437edbc3690ecd5859c39c83217080090a18899b272e515769c0d1f518cc64cbed6b6995b23fdd7ba13b297d530b6e631
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 10c0/366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"y18n@npm:^3.2.1":
  version: 3.2.2
  resolution: "y18n@npm:3.2.2"
  checksum: 10c0/08dc1880f6f766057ed25cd61ef0c7dab3db93639db9a7487a84f75dac7a349dface8dff8d1d8b7bdf50969fcd69ab858ab26b06968b4e4b12ee60d195233c46
  languageName: node
  linkType: hard

"yallist@npm:^2.1.2":
  version: 2.1.2
  resolution: "yallist@npm:2.1.2"
  checksum: 10c0/0b9e25aa00adf19e01d2bcd4b208aee2b0db643d9927131797b7af5ff69480fc80f1c3db738cbf3946f0bddf39d8f2d0a5709c644fd42d4aa3a4e6e786c087b5
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10c0/c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yaml@npm:^1.10.0":
  version: 1.10.2
  resolution: "yaml@npm:1.10.2"
  checksum: 10c0/5c28b9eb7adc46544f28d9a8d20c5b3cb1215a886609a2fd41f51628d8aaa5878ccd628b755dbcd29f6bb4921bd04ffbc6dcc370689bb96e594e2f9813d2605f
  languageName: node
  linkType: hard

"yargs-parser@npm:^4.2.0":
  version: 4.2.1
  resolution: "yargs-parser@npm:4.2.1"
  dependencies:
    camelcase: "npm:^3.0.0"
  checksum: 10c0/84cfede671a1bf3d9ae8349d88f6d93b3fcd835fd05437bc0367b8c6c47aa81b5627bd144953ccbba89d45702fabbb9713fda396ce9e7763a72e19e6c399e743
  languageName: node
  linkType: hard

"yargs-parser@npm:^7.0.0":
  version: 7.0.0
  resolution: "yargs-parser@npm:7.0.0"
  dependencies:
    camelcase: "npm:^4.1.0"
  checksum: 10c0/6fde2df522dd071cf76308fe4e37fd2fb8eae3eb1fb6c6759c2d448264c8451162649eb8558ac8d83aff79278cabfddc17ecba3e1a0d0fe2e406cf4e1bc95b0d
  languageName: node
  linkType: hard

"yargs@npm:6.6.0":
  version: 6.6.0
  resolution: "yargs@npm:6.6.0"
  dependencies:
    camelcase: "npm:^3.0.0"
    cliui: "npm:^3.2.0"
    decamelize: "npm:^1.1.1"
    get-caller-file: "npm:^1.0.1"
    os-locale: "npm:^1.4.0"
    read-pkg-up: "npm:^1.0.1"
    require-directory: "npm:^2.1.1"
    require-main-filename: "npm:^1.0.1"
    set-blocking: "npm:^2.0.0"
    string-width: "npm:^1.0.2"
    which-module: "npm:^1.0.0"
    y18n: "npm:^3.2.1"
    yargs-parser: "npm:^4.2.0"
  checksum: 10c0/d27290e404a279dc4e6b9ae7e378af773c1697e9ceec798adc8ac8bb6c3c12e20309861f5ace03252fa25f93448847e1891adb17edf5ce9e50007b0782a2ee4c
  languageName: node
  linkType: hard

"yargs@npm:^8.0.2":
  version: 8.0.2
  resolution: "yargs@npm:8.0.2"
  dependencies:
    camelcase: "npm:^4.1.0"
    cliui: "npm:^3.2.0"
    decamelize: "npm:^1.1.1"
    get-caller-file: "npm:^1.0.1"
    os-locale: "npm:^2.0.0"
    read-pkg-up: "npm:^2.0.0"
    require-directory: "npm:^2.1.1"
    require-main-filename: "npm:^1.0.1"
    set-blocking: "npm:^2.0.0"
    string-width: "npm:^2.0.0"
    which-module: "npm:^2.0.0"
    y18n: "npm:^3.2.1"
    yargs-parser: "npm:^7.0.0"
  checksum: 10c0/70e4ed99e3f1e8a034d0b600773db9fb44e03ec88cfa7125fc65a965428051ad59e77c074afa1afcbe3ec34436d3a6e7ba18b3ec62b4e5f59f30606df791b125
  languageName: node
  linkType: hard

"yargs@npm:~3.10.0":
  version: 3.10.0
  resolution: "yargs@npm:3.10.0"
  dependencies:
    camelcase: "npm:^1.0.2"
    cliui: "npm:^2.1.0"
    decamelize: "npm:^1.0.0"
    window-size: "npm:0.1.0"
  checksum: 10c0/df727126b4e664987c5bb1f346fbde24d2d5e6bd435d081d816f1f5890811ceb82f90ac7e6eb849eae749dde6fe5a2eda2c6f2b22021824976399fb4362413c1
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"yoga-layout@npm:^3.2.1":
  version: 3.2.1
  resolution: "yoga-layout@npm:3.2.1"
  checksum: 10c0/9001e51be993c85e03757e5a04a2b61b8b30c9e5a7865d0156ca87a6431a3b717d51eb4990bfe588189fcfeac688dd9c3de707bbd50d1c344a84e63974cc54a8
  languageName: node
  linkType: hard

"zod@npm:^3.24.2":
  version: 3.25.76
  resolution: "zod@npm:3.25.76"
  checksum: 10c0/5718ec35e3c40b600316c5b4c5e4976f7fee68151bc8f8d90ec18a469be9571f072e1bbaace10f1e85cf8892ea12d90821b200e980ab46916a6166a4260a983c
  languageName: node
  linkType: hard

# Demo Data Formats

## 1. <PERSON><PERSON><PERSON> dạng CCCD/CMND (dấu |)
```
123456789012|123456789|<PERSON>uy<PERSON><PERSON>n <PERSON>|01/01/1990|Nam|123 Đường ABC, Quận 1, TP.HCM|01/01/2020
```

## 2. <PERSON><PERSON><PERSON> dạng JSON
```json
{
  "cccd": "123456789012",
  "cmnd": "123456789",
  "hoTen": "Nguyễn <PERSON>n <PERSON>",
  "ngaySinh": "01/01/1990",
  "gioiTinh": "Nam",
  "diaChi": "123 Đường ABC, Quận 1, TP.HCM",
  "ngayCap": "01/01/2020"
}
```

## 3. <PERSON><PERSON><PERSON> dạng nhập thủ công (dấu ,)
```
123456789012,123456789,<PERSON><PERSON><PERSON><PERSON>n <PERSON>,01/01/1990,Nam,123 Đường ABC Quận 1 TP.HCM,01/01/2020
```

## 4. <PERSON><PERSON><PERSON> dạng từ máy quét QR (có thể có ký tự đặc biệt)
```
CCCD:123456789012|CMND:123456789|Họ tên:Nguy<PERSON>n Văn A|Ngày sinh:01/01/1990|Giới tính:Nam|Địa chỉ:123 Đường ABC Quận 1 TP.HCM|Ngày cấp:01/01/2020
``` 
import { FormFillData } from '@/common/types/scraper';

export type NotificationType = 'success' | 'warning' | 'error' | 'info';

export interface NotificationOptions {
    duration?: number;
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
    zIndex?: number;
}

export interface FormFillerOptions {
    includeHidden?: boolean;
    includeReadonly?: boolean;
    triggerEvents?: boolean;
    onProgress?: (filled: number, total: number) => void;
    onElementFilled?: (element: HTMLElement, value: string, identifier: string) => void;
    onElementNotFound?: (identifier: string) => void;
}

export interface FormFillerResult {
    filledCount: number;
    notFoundCount: number;
    totalElements: number;
    errors: string[];
}

export class FormFiller {
    private options: FormFillerOptions;

    constructor(options: FormFillerOptions = {}) {
        this.options = {
            includeHidden: false,
            includeReadonly: false,
            triggerEvents: true,
            ...options
        };
    }

    /**
     * Get all fillable input elements on the page (including Vue.js elements)
     */
    public getFormElements(): (HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement)[] {
        // Enhanced selectors to include Vue.js patterns
        const selectors = [
            'input',
            'textarea',
            'select',
            // Vue.js component selectors
            '.v-input input',
            '.v-text-field input',
            '.v-textarea textarea',
            '.v-select input',
            '.el-input input',
            '.el-textarea textarea',
            '.ant-input',
            '.ant-select',
            // Vue.js directive patterns
            '[v-model]',
            '[data-v-model]'
        ];

        const allInputs: (HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement)[] = [];
        const foundElements = new Set<Element>();

        selectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    if (
                        !foundElements.has(el) &&
                        (el instanceof HTMLInputElement ||
                            el instanceof HTMLTextAreaElement ||
                            el instanceof HTMLSelectElement)
                    ) {
                        foundElements.add(el);
                        allInputs.push(el);
                    }
                });
            } catch (e) {
                console.warn(`Error with selector ${selector}:`, e);
            }
        });

        return allInputs.filter(input => this.isElementFillable(input));
    }

    /**
     * Check if an element is fillable based on options
     */
    private isElementFillable(
        input: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    ): boolean {
        const isVisible = input.offsetParent !== null || input.type === 'hidden';
        const isEnabled = !input.disabled;

        // Check readonly only for input and textarea elements
        const isNotReadonly =
            this.options.includeReadonly ||
            !(input instanceof HTMLInputElement || input instanceof HTMLTextAreaElement) ||
            !input.readOnly;

        // Include hidden elements if option is set
        const shouldInclude = this.options.includeHidden || input.type !== 'hidden' || isVisible;

        return isVisible && isEnabled && isNotReadonly && shouldInclude;
    }

    /**
     * Find element by identifier (index or ID/name)
     */
    public findElement(
        identifier: string,
        visibleInputs: (HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement)[]
    ): HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | null {
        // Check if identifier is a number (index-based)
        const fieldIndex = parseInt(identifier);
        if (!isNaN(fieldIndex)) {
            if (fieldIndex >= 0 && fieldIndex < visibleInputs.length) {
                return visibleInputs[fieldIndex];
            }
            return null;
        }

        // Use ID/name-based approach (fallback)
        const strategies = [
            () => document.getElementById(identifier),
            () => document.querySelector(`[name="${identifier}"]`),
            () => document.querySelector(`[data-id="${identifier}"]`),
            () => document.querySelector(`input[id*="${identifier}"]`),
            () => document.querySelector(`input[name*="${identifier}"]`),
            () => document.querySelector(`textarea[id*="${identifier}"]`),
            () => document.querySelector(`select[id*="${identifier}"]`),
            () => document.querySelector(`[id$="_${identifier}"]`),
            () => document.querySelector(`[id^="${identifier}_"]`)
        ];

        for (const strategy of strategies) {
            const element = strategy() as
                | HTMLInputElement
                | HTMLTextAreaElement
                | HTMLSelectElement
                | null;
            if (element && this.isElementFillable(element)) {
                return element;
            }
        }

        return null;
    }

    /**
     * Fill a single element with value (enhanced for Vue.js)
     */
    public fillElement(
        element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement,
        value: string
    ): boolean {
        try {
            // Focus the element first (important for Vue.js)
            element.focus();

            // Set the value
            element.value = value;

            if (this.options.triggerEvents) {
                // Enhanced event triggering for Vue.js compatibility
                const events = ['focus', 'input', 'change', 'keydown', 'keyup', 'blur'];

                events.forEach(eventType => {
                    let event;
                    if (eventType === 'keydown' || eventType === 'keyup') {
                        event = new KeyboardEvent(eventType, {
                            bubbles: true,
                            cancelable: true,
                            key: 'Unidentified'
                        });
                    } else {
                        event = new Event(eventType, {
                            bubbles: true,
                            cancelable: true
                        });
                    }
                    element.dispatchEvent(event);
                });

                // Additional Vue.js specific events
                try {
                    // Trigger Vue.js v-model update
                    element.dispatchEvent(
                        new CustomEvent('vue:update', {
                            bubbles: true,
                            detail: { value }
                        })
                    );
                } catch (e) {
                    // Ignore if custom events are not supported
                }
            }

            return true;
        } catch (error) {
            console.error('Error filling element:', error);
            return false;
        }
    }

    /**
     * Fill form with data
     */
    public async fillForm(data: FormFillData): Promise<FormFillerResult> {
        const result: FormFillerResult = {
            filledCount: 0,
            notFoundCount: 0,
            totalElements: 0,
            errors: []
        };

        try {
            const visibleInputs = this.getFormElements();
            result.totalElements = visibleInputs.length;

            console.log(`Found ${visibleInputs.length} fillable input elements on the page`);

            for (const fieldData of data.data) {
                for (const [fieldIdentifier, value] of Object.entries(fieldData)) {
                    const element = this.findElement(fieldIdentifier, visibleInputs);

                    if (element) {
                        const success = this.fillElement(element, value);

                        if (success) {
                            result.filledCount++;

                            const elementInfo =
                                element.id || element.name || `${element.tagName}[${element.type}]`;
                            console.log(
                                `✅ Filled field ${fieldIdentifier} (${elementInfo}) with value: ${value}`
                            );

                            this.options.onElementFilled?.(element, value, fieldIdentifier);
                        } else {
                            result.notFoundCount++;
                            result.errors.push(`Failed to fill element ${fieldIdentifier}`);
                        }
                    } else {
                        result.notFoundCount++;
                        console.warn(
                            `❌ Could not find element with identifier: ${fieldIdentifier}`
                        );
                        this.options.onElementNotFound?.(fieldIdentifier);
                    }

                    // Report progress
                    this.options.onProgress?.(
                        result.filledCount,
                        result.filledCount + result.notFoundCount
                    );
                }
            }

            console.log(
                `Form filling completed! ✅ Filled: ${result.filledCount}, ❌ Not found: ${result.notFoundCount}`
            );
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            result.errors.push(errorMessage);
            console.error('Error filling form:', error);
        }

        return result;
    }

    /**
     * Get element info for debugging
     */
    public getElementInfo(
        element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    ): string {
        const tagName = element.tagName.toLowerCase();
        const type = element.type || 'text';
        const id = element.id || 'no-id';
        const name = element.name || 'no-name';
        const placeholder = (element as HTMLInputElement).placeholder || 'no-placeholder';
        const value = element.value || 'empty';

        return `${tagName}[${type}] - ID: ${id}, Name: ${name}, Placeholder: ${placeholder}, Value: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`;
    }

    /**
     * Get all elements with their indices for debugging
     */
    public getElementsWithIndices(): Array<{
        index: number;
        element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
        info: string;
    }> {
        const elements = this.getFormElements();
        return elements.map((element, index) => ({
            index,
            element,
            info: this.getElementInfo(element)
        }));
    }
}

// Utility functions for backward compatibility
export async function fillFormData(
    data: FormFillData,
    options?: FormFillerOptions
): Promise<FormFillerResult> {
    const filler = new FormFiller(options);
    return await filler.fillForm(data);
}

export function getFormElements(
    options?: FormFillerOptions
): (HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement)[] {
    const filler = new FormFiller(options);
    return filler.getFormElements();
}

export function getElementsWithIndices(options?: FormFillerOptions): Array<{
    index: number;
    element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
    info: string;
}> {
    const filler = new FormFiller(options);
    return filler.getElementsWithIndices();
}

/**
 * Row/Element Click Handler Interface
 */
export interface ClickableElement {
    element: Element;
    index: number;
    info: string;
    xpath: string;
    cssSelector: string;
}

export interface RowClickOptions {
    highlightOnHover?: boolean;
    highlightColor?: string;
    clickCallback?: (element: Element, index: number) => void;
    includeTableRows?: boolean;
    includeListItems?: boolean;
    includeGridItems?: boolean;
    includeCustomSelectors?: string[];
}

/**
 * Get all clickable rows/elements on the page (similar to Tampermonkey functionality)
 */
export function getClickableElements(options: RowClickOptions = {}): ClickableElement[] {
    const {
        includeTableRows = true,
        includeListItems = true,
        includeGridItems = true,
        includeCustomSelectors = []
    } = options;

    const selectors: string[] = [];

    if (includeTableRows) {
        selectors.push(
            'table tr',
            'tbody tr',
            'thead tr',
            'tfoot tr',
            '.table tr',
            '.data-table tr',
            '.grid-row',
            '[role="row"]'
        );
    }

    if (includeListItems) {
        selectors.push('ul li', 'ol li', '.list-item', '.menu-item', '[role="listitem"]');
    }

    if (includeGridItems) {
        selectors.push(
            '.grid-item',
            '.card',
            '.item',
            '.row',
            '[role="gridcell"]',
            '[role="option"]',
            '.editor-click'
        );
    }

    // Add custom selectors
    selectors.push(...includeCustomSelectors);

    const allElements: Element[] = [];
    const foundElements = new Set<Element>();

    selectors.forEach(selector => {
        try {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                if (!foundElements.has(el) && isElementClickable(el)) {
                    foundElements.add(el);
                    allElements.push(el);
                }
            });
        } catch (e) {
            console.warn(`Error with selector ${selector}:`, e);
        }
    });

    return allElements.map((element, index) => ({
        element,
        index,
        info: getClickableElementInfo(element),
        xpath: getElementXPath(element),
        cssSelector: getElementCSSSelector(element)
    }));
}

/**
 * Check if element is clickable
 */
function isElementClickable(element: Element): boolean {
    const style = window.getComputedStyle(element);
    return (
        style.display !== 'none' &&
        style.visibility !== 'hidden' &&
        element.offsetWidth > 0 &&
        element.offsetHeight > 0
    );
}

/**
 * Get detailed info about clickable element
 */
function getClickableElementInfo(element: Element): string {
    const tagName = element.tagName.toLowerCase();
    const id = element.id ? `#${element.id}` : '';
    const className = element.className
        ? `.${element.className.toString().split(' ').join('.')}`
        : '';
    const textContent = element.textContent?.trim().substring(0, 50) || '';

    return `${tagName}${id}${className} - "${textContent}"`;
}

/**
 * Get XPath for element
 */
function getElementXPath(element: Element): string {
    if (element.id) return `//*[@id="${element.id}"]`;

    const parts = [];
    let current: Element | null = element;

    while (current && current.nodeType === Node.ELEMENT_NODE) {
        let index = 1;
        let sibling = current.previousElementSibling;

        while (sibling) {
            if (sibling.tagName === current.tagName) index++;
            sibling = sibling.previousElementSibling;
        }

        const tagName = current.tagName.toLowerCase();
        parts.unshift(`${tagName}[${index}]`);
        current = current.parentElement;
    }

    return '/' + parts.join('/');
}

/**
 * Get CSS selector for element
 */
function getElementCSSSelector(element: Element): string {
    if (element.id) return `#${element.id}`;

    const path = [];
    let current: Element | null = element;

    while (current && current.nodeType === Node.ELEMENT_NODE) {
        let selector = current.tagName.toLowerCase();

        if (current.className) {
            selector += '.' + current.className.toString().split(' ').join('.');
        }

        path.unshift(selector);
        current = current.parentElement;
    }

    return path.join(' > ');
}

/**
 * Enable click functionality on rows/elements (Tampermonkey-style)
 */
export function enableRowClickHandler(options: RowClickOptions = {}): () => void {
    const { highlightOnHover = true, highlightColor = '#e3f2fd', clickCallback } = options;

    const clickableElements = getClickableElements(options);
    const originalStyles = new Map<Element, string>();

    // Add hover effects and click handlers
    clickableElements.forEach(({ element, index }) => {
        // Store original style
        originalStyles.set(element, element.getAttribute('style') || '');

        // Add cursor pointer
        (element as HTMLElement).style.cursor = 'pointer';

        // Initialize handlers object
        const handlers: any = {};

        if (highlightOnHover) {
            // Mouse enter handler
            const mouseEnterHandler = () => {
                (element as HTMLElement).style.backgroundColor = highlightColor;
                (element as HTMLElement).style.transition = 'background-color 0.2s ease';
            };

            // Mouse leave handler
            const mouseLeaveHandler = () => {
                (element as HTMLElement).style.backgroundColor = '';
            };

            element.addEventListener('mouseenter', mouseEnterHandler);
            element.addEventListener('mouseleave', mouseLeaveHandler);

            handlers.mouseEnterHandler = mouseEnterHandler;
            handlers.mouseLeaveHandler = mouseLeaveHandler;
        }

        // Click handler
        const clickHandler = (event: Event) => {
            event.preventDefault();
            event.stopPropagation();

            console.log(`Clicked on element [${index}]:`, getClickableElementInfo(element));

            if (clickCallback) {
                clickCallback(element, index);
            } else {
                // Default behavior: show element info
                showNotification(
                    `Clicked on element [${index}]: ${getClickableElementInfo(element)}`,
                    'info'
                );
            }
        };

        element.addEventListener('click', clickHandler);
        handlers.clickHandler = clickHandler;

        // Store handlers for cleanup
        (element as any)._clickHandlers = handlers;
    });

    // Return cleanup function
    return () => {
        clickableElements.forEach(({ element }) => {
            const handlers = (element as any)._clickHandlers;
            if (handlers) {
                // Remove hover handlers if they exist
                if (handlers.mouseEnterHandler) {
                    element.removeEventListener('mouseenter', handlers.mouseEnterHandler);
                }
                if (handlers.mouseLeaveHandler) {
                    element.removeEventListener('mouseleave', handlers.mouseLeaveHandler);
                }
                // Remove click handler
                if (handlers.clickHandler) {
                    element.removeEventListener('click', handlers.clickHandler);
                }
                delete (element as any)._clickHandlers;
            }

            // Restore original style
            const originalStyle = originalStyles.get(element);
            if (originalStyle !== undefined) {
                element.setAttribute('style', originalStyle);
            }
        });
    };
}

/**
 * Show notification to user
 */
export function showNotification(
    message: string,
    type: NotificationType = 'success',
    options: NotificationOptions = {}
): void {
    const { duration = 3000, position = 'top-right', zIndex = 2147483647 } = options;

    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        font-size: 14px;
        font-weight: 500;
        z-index: ${zIndex};
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        max-width: 300px;
        word-wrap: break-word;
        transition: all 0.3s ease-in-out;
        opacity: 0;
        pointer-events: auto;
        cursor: pointer;
    `;

    // Set position
    const [vPos, hPos] = position.split('-');
    if (vPos === 'top') {
        notification.style.top = '20px';
    } else {
        notification.style.bottom = '20px';
    }

    if (hPos === 'right') {
        notification.style.right = '20px';
        notification.style.transform = 'translateX(100%)';
    } else {
        notification.style.left = '20px';
        notification.style.transform = 'translateX(-100%)';
    }

    // Set background color based on type
    const colors = {
        success: '#4caf50',
        warning: '#ff9800',
        error: '#f44336',
        info: '#2196f3'
    };
    notification.style.background = colors[type];

    // Add icon based on type
    const icons = {
        success: '✅',
        warning: '⚠️',
        error: '❌',
        info: 'ℹ️'
    };

    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <span style="font-size: 16px;">${icons[type]}</span>
            <span>${message}</span>
            <span style="margin-left: auto; opacity: 0.7; font-size: 18px; cursor: pointer;" onclick="this.parentElement.parentElement.remove()">×</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
        notification.style.opacity = '1';
    }, 100);

    // Auto remove after duration
    const timeoutId = setTimeout(() => {
        removeNotification(notification, hPos);
    }, duration);

    // Click to dismiss
    notification.addEventListener('click', () => {
        clearTimeout(timeoutId);
        removeNotification(notification, hPos);
    });
}

/**
 * Remove notification with animation
 */
function removeNotification(notification: HTMLElement, horizontalPosition: string): void {
    const translateX = horizontalPosition === 'right' ? '100%' : '-100%';
    notification.style.transform = `translateX(${translateX})`;
    notification.style.opacity = '0';

    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

/**
 * Show success notification
 */
export function showSuccessNotification(message: string, options?: NotificationOptions): void {
    showNotification(message, 'success', options);
}

/**
 * Show warning notification
 */
export function showWarningNotification(message: string, options?: NotificationOptions): void {
    showNotification(message, 'warning', options);
}

/**
 * Show error notification
 */
export function showErrorNotification(message: string, options?: NotificationOptions): void {
    showNotification(message, 'error', options);
}

/**
 * Show info notification
 */
export function showInfoNotification(message: string, options?: NotificationOptions): void {
    showNotification(message, 'info', options);
}

name: ci

on: [push]

permissions:
    contents: read

env:
    NODE_VERSION: '24'

jobs:
    ci-ChromeExtension:
        name: Chrome extension CI
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: ${{ env.NODE_VERSION }}
                  cache: 'yarn'
                  cache-dependency-path: 'yarn.lock'

            - name: Cache ESLint cache
              uses: actions/cache@v4
              with:
                  path: js/.cache/.eslintcache
                  # Invalidate cache when any of the listed files changes
                  key: ${{ runner.os }}-eslint-${{ hashFiles('js/yarn.lock', 'js/eslint-config/src/*', 'js/eslint-config/index.js', 'js/eslint.config.js', 'js/.cache/.eslintcache') }}

            - name: Install dependencies
              run: yarn install --frozen-lockfile

            - name: Lint the code
              run: yarn run lint

            - name: Build the code
              run: yarn run build

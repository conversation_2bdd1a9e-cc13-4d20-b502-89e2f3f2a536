import React, { useState } from 'react';

import {
    Analytics,
    BusinessCenter,
    Business as BusinessIcon,
    CloudSync,
    Email as EmailIcon,
    Lock as PasswordIcon,
    Person as PersonIcon,
    Phone as PhoneIcon,
    Security,
    SmartToy,
    AccountCircle as UsernameIcon,
    Visibility,
    VisibilityOff
} from '@mui/icons-material';
import {
    Alert,
    Box,
    Button,
    Card,
    CardContent,
    Checkbox,
    CircularProgress,
    FormControl,
    FormControlLabel,
    IconButton,
    InputAdornment,
    InputLabel,
    Link,
    MenuItem,
    Radio,
    RadioGroup,
    Select,
    TextField,
    Typography
} from '@mui/material';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import { useAuth } from '@shared/hooks/useAuth';
import { useNavigate } from '@tanstack/react-router';

const SignUpScreen: React.FC = () => {
    const navigate = useNavigate();
    const { register, loading, error } = useAuth();

    const [formData, setFormData] = useState({
        productType: '',
        businessType: 'doanh-nghiep',
        companyName: '',
        taxCode: '',
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        username: '',
        password: '',
        retypePassword: '',
        agreeToTerms: false
    });

    const [formErrors, setFormErrors] = useState<{
        [key: string]: string;
    }>({});

    const [showPassword, setShowPassword] = useState(false);
    const [showRetypePassword, setShowRetypePassword] = useState(false);

    const validateForm = () => {
        const errors: { [key: string]: string } = {};

        if (!formData.productType) {
            errors.productType = 'Vui lòng chọn gói dịch vụ';
        }
        if (!formData.companyName) {
            errors.companyName = 'Tên công ty là bắt buộc';
        }
        if (!formData.taxCode) {
            errors.taxCode = 'Mã số thuế là bắt buộc';
        }
        if (!formData.firstName) {
            errors.firstName = '    đệm là bắt buộc';
        }
        if (!formData.lastName) {
            errors.lastName = 'Tên là bắt buộc';
        }
        if (!formData.email) {
            errors.email = 'Email là bắt buộc';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            errors.email = 'Email không hợp lệ';
        }
        if (!formData.phone) {
            errors.phone = 'Số điện thoại là bắt buộc';
        }
        if (!formData.username) {
            errors.username = 'Tên đăng nhập là bắt buộc';
        }
        if (!formData.password) {
            errors.password = 'Mật khẩu là bắt buộc';
        } else if (formData.password.length < 6) {
            errors.password = 'Mật khẩu phải có ít nhất 6 ký tự';
        }
        if (!formData.retypePassword) {
            errors.retypePassword = 'Nhập lại mật khẩu là bắt buộc';
        } else if (formData.password !== formData.retypePassword) {
            errors.retypePassword = 'Mật khẩu không khớp';
        }
        if (!formData.agreeToTerms) {
            errors.agreeToTerms = 'Bạn phải đồng ý với các điều khoản';
        }

        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        const result = await register(formData);

        if (result.success) {
            navigate({ to: '/dashboard' });
        }
    };

    const handleInputChange =
        (field: keyof typeof formData) => (e: React.ChangeEvent<HTMLInputElement>) => {
            const value = field === 'agreeToTerms' ? e.target.checked : e.target.value;
            setFormData(prev => ({ ...prev, [field]: value }));

            // Clear field error when user starts typing
            if (formErrors[field]) {
                setFormErrors(prev => {
                    const newErrors = { ...prev };
                    delete newErrors[field];
                    return newErrors;
                });
            }
        };

    const handleSelectChange = (field: keyof typeof formData) => (e: any) => {
        setFormData(prev => ({ ...prev, [field]: e.target.value }));
        if (formErrors[field]) {
            setFormErrors(prev => {
                const newErrors = { ...prev };
                delete newErrors[field];
                return newErrors;
            });
        }
    };

    return (
        <Box
            sx={{
                height: '100vh',
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                backgroundImage:
                    'url(/assets/icons/images/windows-11-dark-mode-blue-stock-official-3840x2160-5630.jpg)',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                alignItems: 'center',
                justifyContent: 'center',
                p: 3,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.3)',
                    zIndex: 0
                }
            }}
        >
            {/* Combined Cards Container */}
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', md: 'row' },
                    boxShadow: 4,
                    borderRadius: 3,
                    overflow: 'hidden',
                    maxWidth: 1000,
                    width: '100%',
                    height: 'calc(100vh - 48px)',
                    position: 'relative',
                    zIndex: 1
                }}
            >
                {/* Right Panel - Sign Up Form */}
                <Card
                    sx={{
                        flex: 1,
                        width: '100%',
                        borderRadius: 0,
                        boxShadow: 'none',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column'
                    }}
                >
                    <CardContent
                        sx={{
                            p: 4,
                            flex: 1,
                            overflow: 'auto',
                            display: 'flex',
                            flexDirection: 'column'
                        }}
                    >
                        <Box sx={{ textAlign: 'center', mb: 3 }}>
                            <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                                Đăng ký tài khoản dùng thử NTSOFT ID
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                Bạn đã sử dụng NTSOFT ID?{' '}
                                <Link
                                    component="button"
                                    type="button"
                                    underline="hover"
                                    onClick={() => navigate({ to: '/login' })}
                                    sx={{ cursor: 'pointer' }}
                                >
                                    Đăng nhập
                                </Link>
                            </Typography>
                        </Box>

                        <Box component="form" onSubmit={handleSubmit}>
                            {error && (
                                <Alert severity="error" sx={{ mb: 2 }}>
                                    {error}
                                </Alert>
                            )}

                            {/* Product Selection */}
                            <TextField
                                select
                                label="Chọn gói dịch vụ"
                                fullWidth
                                margin="normal"
                                value={formData.productType}
                                onChange={handleSelectChange('productType')}
                                error={!!formErrors.productType}
                                helperText={formErrors.productType}
                                placeholder="Nhấp để chọn"
                            >
                                <MenuItem value="ntsoft-standard">Gói cơ bản</MenuItem>
                                <MenuItem value="ntsoft-pro">Gói chuyên nghiệp</MenuItem>
                                <MenuItem value="ntsoft-enterprise">Gói doanh nghiệp</MenuItem>
                            </TextField>

                            {/* Business Type */}
                            <Box sx={{ mt: 2, mb: 2 }}>
                                <Typography variant="body2" sx={{ mb: 1 }}>
                                    Loại hình kinh doanh
                                </Typography>
                                <RadioGroup
                                    row
                                    value={formData.businessType}
                                    onChange={handleSelectChange('businessType')}
                                >
                                    <FormControlLabel
                                        value="doanh-nghiep"
                                        control={<Radio />}
                                        label="Doanh nghiệp"
                                    />
                                    <FormControlLabel
                                        value="ho-kinh-doanh"
                                        control={<Radio />}
                                        label="Hộ kinh doanh"
                                    />
                                </RadioGroup>
                            </Box>

                            {/* Company and Tax Code */}
                            <Box sx={{ display: 'flex', gap: 2 }}>
                                <TextField
                                    variant="standard"
                                    label="Công ty"
                                    fullWidth
                                    margin="normal"
                                    value={formData.companyName}
                                    onChange={handleInputChange('companyName')}
                                    error={!!formErrors.companyName}
                                    helperText={formErrors.companyName}
                                    placeholder="Tìm theo tên công ty hoặc mã số thuế"
                                    InputProps={{
                                        startAdornment: (
                                            <BusinessIcon sx={{ mr: 1, color: 'action.active' }} />
                                        )
                                    }}
                                />
                                <TextField
                                    variant="standard"
                                    label="Mã số thuế"
                                    fullWidth
                                    margin="normal"
                                    value={formData.taxCode}
                                    onChange={handleInputChange('taxCode')}
                                    error={!!formErrors.taxCode}
                                    helperText={formErrors.taxCode}
                                    placeholder="Nhập mã số thuế"
                                />
                            </Box>

                            {/* Name Fields */}
                            <Box sx={{ display: 'flex', gap: 2 }}>
                                <TextField
                                    variant="standard"
                                    label="Họ và tên đệm"
                                    fullWidth
                                    margin="normal"
                                    value={formData.firstName}
                                    onChange={handleInputChange('firstName')}
                                    error={!!formErrors.firstName}
                                    helperText={formErrors.firstName}
                                    placeholder="Nhập họ và tên đệm"
                                    slotProps={{
                                        input: {
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <AccountCircleIcon />
                                                </InputAdornment>
                                            )
                                        }
                                    }}
                                />
                                <TextField
                                    variant="standard"
                                    label="Tên"
                                    fullWidth
                                    margin="normal"
                                    value={formData.lastName}
                                    onChange={handleInputChange('lastName')}
                                    error={!!formErrors.lastName}
                                    helperText={formErrors.lastName}
                                    placeholder="Nhập tên của bạn"
                                    slotProps={{
                                        input: {
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <AccountCircleIcon />
                                                </InputAdornment>
                                            )
                                        }
                                    }}
                                />
                            </Box>

                            {/* Email and Phone */}
                            <Box sx={{ display: 'flex', gap: 2 }}>
                                <TextField
                                    variant="standard"
                                    label="Email"
                                    fullWidth
                                    margin="normal"
                                    type="email"
                                    value={formData.email}
                                    onChange={handleInputChange('email')}
                                    error={!!formErrors.email}
                                    helperText={formErrors.email}
                                    placeholder="Nhập email của bạn"
                                    InputProps={{
                                        startAdornment: (
                                            <EmailIcon sx={{ mr: 1, color: 'action.active' }} />
                                        )
                                    }}
                                />
                                <TextField
                                    variant="standard"
                                    label="Số điện thoại"
                                    fullWidth
                                    margin="normal"
                                    value={formData.phone}
                                    onChange={handleInputChange('phone')}
                                    error={!!formErrors.phone}
                                    helperText={formErrors.phone}
                                    placeholder="Nhập số điện thoại của bạn"
                                    InputProps={{
                                        startAdornment: (
                                            <PhoneIcon sx={{ mr: 1, color: 'action.active' }} />
                                        )
                                    }}
                                />
                            </Box>

                            {/* Username */}
                            <TextField
                                variant="standard"
                                label="Tên đăng nhập"
                                fullWidth
                                margin="normal"
                                value={formData.username}
                                onChange={handleInputChange('username')}
                                error={!!formErrors.username}
                                helperText={formErrors.username}
                                placeholder="Nhập tên đăng nhập"
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <UsernameIcon />
                                        </InputAdornment>
                                    )
                                }}
                            />

                            {/* Password */}
                            <TextField
                                variant="standard"
                                label="Mật khẩu"
                                fullWidth
                                margin="normal"
                                type={showPassword ? 'text' : 'password'}
                                value={formData.password}
                                onChange={handleInputChange('password')}
                                error={!!formErrors.password}
                                helperText={formErrors.password}
                                placeholder="Nhập mật khẩu"
                                slotProps={{
                                    input: {
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <PasswordIcon />
                                            </InputAdornment>
                                        ),
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                <IconButton
                                                    onClick={() => setShowPassword(!showPassword)}
                                                    edge="end"
                                                >
                                                    {showPassword ? (
                                                        <VisibilityOff />
                                                    ) : (
                                                        <Visibility />
                                                    )}
                                                </IconButton>
                                            </InputAdornment>
                                        )
                                    }
                                }}
                            />

                            {/* Retype Password */}
                            <TextField
                                variant="standard"
                                label="Nhập lại mật khẩu"
                                fullWidth
                                margin="normal"
                                type={showRetypePassword ? 'text' : 'password'}
                                value={formData.retypePassword}
                                onChange={handleInputChange('retypePassword')}
                                error={!!formErrors.retypePassword}
                                helperText={formErrors.retypePassword}
                                placeholder="Nhập lại mật khẩu"
                                slotProps={{
                                    input: {
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <PasswordIcon />
                                            </InputAdornment>
                                        ),
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                <IconButton
                                                    onClick={() =>
                                                        setShowRetypePassword(!showRetypePassword)
                                                    }
                                                    edge="end"
                                                >
                                                    {showRetypePassword ? (
                                                        <VisibilityOff />
                                                    ) : (
                                                        <Visibility />
                                                    )}
                                                </IconButton>
                                            </InputAdornment>
                                        )
                                    }
                                }}
                            />

                            {/* Additional Info Link */}
                            <Box sx={{ mt: 2, mb: 2 }}>
                                <Link href="#" underline="hover" variant="body2">
                                    Thêm mã nhân viên (Dành cho nhân viên NTSOFT đăng ký khách hàng)
                                </Link>
                            </Box>

                            {/* Terms Agreement */}
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={formData.agreeToTerms}
                                        onChange={handleInputChange('agreeToTerms')}
                                        color="primary"
                                    />
                                }
                                label={
                                    <Typography variant="body2">
                                        Tôi đồng ý với các điều khoản của{' '}
                                        <Link href="#" underline="hover">
                                            Chính sách quyền riêng tư
                                        </Link>
                                    </Typography>
                                }
                                sx={{ mt: 1 }}
                            />
                            {formErrors.agreeToTerms && (
                                <Typography
                                    variant="caption"
                                    color="error"
                                    display="block"
                                    sx={{ mt: 1 }}
                                >
                                    {formErrors.agreeToTerms}
                                </Typography>
                            )}

                            {/* Submit Button */}
                            <Button
                                type="submit"
                                fullWidth
                                variant="contained"
                                disabled={loading}
                                sx={{
                                    mt: 3,
                                    mb: 2,
                                    py: 1.5,
                                    fontWeight: 600,
                                    backgroundColor: '#9e9e9e',
                                    '&:hover': {
                                        backgroundColor: '#757575'
                                    }
                                }}
                            >
                                {loading ? (
                                    <CircularProgress size={24} color="inherit" />
                                ) : (
                                    'Đăng ký'
                                )}
                            </Button>
                        </Box>
                    </CardContent>
                </Card>
            </Box>
        </Box>
    );
};

export default SignUpScreen;

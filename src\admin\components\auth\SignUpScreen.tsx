import React, { useState } from 'react';

import bcrypt from 'bcryptjs';

import {
    Home as AddressIcon,
    Business as BusinessIcon,
    Email as EmailIcon,
    Badge as IDIcon,
    Lock as PasswordIcon,
    AccountCircle as PersonIcon,
    Phone as PhoneIcon,
    Visibility,
    VisibilityOff
} from '@mui/icons-material';
import {
    Alert,
    Box,
    Button,
    Card,
    CardContent,
    Checkbox,
    CircularProgress,
    FormControlLabel,
    IconButton,
    InputAdornment,
    Link,
    Radio,
    RadioGroup,
    TextField,
    Typography
} from '@mui/material';
import { useAuth } from '@shared/hooks/useAuth';
import { useNavigate } from '@tanstack/react-router';

const SignUpScreen: React.FC = () => {
    const navigate = useNavigate();
    const { register, loading, error } = useAuth();

    const [formData, setFormData] = useState({
        HoTen: '',
        GioiTinh: '',
        DiaChi: '',
        Email: '',
        SoDienThoai: '',
        IDNTSOFT: '',
        MaSoThue: '',
        PasswordHash: '',
        agreeToTerms: false
    });

    const [formErrors, setFormErrors] = useState<{
        [key: string]: string;
    }>({});

    const [showPassword, setShowPassword] = useState(false);

    // Generate IDNTSOFT automatically
    const generateIDNTSOFT = () => {
        const randomNumber = Math.floor(Math.random() * 10000000)
            .toString()
            .padStart(7, '0');
        return `NTS${randomNumber}`;
    };

    const validateForm = () => {
        const errors: { [key: string]: string } = {};

        if (!formData.HoTen) {
            errors.HoTen = 'Họ và tên là bắt buộc';
        }
        if (!formData.GioiTinh) {
            errors.GioiTinh = 'Giới tính là bắt buộc';
        }
        if (!formData.DiaChi) {
            errors.DiaChi = 'Địa chỉ là bắt buộc';
        }
        if (!formData.Email) {
            errors.Email = 'Email là bắt buộc';
        } else if (!/\S+@\S+\.\S+/.test(formData.Email)) {
            errors.Email = 'Email không hợp lệ';
        }
        if (!formData.SoDienThoai) {
            errors.SoDienThoai = 'Số điện thoại là bắt buộc';
        }
        if (!formData.IDNTSOFT) {
            errors.IDNTSOFT = 'IDNTSOFT là bắt buộc';
        } else if (!/^NTS\d{7}$/.test(formData.IDNTSOFT)) {
            errors.IDNTSOFT = 'IDNTSOFT phải có định dạng NTSxxxxxxx (7 chữ số)';
        }
        if (!formData.MaSoThue) {
            errors.MaSoThue = 'Mã số thuế là bắt buộc';
        }
        if (!formData.PasswordHash) {
            errors.PasswordHash = 'Mật khẩu là bắt buộc';
        } else if (formData.PasswordHash.length < 6) {
            errors.PasswordHash = 'Mật khẩu phải có ít nhất 6 ký tự';
        }
        if (!formData.agreeToTerms) {
            errors.agreeToTerms = 'Bạn phải đồng ý với các điều khoản';
        }

        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        try {
            // Hash the password with bcrypt
            const saltRounds = 10;
            const hashedPassword = await bcrypt.hash(formData.PasswordHash, saltRounds);

            // Map our form data to the expected RegisterCredentials format
            const registerData = {
                productType: 'ntsoft-standard', // Default value
                businessType: 'doanh-nghiep', // Default value
                companyName: formData.HoTen, // Use name as company name for now
                taxCode: formData.MaSoThue,
                firstName: formData.HoTen.split(' ')[0] || formData.HoTen,
                lastName: formData.HoTen.split(' ').slice(1).join(' ') || '',
                email: formData.Email,
                phone: formData.SoDienThoai,
                username: formData.IDNTSOFT,
                password: hashedPassword, // Use hashed password
                retypePassword: hashedPassword, // Same as password since we don't have retype field
                agreeToTerms: formData.agreeToTerms
            };

            const result = await register(registerData);

            if (result.success) {
                navigate({ to: '/dashboard' });
            }
        } catch (error) {
            console.error('Error hashing password:', error);
            // Handle error appropriately
        }
    };

    const handleInputChange =
        (field: keyof typeof formData) => (e: React.ChangeEvent<HTMLInputElement>) => {
            const value = field === 'agreeToTerms' ? e.target.checked : e.target.value;
            setFormData(prev => ({ ...prev, [field]: value }));

            // Clear field error when user starts typing
            if (formErrors[field]) {
                setFormErrors(prev => {
                    const newErrors = { ...prev };
                    delete newErrors[field];
                    return newErrors;
                });
            }
        };

    const handleSelectChange = (field: keyof typeof formData) => (e: any) => {
        setFormData(prev => ({ ...prev, [field]: e.target.value }));
        if (formErrors[field]) {
            setFormErrors(prev => {
                const newErrors = { ...prev };
                delete newErrors[field];
                return newErrors;
            });
        }
    };

    return (
        <Box
            sx={{
                height: '100vh',
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                backgroundImage:
                    'url(/assets/icons/images/windows-11-dark-mode-blue-stock-official-3840x2160-5630.jpg)',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                alignItems: 'center',
                justifyContent: 'center',
                p: 3,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.3)',
                    zIndex: 0
                }
            }}
        >
            {/* Combined Cards Container */}
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', md: 'row' },
                    boxShadow: 4,
                    borderRadius: 3,
                    overflow: 'hidden',
                    maxWidth: 1000,
                    width: '100%',
                    height: 'calc(100vh - 48px)',
                    position: 'relative',
                    zIndex: 1
                }}
            >
                {/* Right Panel - Sign Up Form */}
                <Card
                    sx={{
                        flex: 1,
                        width: '100%',
                        borderRadius: 0,
                        boxShadow: 'none',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column'
                    }}
                >
                    <CardContent
                        sx={{
                            p: 4,
                            flex: 1,
                            overflow: 'auto',
                            display: 'flex',
                            flexDirection: 'column'
                        }}
                    >
                        <Box sx={{ textAlign: 'center', mb: 3 }}>
                            <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                                Đăng ký tài khoản dùng thử NTSOFT ID
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                Bạn đã sử dụng NTSOFT ID?{' '}
                                <Link
                                    component="button"
                                    type="button"
                                    underline="hover"
                                    onClick={() => navigate({ to: '/login' })}
                                    sx={{ cursor: 'pointer' }}
                                >
                                    Đăng nhập
                                </Link>
                            </Typography>
                        </Box>

                        <Box component="form" onSubmit={handleSubmit}>
                            {error && (
                                <Alert severity="error" sx={{ mb: 2 }}>
                                    {error}
                                </Alert>
                            )}

                            {/* Họ và Tên */}
                            <Box sx={{ flex: 1 }}>
                                <TextField
                                    variant="standard"
                                    label="Họ và Tên"
                                    fullWidth
                                    margin="normal"
                                    value={formData.HoTen}
                                    onChange={handleInputChange('HoTen')}
                                    error={!!formErrors.HoTen}
                                    helperText={formErrors.HoTen}
                                    placeholder="Nhập họ và tên đầy đủ"
                                    slotProps={{
                                        input: {
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <PersonIcon />
                                                </InputAdornment>
                                            )
                                        }
                                    }}
                                />
                            </Box>

                            {/* Giới tính */}
                            <Box sx={{ flex: 1, mt: 2, mb: 2 }}>
                                <Typography variant="body2" sx={{ mb: 1 }}>
                                    Giới tính
                                </Typography>
                                <RadioGroup
                                    row
                                    value={formData.GioiTinh}
                                    onChange={handleSelectChange('GioiTinh')}
                                >
                                    <FormControlLabel value="Nam" control={<Radio />} label="Nam" />
                                    <FormControlLabel value="Nữ" control={<Radio />} label="Nữ" />
                                </RadioGroup>
                                {formErrors.GioiTinh && (
                                    <Typography
                                        variant="caption"
                                        color="error"
                                        sx={{ display: 'block', mt: 0.5 }}
                                    >
                                        {formErrors.GioiTinh}
                                    </Typography>
                                )}
                            </Box>

                            {/* Địa chỉ */}
                            <TextField
                                variant="standard"
                                label="Địa chỉ"
                                fullWidth
                                margin="normal"
                                value={formData.DiaChi}
                                onChange={handleInputChange('DiaChi')}
                                error={!!formErrors.DiaChi}
                                helperText={formErrors.DiaChi}
                                placeholder="Nhập địa chỉ đầy đủ"
                                slotProps={{
                                    input: {
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <AddressIcon />
                                            </InputAdornment>
                                        )
                                    }
                                }}
                            />

                            {/* Email and Phone */}
                            <Box sx={{ display: 'flex', gap: 2 }}>
                                <TextField
                                    variant="standard"
                                    label="Email"
                                    fullWidth
                                    margin="normal"
                                    type="email"
                                    value={formData.Email}
                                    onChange={handleInputChange('Email')}
                                    error={!!formErrors.Email}
                                    helperText={formErrors.Email}
                                    placeholder="Nhập email của bạn"
                                    slotProps={{
                                        input: {
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <EmailIcon />
                                                </InputAdornment>
                                            )
                                        }
                                    }}
                                />
                                <TextField
                                    variant="standard"
                                    label="Số điện thoại"
                                    fullWidth
                                    margin="normal"
                                    value={formData.SoDienThoai}
                                    onChange={handleInputChange('SoDienThoai')}
                                    error={!!formErrors.SoDienThoai}
                                    helperText={formErrors.SoDienThoai}
                                    placeholder="Nhập số điện thoại của bạn"
                                    slotProps={{
                                        input: {
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <PhoneIcon />
                                                </InputAdornment>
                                            )
                                        }
                                    }}
                                />
                            </Box>

                            {/* IDNTSOFT and Tax Code */}
                            <Box sx={{ display: 'flex', gap: 2 }}>
                                <TextField
                                    variant="standard"
                                    label="IDNTSOFT"
                                    fullWidth
                                    margin="normal"
                                    value={formData.IDNTSOFT}
                                    onChange={handleInputChange('IDNTSOFT')}
                                    error={!!formErrors.IDNTSOFT}
                                    helperText={formErrors.IDNTSOFT}
                                    placeholder="NTSxxxxxxx"
                                    slotProps={{
                                        input: {
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <IDIcon />
                                                </InputAdornment>
                                            ),
                                            endAdornment: (
                                                <InputAdornment position="end">
                                                    <Button
                                                        size="small"
                                                        onClick={() => {
                                                            const newID = generateIDNTSOFT();
                                                            setFormData(prev => ({
                                                                ...prev,
                                                                IDNTSOFT: newID
                                                            }));
                                                        }}
                                                        sx={{ minWidth: 'auto', p: 0.5 }}
                                                    >
                                                        Tạo
                                                    </Button>
                                                </InputAdornment>
                                            )
                                        }
                                    }}
                                />
                                <TextField
                                    variant="standard"
                                    label="Mã số thuế"
                                    fullWidth
                                    margin="normal"
                                    value={formData.MaSoThue}
                                    onChange={handleInputChange('MaSoThue')}
                                    error={!!formErrors.MaSoThue}
                                    helperText={formErrors.MaSoThue}
                                    placeholder="Nhập mã số thuế"
                                    slotProps={{
                                        input: {
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <BusinessIcon />
                                                </InputAdornment>
                                            )
                                        }
                                    }}
                                />
                            </Box>

                            {/* Password */}
                            <TextField
                                variant="standard"
                                label="Mật khẩu"
                                fullWidth
                                margin="normal"
                                type={showPassword ? 'text' : 'password'}
                                value={formData.PasswordHash}
                                onChange={handleInputChange('PasswordHash')}
                                error={!!formErrors.PasswordHash}
                                helperText={formErrors.PasswordHash}
                                placeholder="Nhập mật khẩu"
                                slotProps={{
                                    input: {
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <PasswordIcon />
                                            </InputAdornment>
                                        ),
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                <IconButton
                                                    onClick={() => setShowPassword(!showPassword)}
                                                    edge="end"
                                                >
                                                    {showPassword ? (
                                                        <VisibilityOff />
                                                    ) : (
                                                        <Visibility />
                                                    )}
                                                </IconButton>
                                            </InputAdornment>
                                        )
                                    }
                                }}
                            />

                            {/* Re-Password */}
                            <TextField
                                variant="standard"
                                label="Nhập lại mật khẩu"
                                fullWidth
                                margin="normal"
                                type={showPassword ? 'text' : 'password'}
                                value={formData.PasswordHash}
                                onChange={handleInputChange('PasswordHash')}
                                error={!!formErrors.PasswordHash}
                                helperText={formErrors.PasswordHash}
                                placeholder="Nhập lại mật khẩu"
                                slotProps={{
                                    input: {
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <PasswordIcon />
                                            </InputAdornment>
                                        ),
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                <IconButton
                                                    onClick={() => setShowPassword(!showPassword)}
                                                    edge="end"
                                                >
                                                    {showPassword ? (
                                                        <VisibilityOff />
                                                    ) : (
                                                        <Visibility />
                                                    )}
                                                </IconButton>
                                            </InputAdornment>
                                        )
                                    }
                                }}
                            />

                            {/* Additional Info Link */}
                            <Box sx={{ mt: 2, mb: 2 }}>
                                <Link href="#" underline="hover" variant="body2">
                                    Thêm mã nhân viên (Dành cho nhân viên NTSOFT đăng ký khách hàng)
                                </Link>
                            </Box>

                            {/* Terms Agreement */}
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={formData.agreeToTerms}
                                        onChange={handleInputChange('agreeToTerms')}
                                        color="primary"
                                    />
                                }
                                label={
                                    <Typography variant="body2">
                                        Tôi đồng ý với các điều khoản của{' '}
                                        <Link href="#" underline="hover">
                                            Chính sách quyền riêng tư
                                        </Link>
                                    </Typography>
                                }
                                sx={{ mt: 1 }}
                            />
                            {formErrors.agreeToTerms && (
                                <Typography
                                    variant="caption"
                                    color="error"
                                    display="block"
                                    sx={{ mt: 1 }}
                                >
                                    {formErrors.agreeToTerms}
                                </Typography>
                            )}

                            {/* Submit Button */}
                            <Button
                                type="submit"
                                fullWidth
                                variant="contained"
                                disabled={loading}
                                sx={{
                                    mt: 3,
                                    mb: 2,
                                    py: 1.5,
                                    fontWeight: 600,
                                    backgroundColor: '#9e9e9e',
                                    '&:hover': {
                                        backgroundColor: '#757575'
                                    }
                                }}
                            >
                                {loading ? (
                                    <CircularProgress size={24} color="inherit" />
                                ) : (
                                    'Đăng ký'
                                )}
                            </Button>
                        </Box>
                    </CardContent>
                </Card>
            </Box>
        </Box>
    );
};

export default SignUpScreen;

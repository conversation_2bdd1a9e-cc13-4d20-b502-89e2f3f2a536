{"manifest_version": 3, "name": "Chrome Extension with React & Vite", "version": "REPLACED-BY-VITE", "description": "A chrome extension boilerplate built with React 18 and Vite 5", "icons": {"34": "assets/icons/icon-34.png", "128": "assets/icons/icon-128.png"}, "action": {"default_title": "Chrome Extension React", "default_icon": "assets/icons/icon-34.png"}, "options_page": "src/admin/index.html", "content_scripts": [{"js": ["src/content/index.ts"], "matches": ["*://*/*"], "run_at": "document_end"}], "background": {"service_worker": "src/background/index.ts"}, "web_accessible_resources": [{"resources": ["assets/icons/*.png", "src/admin/index.html"], "matches": [], "extension_ids": []}, {"resources": ["src/injected/vue-injector.js", "src/content/styles/floating-ui.css"], "matches": ["*://*/*"]}], "permissions": ["storage", "tabs", "activeTab", "notifications"], "host_permissions": ["https://*/*", "http://*/*"]}
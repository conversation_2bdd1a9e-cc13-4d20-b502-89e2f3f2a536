import React, { useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useAuth } from '@shared/hooks/useAuth';

interface ProtectedRouteProps {
    children: React.ReactNode;
    requiredRole?: 'admin' | 'user';
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
    children, 
    requiredRole = 'user' 
}) => {
    const { isAuthenticated, user, loading } = useAuth();
    const navigate = useNavigate();

    useEffect(() => {
        if (!loading && !isAuthenticated) {
            navigate({ to: '/login' });
        } else if (!loading && isAuthenticated && user && requiredRole === 'admin' && user.role !== 'admin') {
            // User is authenticated but doesn't have admin role
            navigate({ to: '/login' });
        }
    }, [isAuthenticated, user, loading, navigate, requiredRole]);

    if (loading) {
        return (
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: '100vh',
                    gap: 2
                }}
            >
                <CircularProgress size={40} />
                <Typography variant="body1" color="text.secondary">
                    Loading...
                </Typography>
            </Box>
        );
    }

    if (!isAuthenticated || (requiredRole === 'admin' && user?.role !== 'admin')) {
        return null; // Will redirect in useEffect
    }

    return <>{children}</>;
};

export default ProtectedRoute;
